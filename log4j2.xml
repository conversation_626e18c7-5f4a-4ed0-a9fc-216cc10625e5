<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" shutdownHook="disable">
  <Loggers>
    <Root level="info">
      <AppenderRef ref="Console"/>
      <AppenderRef ref="File"/>
    </Root>
    <Logger name="io.netty.channel.DefaultChannelPipeline" level="info" />

   <Logger name="org.quartz.core.QuartzSchedulerThread" level="error" />
   <Logger name="org.quartz.core.QuartzScheduler" level="error" />
   
    <Logger name="TEMP" level="info" />
    <Logger name="CORE" level="error" />
    <Logger name="CORE_DB" level="error" />
    <Logger name="CORE_REMOTE" level="error" />
    <Logger name="CORE_MSG" level="error" />
    <Logger name="CORE_EFFECT" level="error" />
    <Logger name="CORE_CONN" level="error" />
    <Logger name="PLATFORM" level="error" />
    <Logger name="HUMAN" level="info" />
    <Logger name="GAME" level="debug" />
    <Logger name="COMMON" level="info" />
    <Logger name="STAGE_COMMON" level="info" />
    <Logger name="STAGE_MOVE" level="info" />
    <Logger name="FIGHT" level="info" />
    <Logger name="FARM" level="info" />
    <Logger name="RANK" level="info" />
    <Logger name="CAPTURE" level="info" />
    <Logger name="FRIEND" level="info" />
    <Logger name="CHAT" level="info" />
    <Logger name="MONSTER" level="info" />
    <Logger name="INFORM" level="info" />
    <Logger name="COUNTRY" level="info" />
    <Logger name="ITEM" level="info" />
    <Logger name="BUFF" level="info" />
    <Logger name="INSTANCE" level="info" />
    <Logger name="PLATFORM" level="info" />
    <Logger name="QUEST" level="info" />
    <Logger name="SKILL" level="info" />
    <Logger name="CROSS" level="info" />
    <Logger name="BATTLEFIELD" level="info" />
    <Logger name="CAR_PARK" level="info" />
    <Logger name="ACTIVITY" level="info" />
    <Logger name="FISH" level="debug" />
    <Logger name="GUILD" level="off" />
    <Logger name="MONITOR" level="info">
			<AppenderRef ref="MONITOR_File" />
		</Logger>
    <Logger name="RDF_SQL" level="info">
			<AppenderRef ref="RDFSQLLogAppender" />
		</Logger>

  </Loggers>


  <Appenders>
    <Console name="Console" target="SYSTEM_OUT">
      <!-- <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/> -->
      <PatternLayout pattern="%d{HH:mm:ss.SSS} %-5level (%file:%line) - %msg%n"/>

    </Console>

    <File name="MONITOR_File" fileName="./log4j2/monitor.log">
			<PatternLayout pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} (%F:%L) [%p][%c] %m%n" />
		</File>


    <RollingRandomAccessFile name="File" fileName="./log4j2/${sys:logFileName}/${sys:logFileName}.log" filePattern="./log4j2/${sys:logFileName}/${sys:logFileName}-%d{MM-dd-yyyy}-%i.log">
      <PatternLayout>


        <charset>UTF-8</charset>
        
        <Pattern>%d{yyyy-MM-dd HH:mm:ss,SSS} (%F:%L) [%p][%c] %m%n</Pattern>
      </PatternLayout>
      <Policies>
        <OnStartupTriggeringPolicy />
        <SizeBasedTriggeringPolicy size="100 M"/>
      </Policies>
      <DefaultRolloverStrategy max="20" fileIndex="max"/>

    </RollingRandomAccessFile>
    
    <RollingRandomAccessFile name="RDFSQLLogAppender" fileName="./log4j2/rdf_sql.log" filePattern="./log4j2/rdf_sql-%d{MM-dd-yyyy}-%i.log">
      <PatternLayout>
        <charset>UTF-8</charset>
        <Pattern>%m%n</Pattern>
      </PatternLayout>
      <Policies>
        <OnStartupTriggeringPolicy />
        <SizeBasedTriggeringPolicy size="100 M"/>
      </Policies>
      <DefaultRolloverStrategy max="20" fileIndex="max"/>
    </RollingRandomAccessFile>


  </Appenders>
</Configuration>
