package org.gof.core.connsrv;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import org.gof.core.Chunk;
import org.gof.core.support.ConnectionStatus;
import java.util.List;

@GofGenFile
public final class ConnectionProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_CORE_CONNSRV_CONNECTION_CHECKCONN = 1;
		public static final int ORG_GOF_CORE_CONNSRV_CONNECTION_CLOSE = 2;
		public static final int ORG_GOF_CORE_CONNSRV_CONNECTION_GETIPADDRESS = 3;
		public static final int ORG_GOF_CORE_CONNSRV_CONNECTION_GETMSGBUF = 4;
		public static final int ORG_GOF_CORE_CONNSRV_CONNECTION_INITMSGBUF_CALLPOINT = 5;
		public static final int ORG_GOF_CORE_CONNSRV_CONNECTION_REPLACEMSGBUF_CONNECTIONBUF = 6;
		public static final int ORG_GOF_CORE_CONNSRV_CONNECTION_SENDMSG_LIST_LIST = 7;
		public static final int ORG_GOF_CORE_CONNSRV_CONNECTION_SENDMSG_INT_CHUNK = 8;
		public static final int ORG_GOF_CORE_CONNSRV_CONNECTION_SENDMSGBUF = 9;
		public static final int ORG_GOF_CORE_CONNSRV_CONNECTION_SETSTATUS_INT = 10;
		public static final int ORG_GOF_CORE_CONNSRV_CONNECTION_UPDATESTATUS_STRING_STRING = 11;
		public static final int ORG_GOF_CORE_CONNSRV_CONNECTION_UPDATESTATUS_CONNECTIONSTATUS = 12;
	}

	private static final String SERV_ID = "";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private ConnectionProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		Connection serv = (Connection)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_CHECKCONN: {
				return (GofFunction0)serv::checkConn;
			}
			case EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_CLOSE: {
				return (GofFunction0)serv::close;
			}
			case EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_GETIPADDRESS: {
				return (GofFunction0)serv::getIpAddress;
			}
			case EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_GETMSGBUF: {
				return (GofFunction0)serv::getMsgBuf;
			}
			case EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_INITMSGBUF_CALLPOINT: {
				return (GofFunction1<CallPoint>)serv::initMsgBuf;
			}
			case EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_REPLACEMSGBUF_CONNECTIONBUF: {
				return (GofFunction1<ConnectionBuf>)serv::replaceMsgBuf;
			}
			case EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_SENDMSG_LIST_LIST: {
				return (GofFunction2<List, List>)serv::sendMsg;
			}
			case EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_SENDMSG_INT_CHUNK: {
				return (GofFunction2<Integer, Chunk>)serv::sendMsg;
			}
			case EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_SENDMSGBUF: {
				return (GofFunction0)serv::sendMsgBuf;
			}
			case EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_SETSTATUS_INT: {
				return (GofFunction1<Integer>)serv::setStatus;
			}
			case EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_UPDATESTATUS_STRING_STRING: {
				return (GofFunction2<String, String>)serv::updateStatus;
			}
			case EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_UPDATESTATUS_CONNECTIONSTATUS: {
				return (GofFunction1<ConnectionStatus>)serv::updateStatus;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static ConnectionProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static ConnectionProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static ConnectionProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static ConnectionProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static ConnectionProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static ConnectionProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static ConnectionProxy createInstance(String node, String port, Object serviceId) {
		ConnectionProxy inst = new ConnectionProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link Connection#checkConn()}*/
	public void checkConn() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_CHECKCONN,"ORG_GOF_CORE_CONNSRV_CONNECTION_CHECKCONN", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link Connection#close()}*/
	public void close() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_CLOSE,"ORG_GOF_CORE_CONNSRV_CONNECTION_CLOSE", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link Connection#getIpAddress()}*/
	public void getIpAddress() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_GETIPADDRESS,"ORG_GOF_CORE_CONNSRV_CONNECTION_GETIPADDRESS", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link Connection#getMsgBuf()}*/
	public void getMsgBuf() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_GETMSGBUF,"ORG_GOF_CORE_CONNSRV_CONNECTION_GETMSGBUF", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link Connection#initMsgBuf(CallPoint connPoint)}*/
	public void initMsgBuf(CallPoint connPoint) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_INITMSGBUF_CALLPOINT,"ORG_GOF_CORE_CONNSRV_CONNECTION_INITMSGBUF_CALLPOINT", new Object[] {connPoint});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link Connection#replaceMsgBuf(ConnectionBuf conBuf)}*/
	public void replaceMsgBuf(ConnectionBuf conBuf) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_REPLACEMSGBUF_CONNECTIONBUF,"ORG_GOF_CORE_CONNSRV_CONNECTION_REPLACEMSGBUF_CONNECTIONBUF", new Object[] {conBuf});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link Connection#sendMsg(List idList, List chunkList)}*/
	public void sendMsg(List idList, List chunkList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_SENDMSG_LIST_LIST,"ORG_GOF_CORE_CONNSRV_CONNECTION_SENDMSG_LIST_LIST", new Object[] {idList, chunkList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link Connection#sendMsg(int msgId, Chunk msgbuf)}*/
	public void sendMsg(int msgId, Chunk msgbuf) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_SENDMSG_INT_CHUNK,"ORG_GOF_CORE_CONNSRV_CONNECTION_SENDMSG_INT_CHUNK", new Object[] {msgId, msgbuf});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link Connection#sendMsgBuf()}*/
	public void sendMsgBuf() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_SENDMSGBUF,"ORG_GOF_CORE_CONNSRV_CONNECTION_SENDMSGBUF", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link Connection#setStatus(int status)}*/
	public void setStatus(int status) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_SETSTATUS_INT,"ORG_GOF_CORE_CONNSRV_CONNECTION_SETSTATUS_INT", new Object[] {status});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link Connection#updateStatus(String node, String port)}*/
	public void updateStatus(String node, String port) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_UPDATESTATUS_STRING_STRING,"ORG_GOF_CORE_CONNSRV_CONNECTION_UPDATESTATUS_STRING_STRING", new Object[] {node, port});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link Connection#updateStatus(ConnectionStatus status)}*/
	public void updateStatus(ConnectionStatus status) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_CORE_CONNSRV_CONNECTION_UPDATESTATUS_CONNECTIONSTATUS,"ORG_GOF_CORE_CONNSRV_CONNECTION_UPDATESTATUS_CONNECTIONSTATUS", new Object[] {status});
		if(immutableOnce) immutableOnce = false;
	}
}
