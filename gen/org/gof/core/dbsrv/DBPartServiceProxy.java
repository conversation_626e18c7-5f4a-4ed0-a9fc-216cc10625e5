package org.gof.core.dbsrv;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import org.gof.core.Chunk;
import java.util.List;
import org.gof.core.Record;

@GofGenFile
public final class DBPartServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_COUNTALL_BOOLEAN_STRING = 1;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_COUNTBY_BOOLEAN_STRING_OBJECTS = 2;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_COUNTBYQUERY_BOOLEAN_STRING_STRING_OBJECTS = 3;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LONG_BOOLEAN = 4;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LIST = 5;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LIST_BOOLEAN = 6;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LONG = 7;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETEALL_STRING = 8;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FIND_STRING_LIST = 9;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDBY_BOOLEAN_INT_INT_STRING_OBJECTS = 10;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDBY_BOOLEAN_STRING_OBJECTS = 11;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDBYQUERY_BOOLEAN_STRING_STRING_OBJECTS = 12;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDFIELDTABLE = 13;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FLUSH_STRING = 14;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FLUSHALL = 15;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GET_STRING_LONG = 16;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GETBY_BOOLEAN_STRING_OBJECTS = 17;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GETBYQUERY_BOOLEAN_STRING_STRING_OBJECTS = 18;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_RECORD = 19;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_LIST_BOOLEAN = 20;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_RECORD_BOOLEAN = 21;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_LIST = 22;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_QUERY_STRINGS_STRING_OBJECTS = 23;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_SQL_BOOLEAN_BOOLEAN_STRING_STRING_OBJECTS = 24;
		public static final int ORG_GOF_CORE_DBSRV_DBPARTSERVICE_UPDATE_STRING_LONG_CHUNK_BOOLEAN = 25;
	}

	private static final String SERV_ID = "";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private DBPartServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		DBPartService serv = (DBPartService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_COUNTALL_BOOLEAN_STRING: {
				return (GofFunction2<Boolean, String>)serv::countAll;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_COUNTBY_BOOLEAN_STRING_OBJECTS: {
				return (GofFunction3<Boolean, String, Object[]>)serv::countBy;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_COUNTBYQUERY_BOOLEAN_STRING_STRING_OBJECTS: {
				return (GofFunction4<Boolean, String, String, Object[]>)serv::countByQuery;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LONG_BOOLEAN: {
				return (GofFunction3<String, Long, Boolean>)serv::delete;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LIST: {
				return (GofFunction2<String, List>)serv::delete;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LIST_BOOLEAN: {
				return (GofFunction3<String, List, Boolean>)serv::delete;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LONG: {
				return (GofFunction2<String, Long>)serv::delete;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETEALL_STRING: {
				return (GofFunction1<String>)serv::deleteAll;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FIND_STRING_LIST: {
				return (GofFunction2<String, List>)serv::find;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDBY_BOOLEAN_INT_INT_STRING_OBJECTS: {
				return (GofFunction5<Boolean, Integer, Integer, String, Object[]>)serv::findBy;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDBY_BOOLEAN_STRING_OBJECTS: {
				return (GofFunction3<Boolean, String, Object[]>)serv::findBy;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDBYQUERY_BOOLEAN_STRING_STRING_OBJECTS: {
				return (GofFunction4<Boolean, String, String, Object[]>)serv::findByQuery;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDFIELDTABLE: {
				return (GofFunction0)serv::findFieldTable;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FLUSH_STRING: {
				return (GofFunction1<String>)serv::flush;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FLUSHALL: {
				return (GofFunction0)serv::flushAll;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GET_STRING_LONG: {
				return (GofFunction2<String, Long>)serv::get;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GETBY_BOOLEAN_STRING_OBJECTS: {
				return (GofFunction3<Boolean, String, Object[]>)serv::getBy;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GETBYQUERY_BOOLEAN_STRING_STRING_OBJECTS: {
				return (GofFunction4<Boolean, String, String, Object[]>)serv::getByQuery;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_RECORD: {
				return (GofFunction1<Record>)serv::insert;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_LIST_BOOLEAN: {
				return (GofFunction2<List, Boolean>)serv::insert;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_RECORD_BOOLEAN: {
				return (GofFunction2<Record, Boolean>)serv::insert;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_LIST: {
				return (GofFunction1<List>)serv::insert;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_QUERY_STRINGS_STRING_OBJECTS: {
				return (GofFunction3<String[], String, Object[]>)serv::query;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_SQL_BOOLEAN_BOOLEAN_STRING_STRING_OBJECTS: {
				return (GofFunction5<Boolean, Boolean, String, String, Object[]>)serv::sql;
			}
			case EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_UPDATE_STRING_LONG_CHUNK_BOOLEAN: {
				return (GofFunction4<String, Long, Chunk, Boolean>)serv::update;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static DBPartServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static DBPartServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static DBPartServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static DBPartServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static DBPartServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static DBPartServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static DBPartServiceProxy createInstance(String node, String port, Object serviceId) {
		DBPartServiceProxy inst = new DBPartServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link DBPartService#countAll(boolean flush, String tableName)}*/
	public void countAll(boolean flush, String tableName) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_COUNTALL_BOOLEAN_STRING,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_COUNTALL_BOOLEAN_STRING", new Object[] {flush, tableName});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#countBy(boolean flush, String tableName, Object... params)}*/
	public void countBy(boolean flush, String tableName, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_COUNTBY_BOOLEAN_STRING_OBJECTS,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_COUNTBY_BOOLEAN_STRING_OBJECTS", new Object[] {flush, tableName, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#countByQuery(boolean flush, String tableName, String whereAndOther, Object... params)}*/
	public void countByQuery(boolean flush, String tableName, String whereAndOther, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_COUNTBYQUERY_BOOLEAN_STRING_STRING_OBJECTS,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_COUNTBYQUERY_BOOLEAN_STRING_STRING_OBJECTS", new Object[] {flush, tableName, whereAndOther, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#delete(String tableName, long id, boolean needResult)}*/
	public void delete(String tableName, long id, boolean needResult) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LONG_BOOLEAN,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LONG_BOOLEAN", new Object[] {tableName, id, needResult});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link DBPartService#delete(String tableName, List ids)}*/
	public void delete(String tableName, List ids) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LIST,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LIST", new Object[] {tableName, ids});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link DBPartService#delete(String tableName, List ids, boolean needResult)}*/
	public void delete(String tableName, List ids, boolean needResult) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LIST_BOOLEAN,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LIST_BOOLEAN", new Object[] {tableName, ids, needResult});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#delete(String tableName, long id)}*/
	public void delete(String tableName, long id) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LONG,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETE_STRING_LONG", new Object[] {tableName, id});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#deleteAll(String tableName)}*/
	public void deleteAll(String tableName) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETEALL_STRING,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_DELETEALL_STRING", new Object[] {tableName});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link DBPartService#find(String tableName, List ids)}*/
	public void find(String tableName, List ids) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FIND_STRING_LIST,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FIND_STRING_LIST", new Object[] {tableName, ids});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#findBy(boolean flush, int firstResult, int maxResults, String tableName, Object... params)}*/
	public void findBy(boolean flush, int firstResult, int maxResults, String tableName, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDBY_BOOLEAN_INT_INT_STRING_OBJECTS,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDBY_BOOLEAN_INT_INT_STRING_OBJECTS", new Object[] {flush, firstResult, maxResults, tableName, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#findBy(boolean flush, String tableName, Object... params)}*/
	public void findBy(boolean flush, String tableName, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDBY_BOOLEAN_STRING_OBJECTS,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDBY_BOOLEAN_STRING_OBJECTS", new Object[] {flush, tableName, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#findByQuery(boolean flush, String tableName, String whereAndOther, Object... params)}*/
	public void findByQuery(boolean flush, String tableName, String whereAndOther, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDBYQUERY_BOOLEAN_STRING_STRING_OBJECTS,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDBYQUERY_BOOLEAN_STRING_STRING_OBJECTS", new Object[] {flush, tableName, whereAndOther, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#findFieldTable()}*/
	public void findFieldTable() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDFIELDTABLE,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDFIELDTABLE", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#flush(String tableName)}*/
	public void flush(String tableName) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FLUSH_STRING,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FLUSH_STRING", new Object[] {tableName});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#flushAll()}*/
	public void flushAll() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FLUSHALL,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FLUSHALL", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#get(String tableName, long id)}*/
	public void get(String tableName, long id) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GET_STRING_LONG,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GET_STRING_LONG", new Object[] {tableName, id});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#getBy(boolean flush, String tableName, Object... params)}*/
	public void getBy(boolean flush, String tableName, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GETBY_BOOLEAN_STRING_OBJECTS,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GETBY_BOOLEAN_STRING_OBJECTS", new Object[] {flush, tableName, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#getByQuery(boolean flush, String tableName, String whereAndOther, Object... params)}*/
	public void getByQuery(boolean flush, String tableName, String whereAndOther, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GETBYQUERY_BOOLEAN_STRING_STRING_OBJECTS,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GETBYQUERY_BOOLEAN_STRING_STRING_OBJECTS", new Object[] {flush, tableName, whereAndOther, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#insert(Record record)}*/
	public void insert(Record record) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_RECORD,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_RECORD", new Object[] {record});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link DBPartService#insert(List records, boolean needResult)}*/
	public void insert(List records, boolean needResult) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_LIST_BOOLEAN,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_LIST_BOOLEAN", new Object[] {records, needResult});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#insert(Record record, boolean needResult)}*/
	public void insert(Record record, boolean needResult) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_RECORD_BOOLEAN,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_RECORD_BOOLEAN", new Object[] {record, needResult});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link DBPartService#insert(List records)}*/
	public void insert(List records) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_LIST,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_INSERT_LIST", new Object[] {records});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#query(String[] flushTables, String sql, Object... params)}*/
	public void query(String[] flushTables, String sql, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_QUERY_STRINGS_STRING_OBJECTS,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_QUERY_STRINGS_STRING_OBJECTS", new Object[] {flushTables, sql, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#sql(boolean needResult, boolean flush, String tableName, String sql, Object... params)}*/
	public void sql(boolean needResult, boolean flush, String tableName, String sql, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_SQL_BOOLEAN_BOOLEAN_STRING_STRING_OBJECTS,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_SQL_BOOLEAN_BOOLEAN_STRING_STRING_OBJECTS", new Object[] {needResult, flush, tableName, sql, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link DBPartService#update(String tableName, long id, Chunk patch, boolean sync)}*/
	public void update(String tableName, long id, Chunk patch, boolean sync) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_CORE_DBSRV_DBPARTSERVICE_UPDATE_STRING_LONG_CHUNK_BOOLEAN,"ORG_GOF_CORE_DBSRV_DBPARTSERVICE_UPDATE_STRING_LONG_CHUNK_BOOLEAN", new Object[] {tableName, id, patch, sync});
		if(immutableOnce) immutableOnce = false;
	}
}
