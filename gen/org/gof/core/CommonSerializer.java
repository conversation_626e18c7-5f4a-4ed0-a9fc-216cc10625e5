package org.gof.core;
import org.gof.core.gen.GofGenFile;
import org.gof.core.InputStream;

@GofGenFile
public final class CommonSerializer{
	public static org.gof.core.interfaces.ISerilizable create(int id){
		switch(id){
			case 1299139699:
				return new org.gof.core.Call();
			case 957933789:
				return new org.gof.core.CallPoint();
			case -320466045:
				return new org.gof.core.CallReturn();
			case 1618842360:
				return new org.gof.core.Chunk();
			case -929362650:
				return new org.gof.core.Record();
			case 1771839492:
				return new org.gof.core.RecordTransient();
			case 517232605:
				return new org.gof.core.connsrv.ConnectionBuf();
			case 1618489055:
				return new org.gof.core.db.Field();
			case 1104655619:
				return new org.gof.core.db.FieldSet();
			case 717918255:
				return new org.gof.core.db.FieldTable();
			case -1592761974:
				return new org.gof.core.dbsrv.entity.IdAllot();
			case 1097158212:
				return new org.gof.core.support.ConnectionStatus();
			case 2079456164:
				return new org.gof.core.support.NodeInfo();
			case 948421433:
				return new org.gof.core.support.Param();
			case 1209550662:
				return new org.gof.core.support.RangeInt();
			case -1158544891:
				return new org.gof.core.support.RangeLong();
			case -493833132:
				return new org.gof.core.support.TickTimer();
		}
		return null;
	}
	public static void init(){
		InputStream.setCreateCommonFunc(CommonSerializer::create);
	}
}

