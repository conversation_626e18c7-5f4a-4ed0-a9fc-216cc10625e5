package org.gof.demo;
import org.gof.core.gen.GofGenFile;
import org.gof.core.InputStream;

@GofGenFile
public final class CommonSerializer{
	public static org.gof.core.interfaces.ISerilizable create(int id){
		switch(id){
			case 85794904:
				return new org.gof.demo.battlesrv.battle.BattleData();
			case 127938049:
				return new org.gof.demo.battlesrv.battle.PlayerData();
			case -**********:
				return new org.gof.demo.battlesrv.msgHandler.SkillData();
			case -**********:
				return new org.gof.demo.battlesrv.stageObj.UnitDataPersistance();
			case -**********:
				return new org.gof.demo.battlesrv.stageObj.UnitPropPlusMap();
			case -**********:
				return new org.gof.demo.battlesrv.support.Vector2D();
			case -**********:
				return new org.gof.demo.battlesrv.support.Vector3D();
			case -*********:
				return new org.gof.demo.seam.account.AccountObject();
			case -**********:
				return new org.gof.demo.support.PeriodChecker();
			case -*********:
				return new org.gof.demo.support.PeriodExecutor();
			case ********:
				return new org.gof.demo.worldsrv.activity.ActivityCrossRankSettleInfo();
			case -*********:
				return new org.gof.demo.worldsrv.activity.ActivityVo();
			case -*********:
				return new org.gof.demo.worldsrv.activity.data.ActivityControlData();
			case **********:
				return new org.gof.demo.worldsrv.activity.data.ActivityControlObjectData();
			case -*********:
				return new org.gof.demo.worldsrv.activity.data.ActivityServerData();
			case **********:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlActivityBossData();
			case *********:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlBreakEggData();
			case *********:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlCampData();
			case -*********:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlChallengeData();
			case -195585437:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlFestivalSearchData();
			case 71294018:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlLegionInvasionData();
			case -354903750:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlLoginData();
			case 749469667:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlLuckyBagData();
			case 1648979121:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlLuckyCatData();
			case -1031232436:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlMiniGameData();
			case -463975090:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlMountDrawData();
			case 1646297660:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlRefreshGiftData();
			case 771643114:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlTanabataFlowerData();
			case 1323405140:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlWarTokenData();
			case -106593882:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlWaterTreeData();
			case -1227906297:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlWeekCardData();
			case -1037248476:
				return new org.gof.demo.worldsrv.activity.data.controldata.ControlWhackMoleData();
			case 1022571010:
				return new org.gof.demo.worldsrv.activity.data.serverdata.ServerActDataBoss();
			case 1022587158:
				return new org.gof.demo.worldsrv.activity.data.serverdata.ServerActDataCamp();
			case -1598892877:
				return new org.gof.demo.worldsrv.activity.data.serverdata.ServerActDataCohesion();
			case 2142058442:
				return new org.gof.demo.worldsrv.activity.data.serverdata.ServerActDataGuildPay();
			case 1023109683:
				return new org.gof.demo.worldsrv.activity.data.serverdata.ServerActDataTree();
			case -1904071204:
				return new org.gof.demo.worldsrv.appearance.Personality();
			case -178637592:
				return new org.gof.demo.worldsrv.appearance.SkinVo();
			case 173186599:
				return new org.gof.demo.worldsrv.arena.ArenaRankedBrief();
			case -327594432:
				return new org.gof.demo.worldsrv.artifact.ArtifactData();
			case -1562610030:
				return new org.gof.demo.worldsrv.artifact.ArtifactGemVo();
			case -1387577240:
				return new org.gof.demo.worldsrv.backstage.BackStage();
			case 228787252:
				return new org.gof.demo.worldsrv.backstage.FightSoul();
			case 1321834337:
				return new org.gof.demo.worldsrv.backstage.HelpPartnerPlan();
			case 588833973:
				return new org.gof.demo.worldsrv.backstage.HiddenWeapon();
			case -1629051052:
				return new org.gof.demo.worldsrv.backstage.MoneyStatistics();
			case 50023414:
				return new org.gof.demo.worldsrv.backstage.PropBag();
			case -126997782:
				return new org.gof.demo.worldsrv.backstage.SoulBoneLoop();
			case 959956054:
				return new org.gof.demo.worldsrv.backstage.SoulRingLoop();
			case 399256066:
				return new org.gof.demo.worldsrv.backstage.WearEquip();
			case 1692219726:
				return new org.gof.demo.worldsrv.botHelper.DebugBuffVo();
			case 1955596836:
				return new org.gof.demo.worldsrv.botHelper.DebugHumanVo();
			case 2044808997:
				return new org.gof.demo.worldsrv.bridgeEntity.BridgeTest();
			case -298909520:
				return new org.gof.demo.worldsrv.bridgeEntity.GuildLeagueBattle();
			case 1194965200:
				return new org.gof.demo.worldsrv.bridgeEntity.GuildLeagueBattleWarmUp();
			case -201080674:
				return new org.gof.demo.worldsrv.bridgeEntity.GuildLeagueEnroll();
			case -1840361922:
				return new org.gof.demo.worldsrv.bridgeEntity.GuildLeagueEnrollWarmUp();
			case 581907356:
				return new org.gof.demo.worldsrv.bridgeEntity.GuildLeagueHistory();
			case -678753604:
				return new org.gof.demo.worldsrv.bridgeEntity.GuildLeagueHistoryWarmUp();
			case -1246410035:
				return new org.gof.demo.worldsrv.bridgeEntity.GuildLeagueMatch();
			case 1567963437:
				return new org.gof.demo.worldsrv.bridgeEntity.GuildLeagueMatchWarmUp();
			case 162339913:
				return new org.gof.demo.worldsrv.bridgeEntity.GuildLeagueRecord();
			case 839974313:
				return new org.gof.demo.worldsrv.bridgeEntity.GuildLeagueRecordWarmUp();
			case 190913243:
				return new org.gof.demo.worldsrv.bridgeEntity.GuildLeagueSeason();
			case 2073839291:
				return new org.gof.demo.worldsrv.bridgeEntity.GuildLeagueSeasonWarmUp();
			case 1676761817:
				return new org.gof.demo.worldsrv.bridgeEntity.KungFuRaceBattleReport();
			case -2127134940:
				return new org.gof.demo.worldsrv.bridgeEntity.KungFuRaceBet();
			case -208096380:
				return new org.gof.demo.worldsrv.bridgeEntity.KungFuRaceChampionInfo();
			case -418098862:
				return new org.gof.demo.worldsrv.bridgeEntity.KungFuRaceCompetition();
			case -1516137942:
				return new org.gof.demo.worldsrv.bridgeEntity.KungFuRaceTeam();
			case 637389668:
				return new org.gof.demo.worldsrv.bridgeEntity.KungFuRaceTeamApply();
			case -622569492:
				return new org.gof.demo.worldsrv.bridgeEntity.Warfare();
			case -139433264:
				return new org.gof.demo.worldsrv.callback.MemberCallback();
			case 732062763:
				return new org.gof.demo.worldsrv.captureSlave.SlaveVo();
			case 1425643430:
				return new org.gof.demo.worldsrv.carPark.BattleHumanVo();
			case 24398186:
				return new org.gof.demo.worldsrv.carPark.MountVo();
			case -284541464:
				return new org.gof.demo.worldsrv.carPark.ParkSkinVo();
			case -95407155:
				return new org.gof.demo.worldsrv.carPark.ParkSpaceVo();
			case 212627348:
				return new org.gof.demo.worldsrv.character.HumanDailyResetInfo();
			case -1659014413:
				return new org.gof.demo.worldsrv.character.HumanDataVO();
			case -1343463857:
				return new org.gof.demo.worldsrv.character.HumanObject();
			case 185284555:
				return new org.gof.demo.worldsrv.character.HumanObjectMirr();
			case -536556468:
				return new org.gof.demo.worldsrv.character.HumanOperationPersistance();
			case *********:
				return new org.gof.demo.worldsrv.character.HumanReset();
			case -**********:
				return new org.gof.demo.worldsrv.character.HumanStageInfoVO();
			case *********:
				return new org.gof.demo.worldsrv.character.part.RagePart();
			case -**********:
				return new org.gof.demo.worldsrv.character.part.SkillPassiveEffectVO();
			case **********:
				return new org.gof.demo.worldsrv.crossWar.CrossWarRewardVO();
			case -**********:
				return new org.gof.demo.worldsrv.entity.Account();
			case **********:
				return new org.gof.demo.worldsrv.entity.ActControlData();
			case -*********:
				return new org.gof.demo.worldsrv.entity.ActivityData();
			case -**********:
				return new org.gof.demo.worldsrv.entity.ActivityRoundData();
			case -**********:
				return new org.gof.demo.worldsrv.entity.Angel();
			case -**********:
				return new org.gof.demo.worldsrv.entity.Artifact();
			case **********:
				return new org.gof.demo.worldsrv.entity.Buff();
			case -*********:
				return new org.gof.demo.worldsrv.entity.Capture();
			case -*********:
				return new org.gof.demo.worldsrv.entity.CarPark();
			case -*********:
				return new org.gof.demo.worldsrv.entity.CarPark2();
			case -**********:
				return new org.gof.demo.worldsrv.entity.CarParkCross();
			case -**********:
				return new org.gof.demo.worldsrv.entity.CarParkPublic();
			case -**********:
				return new org.gof.demo.worldsrv.entity.Charm();
			case -********:
				return new org.gof.demo.worldsrv.entity.ClientInfo();
			case -1897323383:
				return new org.gof.demo.worldsrv.entity.CrossWarInfo();
			case -2326844:
				return new org.gof.demo.worldsrv.entity.Currency();
			case -941718423:
				return new org.gof.demo.worldsrv.entity.DoubleChapter();
			case -1790013859:
				return new org.gof.demo.worldsrv.entity.Equip();
			case 1189197929:
				return new org.gof.demo.worldsrv.entity.Farm();
			case -1789569815:
				return new org.gof.demo.worldsrv.entity.Farm2();
			case 1189197983:
				return new org.gof.demo.worldsrv.entity.Fate();
			case -1307016051:
				return new org.gof.demo.worldsrv.entity.FillMail();
			case -1852964564:
				return new org.gof.demo.worldsrv.entity.FlyHybridPartner();
			case 368251679:
				return new org.gof.demo.worldsrv.entity.FlyPet();
			case 1385924120:
				return new org.gof.demo.worldsrv.entity.FlyPetHumanData();
			case 373336593:
				return new org.gof.demo.worldsrv.entity.Friend();
			case 2081866748:
				return new org.gof.demo.worldsrv.entity.GroupGift();
			case -1788059104:
				return new org.gof.demo.worldsrv.entity.Guild();
			case 1667532078:
				return new org.gof.demo.worldsrv.entity.GuildApply();
			case 1716557185:
				return new org.gof.demo.worldsrv.entity.GuildHelp();
			case -1884285692:
				return new org.gof.demo.worldsrv.entity.GuildLog();
			case 487178458:
				return new org.gof.demo.worldsrv.entity.GuildMember();
			case -1064343926:
				return new org.gof.demo.worldsrv.entity.HomeFish();
			case -1787132070:
				return new org.gof.demo.worldsrv.entity.Human();
			case 433480728:
				return new org.gof.demo.worldsrv.entity.Human2();
			case 433480729:
				return new org.gof.demo.worldsrv.entity.Human3();
			case -1033012928:
				return new org.gof.demo.worldsrv.entity.HumanBrief();
			case -2116673379:
				return new org.gof.demo.worldsrv.entity.HumanDailyResetTime();
			case -2039324235:
				return new org.gof.demo.worldsrv.entity.HumanExtInfo();
			case -683248770:
				return new org.gof.demo.worldsrv.entity.HumanSubjoinInfo();
			case 1189305158:
				return new org.gof.demo.worldsrv.entity.Item();
			case 1404813854:
				return new org.gof.demo.worldsrv.entity.ItemLog();
			case 1189406186:
				return new org.gof.demo.worldsrv.entity.Mail();
			case 1189406279:
				return new org.gof.demo.worldsrv.entity.Mall();
			case 1189414022:
				return new org.gof.demo.worldsrv.entity.Mine();
			case -1782685114:
				return new org.gof.demo.worldsrv.entity.Mount();
			case -1499182875:
				return new org.gof.demo.worldsrv.entity.PayGift();
			case 644380911:
				return new org.gof.demo.worldsrv.entity.PayLog();
			case -526078819:
				return new org.gof.demo.worldsrv.entity.PocketLine();
			case 1987853918:
				return new org.gof.demo.worldsrv.entity.Privilege();
			case 1938067183:
				return new org.gof.demo.worldsrv.entity.Profession();
			case 330062686:
				return new org.gof.demo.worldsrv.entity.Redisdblog();
			case -1778374240:
				return new org.gof.demo.worldsrv.entity.Relic();
			case -1500824714:
				return new org.gof.demo.worldsrv.entity.RemoveHuman();
			case -848929287:
				return new org.gof.demo.worldsrv.entity.ServerGlobal();
			case 1322222249:
				return new org.gof.demo.worldsrv.entity.ServerNews();
			case -559061909:
				return new org.gof.demo.worldsrv.entity.TaskFinish();
			case -988196218:
				return new org.gof.demo.worldsrv.entity.TaskInfo();
			case 413774452:
				return new org.gof.demo.worldsrv.entity.UnitPropPlus();
			case 1189711934:
				return new org.gof.demo.worldsrv.entity.Wing();
			case -1658909542:
				return new org.gof.demo.worldsrv.entity.WorldBossInfo();
			case -1678150264:
				return new org.gof.demo.worldsrv.equip.EquipInfo();
			case 1788535936:
				return new org.gof.demo.worldsrv.fate.FateData();
			case 158286095:
				return new org.gof.demo.worldsrv.fate.FateVo();
			case -824696598:
				return new org.gof.demo.worldsrv.flyPet.FlyEggInfo();
			case 302656772:
				return new org.gof.demo.worldsrv.flyPet.FlyPetInfo();
			case -86466074:
				return new org.gof.demo.worldsrv.flyPet.hybrid.FlyHybridBaseInfo();
			case -602405570:
				return new org.gof.demo.worldsrv.flyPet.hybrid.FlyPetHybridInfo();
			case 630893917:
				return new org.gof.demo.worldsrv.friend.FavorabilityVo();
			case 634607888:
				return new org.gof.demo.worldsrv.guild.GuildGvgBattle();
			case -2070972144:
				return new org.gof.demo.worldsrv.guild.GuildGvgBattleCross();
			case -1286855142:
				return new org.gof.demo.worldsrv.guild.GuildRankInfo();
			case -2045032679:
				return new org.gof.demo.worldsrv.guild.HumanBriefVO();
			case -277837548:
				return new org.gof.demo.worldsrv.guild.league.LeagueVO();
			case 1954473379:
				return new org.gof.demo.worldsrv.guild.league.match.LeagueBattleGroup();
			case 129856318:
				return new org.gof.demo.worldsrv.guild.league.match.LeagueMatchGroup();
			case 142987835:
				return new org.gof.demo.worldsrv.guild.league.match.LeagueMatchPair();
			case -129222736:
				return new org.gof.demo.worldsrv.home.BuildVo();
			case -1642191817:
				return new org.gof.demo.worldsrv.home.FarmData();
			case -2124610938:
				return new org.gof.demo.worldsrv.home.FarmVo();
			case -1952963845:
				return new org.gof.demo.worldsrv.home.LandVo();
			case 1164586042:
				return new org.gof.demo.worldsrv.home.RobberVo();
			case 711521555:
				return new org.gof.demo.worldsrv.home.StatueTabVo();
			case -1256561420:
				return new org.gof.demo.worldsrv.home.StatueVo();
			case -863609481:
				return new org.gof.demo.worldsrv.home.StolenVo();
			case -51457090:
				return new org.gof.demo.worldsrv.human.HumanData();
			case 755816453:
				return new org.gof.demo.worldsrv.human.HumanGlobalInfo();
			case -1142464549:
				return new org.gof.demo.worldsrv.human.PlanVo();
			case 543885843:
				return new org.gof.demo.worldsrv.inform.ChatInfo();
			case -1116497660:
				return new org.gof.demo.worldsrv.inform.InformParamTime();
			case 1412887267:
				return new org.gof.demo.worldsrv.inform.MarqueeInfo();
			case -747543214:
				return new org.gof.demo.worldsrv.inform.RedInfo();
			case -220095317:
				return new org.gof.demo.worldsrv.instance.BridgeHalfwayVO();
			case 280238454:
				return new org.gof.demo.worldsrv.instance.BridgeInstanceAidVO();
			case -636238922:
				return new org.gof.demo.worldsrv.instance.BridgeInstanceConsumeVO();
			case -1267078246:
				return new org.gof.demo.worldsrv.instance.BridgeInstanceProduceVO();
			case 1704863011:
				return new org.gof.demo.worldsrv.instance.BridgeInstanceSettleVO();
			case -1127594784:
				return new org.gof.demo.worldsrv.item.ItemData();
			case 1989364254:
				return new org.gof.demo.worldsrv.kungFuRace.KungFuRaceRewardVO();
			case -814223769:
				return new org.gof.demo.worldsrv.match.ArenaHuman();
			case 1435064592:
				return new org.gof.demo.worldsrv.match.ArenaHumanObj();
			case 1839353842:
				return new org.gof.demo.worldsrv.match.MatchInfo();
			case -1801079586:
				return new org.gof.demo.worldsrv.match.MatchMember();
			case 1270383566:
				return new org.gof.demo.worldsrv.match.TeamRepHuman();
			case -1934603987:
				return new org.gof.demo.worldsrv.match.TeamRepHumanMirror();
			case 1863276376:
				return new org.gof.demo.worldsrv.monster.DeadMonsterInfo();
			case -1768728478:
				return new org.gof.demo.worldsrv.pet.PetData();
			case 1907063193:
				return new org.gof.demo.worldsrv.placingReward.PlacingRewardVo();
			case -399185017:
				return new org.gof.demo.worldsrv.pmSync.InformChatKafka();
			case 2015337203:
				return new org.gof.demo.worldsrv.produce.ProduceVo();
			case -1423623530:
				return new org.gof.demo.worldsrv.rank.HumanRankInfoVo();
			case -1907247516:
				return new org.gof.demo.worldsrv.rank.RankBrief();
			case -1446792828:
				return new org.gof.demo.worldsrv.rank.RankInfo();
			case -2071722226:
				return new org.gof.demo.worldsrv.relic.RelicTabVo();
			case -2056886353:
				return new org.gof.demo.worldsrv.relic.ReliceData();
			case -840461602:
				return new org.gof.demo.worldsrv.stage.StageHistory();
			case -1863547448:
				return new org.gof.demo.worldsrv.support.GetSetGrowthList();
			case 1402556280:
				return new org.gof.demo.worldsrv.support.ReasonResult();
			case 1200184352:
				return new org.gof.demo.worldsrv.task.TaskData();
			case 2116173185:
				return new org.gof.demo.worldsrv.task.type.TaskVO();
			case -1439505547:
				return new org.gof.demo.worldsrv.task.type.achievementData.AchievementTaskVO();
			case -927889473:
				return new org.gof.demo.worldsrv.task.type.activityData.ActivityTaskVO();
			case 791961961:
				return new org.gof.demo.worldsrv.task.type.dailydata.DailyTaskVO();
			case -1494253537:
				return new org.gof.demo.worldsrv.task.type.flyAchieveData.FlyAchieveTaskVO();
			case 1681030815:
				return new org.gof.demo.worldsrv.task.type.maindata.MainTaskVO();
			case -1949394908:
				return new org.gof.demo.worldsrv.team.TeamInfo();
			case -656361328:
				return new org.gof.demo.worldsrv.team.TeamMember();
			case 1517298564:
				return new org.gof.demo.worldsrv.treasureSkin.TreasureSkinInfo();
		}
		return null;
	}
	public static void init(){
		InputStream.setCreateCommonFunc(CommonSerializer::create);
	}
}

