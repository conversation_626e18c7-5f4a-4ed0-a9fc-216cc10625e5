package org.gof.demo;
import org.gof.core.support.observer.ObServer;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;

@GofGenFile
public final class ListenerInit{
	public static <K,P> void init(ObServer<K, P> ob){
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.battlesrv.msgHandler.SkillManager.class))::_on_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("257", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.distr.admin.AdminCenterManager.class))::onNodeRegister, 1);  
		ob.reg("258", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.distr.admin.AdminCenterManager.class))::onNodeUnregister, 1);  
		ob.reg("4098", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.distr.admin.AdminCenterManager.class))::onServerStartFinish, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.seam.account.AccountManager.class))::onHumanLogin, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.accumulatedRecharge.AccumulatedRechargeManager.class))::_on_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("13569", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityManager.class))::_listener_ACTIVITY_ADD_PROGRESS, 1);  
		ob.reg("16433", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityManager.class))::_listener_Finish_Task, 1);  
		ob.reg("20481", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityManager.class))::_listener_HUMAN_LOGIN, 1);  
		ob.reg("20487", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityManager.class))::_listener_HUMAN_RESET_WEEK_ZERO, 1);  
		ob.reg("13573", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityManager.class))::onActivityOpen, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityManager.class))::onLoginFinish, 1);  
		ob.reg("53250$RECEIVE_FLOWER", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityManager.class))::pocketLine_RECEIVE_FLOWER, 1);  
		ob.reg("53250$UPDATE_FRIEND", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityManager.class))::pocketLine_UPDATE_FRIEND, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.activity.ActivityManager.class))::unlock, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.angel.AngelManager.class))::unlock, 1);  
		ob.reg("16418", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.appearance.AppearanceManager.class))::_listener_HUMAN_ALL_LIFE_FIRST_LOGIN, 1);  
		ob.reg("16386", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.appearance.AppearanceManager.class))::onHumanLogin, 1);  
		ob.reg("20489", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaManager.class))::_HUMAN_RESET_EVERY_HOUR, 1);  
		ob.reg("20481", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaManager.class))::_listener_HUMAN_RESET_ZERO, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaManager.class))::_on_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("53250$ARENA_RANKED_GRADE", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaManager.class))::pocketLine_ARENA_RANKED_GRADE, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.arena.ArenaManager.class))::unlock, 1);  
		ob.reg("16466", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.artifact.ArtifactManager.class))::_listener_FUNCTION_OPEN_LOGIN, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.artifact.ArtifactManager.class))::unlock, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.back.BackManager.class))::_on_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.backLamp.BackLampManager.class))::_on_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.captureSlave.CaptureSlaveManager.class))::onFunctionOpen, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkManager.class))::onFunctionOpen, 1);  
		ob.reg("16389", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkManager.class))::onHumanLogout, 1);  
		ob.reg("53250$CAR_PARK_REWARD", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.carPark.CarParkManager.class))::pocketLine_REWARD, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.charm.CharmManager.class))::_on_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("16386", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.check.CheckManager.class))::onHumanLogin, 1);  
		ob.reg("4097", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.common.GameServiceManager.class))::onGameStartupBefore, 1);  
		ob.reg("16466", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarManager.class))::_listener_FUNCTION_OPEN_LOGIN, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarManager.class))::_on_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("16389", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarManager.class))::onHumanLogout, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.crossWar.CrossWarManager.class))::unlock, 1);  
		ob.reg("20487", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterManager.class))::onHumanResetWeekZero, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.doubleChapter.DoubleChapterManager.class))::unlock, 1);  
		ob.reg("16386", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipManager.class))::onHumanLogin, 1);  
		ob.reg("12289", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.equip.EquipManager.class))::onHumanUpgrade, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateManager.class))::_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("61441$11", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateManager.class))::onInstancePass, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.fate.FateManager.class))::unlock, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetManager.class))::listener_FUNCTION_OPEN, 1);  
		ob.reg("16466", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetManager.class))::listener_FUNCTION_OPEN_LOGIN, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetManager.class))::listener_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("13824", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetManager.class))::listener_PRIVILEGE_CARD_ACTIVE, 1);  
		ob.reg("13825", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetManager.class))::listener_PRIVILEGE_CARD_RENEW, 1);  
		ob.reg("53250$FLY_HYBRID_APPLY", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetManager.class))::pocketLine_FLY_HYBRID_APPLY, 1);  
		ob.reg("53250$FLY_HYBRID_PARTNER_UPDATE", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetManager.class))::pocketLine_FLY_HYBRID_PARTNER_UPDATE, 1);  
		ob.reg("53250$FLY_PET_BORROW_HYBRID_UPDATE", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.flyPet.FlyPetManager.class))::pocketLine_FLY_PET_BORROW_HYBRID_UPDATE, 1);  
		ob.reg("20482", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendManager.class))::_listener_HUMAN_RESET_FIVE, 1);  
		ob.reg("16386", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.friend.FriendManager.class))::humanLogin, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.fund.FundManager.class))::_listener_FUNCTION_OPEN, 1);  
		ob.reg("16466", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.fund.FundManager.class))::_listener_FUNCTION_OPEN_LOGIN, 1);  
		ob.reg("20505", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::_CRON_WEEK_345_hour_22, 1);  
		ob.reg("12291", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::_HUMAN_CHANGE_NAME, 1);  
		ob.reg("16389", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::_HUMAN_LOGOUT, 1);  
		ob.reg("20489", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::_HUMAN_RESET_EVERY_HOUR, 1);  
		ob.reg("49154", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::_ITEM_CHANGE_ADD, 1);  
		ob.reg("98305", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::_listener_GUILD_GVE_READY, 1);  
		ob.reg("16392", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::_listner_UPGRADE_FINISH, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::_on_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("16386", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::_on_HUMAN_LOGION, 1);  
		ob.reg("20481", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::_on_HUMAN_LOGION, 1);  
		ob.reg("53250$GUILD_GVE_ONLINE", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::pocketLine_GUILD_GVE_ONLINE, 1);  
		ob.reg("53250$GUILD_GVE_SETTLE", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::pocketLine_GUILD_GVE_SETTLE, 1);  
		ob.reg("53250$GUILD_LEAGUE_ADD_SCORE", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::pocketLine_GUILD_LEAGUE_ADD_SCORE, 1);  
		ob.reg("53250$GUILD_GVE_KILL", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.guild.GuildManager.class))::pocketLine_REWARD, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishManager.class))::onFunctionOpen, 1);  
		ob.reg("14080", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.home.Fish.HomeFishManager.class))::onHomeFishing, 1);  
		ob.reg("16386", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeManager.class))::onHumanLogin, 1);  
		ob.reg("53250$FARM_STEAL", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeManager.class))::pocketLine_FARM_STEAL, 1);  
		ob.reg("53250$FARM_STOLEN", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeManager.class))::pocketLine_FARM_STOLEN, 1);  
		ob.reg("53250$FARM_UPGRADE_FINISH", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeManager.class))::pocketLine_FARM_UPGRADE_FINISH, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.home.HomeManager.class))::unlock, 1);  
		ob.reg("393224", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanKillerManager.class))::onUnitBeKilled, 1);  
		ob.reg("36870", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanKillerManager.class))::onUnitBeKilled, 1);  
		ob.reg("20489", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::_HUMAN_RESET_EVERY_HOUR, 1);  
		ob.reg("20482", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::_HUMAN_RESET_FIVE, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::_listen_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("65537", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::_listener_HUMAN_COMBAT_CHANGE, 1);  
		ob.reg("1376259", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::_listener_PAY, 1);  
		ob.reg("16433", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::_on_FINISH_TASK, 1);  
		ob.reg("49153", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::_on_ITEM_CHANGE, 1);  
		ob.reg("49155", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::_on_ITEM_CHANGE_DEL, 1);  
		ob.reg("12304", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::_on_PRODUCE_MONEY_CHANGE, 1);  
		ob.reg("12289", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::_on_humanPayBack, 1);  
		ob.reg("53250$NOTICE_BALL", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::factionNotice, 1);  
		ob.reg("94209", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::gather_success, 1);  
		ob.reg("28675", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::loadDataBeginOne, 1);  
		ob.reg("28676", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::loadDataFinishOne, 1);  
		ob.reg("28674", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::loadHumanData, 1);  
		ob.reg("393221", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onBridgeLogout, 1);  
		ob.reg("16388", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onDayFirstLogin, 1);  
		ob.reg("16418", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onFirstLogin, 1);  
		ob.reg("4098", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onGameStartupBefore, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onHumanCombatChange, 1);  
		ob.reg("65537", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onHumanCombatChange, 1);  
		ob.reg("12290", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onHumanDie, 1);  
		ob.reg("393233", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onHumanDie, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onHumanExpChange, 1);  
		ob.reg("16386", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onHumanLogin, 1);  
		ob.reg("12291", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onHumanReName, 1);  
		ob.reg("24578", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onHumanStageEnter, 1);  
		ob.reg("393220", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onHumanStageEnterBefore, 1);  
		ob.reg("24577", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onHumanStageEnterBefore, 1);  
		ob.reg("49154", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onItemAdd, 1);  
		ob.reg("49156$1", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onItemChange, 1);  
		ob.reg("49155$1", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onItemDel, 1);  
		ob.reg("16389", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::onLogout, 1);  
		ob.reg("53250$GUILD_HELP", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::pocketLine_GUILD_HELP, 1);  
		ob.reg("53250$GUILD_JOIN", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::pocketLine_GUILD_UPDATE, 1);  
		ob.reg("53251", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::readyToSendInitataToClient, 1);  
		ob.reg("20482", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::resetFive, 1);  
		ob.reg("20480", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::resetFive, 1);  
		ob.reg("20481", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::resetZero, 1);  
		ob.reg("20480", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::resetZero, 1);  
		ob.reg("53250$SILENCE", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::silence, 1);  
		ob.reg("20481", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.human.HumanManager.class))::writeLoginLog, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("20489", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_HUMAN_RESET_EVERY_HOUR, 1);  
		ob.reg("61441", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_on_INSTANCE_PASS, 1);  
		ob.reg("61441$22", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_on_INSTANCE_PASS_ARTIFACTGEMCHAPTER_22, 1);  
		ob.reg("61441$1", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_on_INSTANCE_PASS_CHAPTER_1, 1);  
		ob.reg("61441$2", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_on_INSTANCE_PASS_COINCHAPTER_2, 1);  
		ob.reg("61441$28", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_on_INSTANCE_PASS_DARKTRIALCHAPTER_28, 1);  
		ob.reg("61441$29", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_on_INSTANCE_PASS_DARKTRIALCHAPTER_29, 1);  
		ob.reg("61441$30", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_on_INSTANCE_PASS_DARKTRIALCHAPTER_30, 1);  
		ob.reg("61441$3", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_on_INSTANCE_PASS_DIAMONDCHAPTER_3, 1);  
		ob.reg("61441$8", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_on_INSTANCE_PASS_LEGACYTEAMCHAPTER_8, 1);  
		ob.reg("61441$9", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_on_INSTANCE_PASS_MOUNTCHAPTER_9, 1);  
		ob.reg("61441$23", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_on_INSTANCE_PASS_SEVENTRIALCHAPTER_23, 1);  
		ob.reg("61441$13", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::_on_INSTANCE_PASS_WORLDBOSS_13, 1);  
		ob.reg("16386", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::onHumanLogin, 1);  
		ob.reg("16389", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::onHumanLogout, 1);  
		ob.reg("20481", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceManager.class))::onOpenServerDayChange, 1);  
		ob.reg("45068", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceStatisticsManager.class))::onHUMAN_HPADD, 1);  
		ob.reg("393232", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceStatisticsManager.class))::onHUMAN_HPADD, 1);  
		ob.reg("36870", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceStatisticsManager.class))::onUNIT_BE_KILLED, 1);  
		ob.reg("393225", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceStatisticsManager.class))::onUNIT_HPLOSS, 1);  
		ob.reg("36866", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.instance.InstanceStatisticsManager.class))::onUNIT_HPLOSS, 1);  
		ob.reg("1376257$addGoods", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::addGoods, 1);  
		ob.reg("1376257$blackMarketNotice", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::blackMarketNotice, 1);  
		ob.reg("1376257$bossEliteCreate", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::bossEliteCreate, 1);  
		ob.reg("1376257$innerPay", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::chargeGs, 1);  
		ob.reg("1376257$closeOperateActivity", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::closeOperateActivity, 1);  
		ob.reg("1376257$unSendFillMail", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::deleteFillMail, 1);  
		ob.reg("1376257$kick", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::kickHuman, 1);  
		ob.reg("1376257$online", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::online, 1);  
		ob.reg("1376257$openOperateActivity", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::openOperateActivity, 1);  
		ob.reg("1376257$subCmd", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::optionalExecute, 1);  
		ob.reg("1376257$pullData", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::pullData, 1);  
		ob.reg("1376257$pullDataCreate", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::pullDataCreate, 1);  
		ob.reg("1376257$recallNotice", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::recallNotice, 1);  
		ob.reg("1376257$pullHumanToCommon", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::resetHumanPort, 1);  
		ob.reg("1376257$seal", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::seal, 1);  
		ob.reg("1376257$sendFillMail", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::sendFillMail, 1);  
		ob.reg("1376257$sendGiftCode", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::sendGiftCode, 1);  
		ob.reg("1376257$sendGiftInfo", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::sendGiftInfo, 1);  
		ob.reg("1376257$sendMail", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::sendMail, 1);  
		ob.reg("1376257$sendNotice", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::sendNotice, 1);  
		ob.reg("1376257$setMaxOnline", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::setMaxOnline, 1);  
		ob.reg("1376257$test", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::test, 1);  
		ob.reg("1376257$test05", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::test05, 1);  
		ob.reg("1376257$test06", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::test06, 1);  
		ob.reg("1376257$unSeal", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::unSeal, 1);  
		ob.reg("1376257$updateAnnoucement", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_GM_Manager.class))::updateAnnoucement, 1);  
		ob.reg("1376258", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_PAY_Manager.class))::onPay, 1);  
		ob.reg("53250$GUILD_KICK", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_PAY_Manager.class))::pocketLine_GUILD_KICK, 1);  
		ob.reg("53250$PAY", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_PAY_Manager.class))::pocketLine_pay, 1);  
		ob.reg("53250$PAY_TW", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_PAY_Manager.class))::pocketLine_payTw, 1);  
		ob.reg("53250$PAY_FAKE", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_PAY_Manager.class))::pocketLine_pay_fake, 1);  
		ob.reg("53250$PAY_GIFT", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_PAY_Manager.class))::pocketLine_pay_gift, 1);  
		ob.reg("53250$PAY_GIFT_CREATE", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.intergration.PF_PAY_Manager.class))::pocketLine_pay_gift_create, 1);  
		ob.reg("20482", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.item.ItemManager.class))::_listener_HUMAN_RESET_FIVE, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.item.ItemManager.class))::_on_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("49154", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.item.ItemManager.class))::_on_ITEM_CHANGE_ADD, 1);  
		ob.reg("49160$0", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.item.ItemUseManager.class))::itemUseCostItem, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceManager.class))::_on_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceManager.class))::listener_FUNCTION_OPEN, 1);  
		ob.reg("53250$KUNG_FU_RACE_ADD_BET_COIN", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceManager.class))::pocketLine_KUNG_FU_RACE_ADD_BET_COIN, 1);  
		ob.reg("53250$KUNG_FU_RACE_UPDATE_TASK", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.kungFuRace.KungFuRaceManager.class))::pocketLine_KUNG_FU_RACE_UPDATE_TASK, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.mail.MailManager.class))::onHumanLoginFinish, 1);  
		ob.reg("1376259", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallManager.class))::_listener_PAY_NOTIFY, 1);  
		ob.reg("61441$1", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallManager.class))::_on_INSTANCE_PASS_CHAPTER_1, 1);  
		ob.reg("61441$28", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallManager.class))::_on_INSTANCE_PASS_DARKTRIALCHAPTER_28, 1);  
		ob.reg("61441$29", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallManager.class))::_on_INSTANCE_PASS_DARKTRIALCHAPTER_28, 1);  
		ob.reg("61441$30", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallManager.class))::_on_INSTANCE_PASS_DARKTRIALCHAPTER_28, 1);  
		ob.reg("13574", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallManager.class))::onActivityEndShow, 1);  
		ob.reg("13573", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallManager.class))::onActivityOpen, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallManager.class))::onHUMAN_LOGIN_FINISH, 1);  
		ob.reg("20481", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.mall.MallManager.class))::onHumanResetZero, 1);  
		ob.reg("16466", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.mount.MountManager.class))::_listener_FUNCTION_OPEN_LOGIN, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.mount.MountManager.class))::unlock, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.pet.PetManager.class))::_on_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("16386", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.pocketLine.PocketLineManager.class))::loadHumanDataPocketList, 1);  
		ob.reg("16386", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.privilege.PrivilegeManager.class))::onHumanLogin, 1);  
		ob.reg("20481", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.privilege.PrivilegeManager.class))::resetZero, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.relic.RelicManager.class))::onFunctionOpen, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.share.ShareManager.class))::_listener_FUNCTION_OPEN, 1);  
		ob.reg("16466", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.share.ShareManager.class))::_listener_FUNCTION_OPEN_LOGIN, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.share.ShareManager.class))::_listener_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("16432", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskManager.class))::_on_EQUIP_BOX_OPEN_NUM, 1);  
		ob.reg("16433", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskManager.class))::_on_FINISH_TASK, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskManager.class))::_on_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("16388", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskManager.class))::_on_HUMAN_LOGIN_FINISH_FIRST_TODAY, 1);  
		ob.reg("12289", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskManager.class))::_on_HUMAN_UPGRADE, 1);  
		ob.reg("61441", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskManager.class))::_on_INSTANCE_PASS, 1);  
		ob.reg("49154", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskManager.class))::_on_ITEM_CHANGE_ADD, 1);  
		ob.reg("12292", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskManager.class))::_on_LOOK_AD, 1);  
		ob.reg("77825", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskManager.class))::_on_LOTTERY_SELECT, 1);  
		ob.reg("16391", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskManager.class))::_on_PET_UPGRADE, 1);  
		ob.reg("57346", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskManager.class))::_on_SKILL_UPGRADE, 1);  
		ob.reg("16434", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskManager.class))::_on_TASK_CONDITION_TYPE, 1);  
		ob.reg("53250$ACHIEVEMENT_PROGRESS", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.task.TaskManager.class))::pocketLine_ACHIEVEMENT_PROGRESS, 1);  
		ob.reg("16387", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamManager.class))::_on_HUMAN_LOGIN_FINISH, 1);  
		ob.reg("12289", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamManager.class))::_on_HUMAN_UPGRADE, 1);  
		ob.reg("16389", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.team.TeamManager.class))::on_HUMAN_LOGOUT, 1);  
		ob.reg("16466", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.wing.WingManager.class))::_listener_FUNC_OPEN_LOGIN, 1);  
		ob.reg("16465", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.wing.WingManager.class))::unlock, 1);  
		ob.reg("20481", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.demo.worldsrv.zero.ZeroManager.class))::_HUMAN_RESET_ZERO, 1);  
	}
}

