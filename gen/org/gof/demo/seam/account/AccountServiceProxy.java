package org.gof.demo.seam.account;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import org.gof.core.support.ConnectionStatus;

@GofGenFile
public final class AccountServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_APPLYLOGIN_ACCOUNTOBJECT = 1;
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKMETHOD1_STRING = 2;
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKMETHOD2_STRING = 3;
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKUPMETHOD3_STRING = 4;
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKUPMETHOD4_STRING = 5;
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_CHECKGATENUM_INT = 6;
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_CONNCHECK_LONG = 7;
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_CONNCLOSED_LONG = 8;
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_FINISHLOGIN_STRING = 9;
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_MSGHANDLER_LONG_CONNECTIONSTATUS_BYTES = 10;
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_RECREATEACCOUNT_STRING_CALLPOINT = 11;
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_REMOVEACCOUNT_ACCOUNTOBJECT = 12;
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_SETHUMANONLINEFULL_INT = 13;
		public static final int ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_SETLOGINMAXONLINE_INT = 14;
	}

	private static final String SERV_ID = "";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private AccountServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings("unchecked")
	public Object getMethodFunction(Service service, int methodKey) {
		AccountService serv = (AccountService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_APPLYLOGIN_ACCOUNTOBJECT: {
				return (GofFunction1<AccountObject>)serv::applyLogin;
			}
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKMETHOD1_STRING: {
				return (GofFunction1<String>)serv::backMethod1;
			}
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKMETHOD2_STRING: {
				return (GofFunction1<String>)serv::backMethod2;
			}
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKUPMETHOD3_STRING: {
				return (GofFunction1<String>)serv::backupMethod3;
			}
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKUPMETHOD4_STRING: {
				return (GofFunction1<String>)serv::backupMethod4;
			}
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_CHECKGATENUM_INT: {
				return (GofFunction1<Integer>)serv::checkGateNum;
			}
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_CONNCHECK_LONG: {
				return (GofFunction1<Long>)serv::connCheck;
			}
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_CONNCLOSED_LONG: {
				return (GofFunction1<Long>)serv::connClosed;
			}
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_FINISHLOGIN_STRING: {
				return (GofFunction1<String>)serv::finishLogin;
			}
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_MSGHANDLER_LONG_CONNECTIONSTATUS_BYTES: {
				return (GofFunction3<Long, ConnectionStatus, byte[]>)serv::msgHandler;
			}
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_RECREATEACCOUNT_STRING_CALLPOINT: {
				return (GofFunction2<String, CallPoint>)serv::reCreateAccount;
			}
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_REMOVEACCOUNT_ACCOUNTOBJECT: {
				return (GofFunction1<AccountObject>)serv::removeAccount;
			}
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_SETHUMANONLINEFULL_INT: {
				return (GofFunction1<Integer>)serv::setHumanOnlineFull;
			}
			case EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_SETLOGINMAXONLINE_INT: {
				return (GofFunction1<Integer>)serv::setLoginMaxOnline;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static AccountServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static AccountServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static AccountServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static AccountServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static AccountServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static AccountServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static AccountServiceProxy createInstance(String node, String port, Object serviceId) {
		AccountServiceProxy inst = new AccountServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link AccountService#applyLogin(AccountObject accountObj)}*/
	public void applyLogin(AccountObject accountObj) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_APPLYLOGIN_ACCOUNTOBJECT,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_APPLYLOGIN_ACCOUNTOBJECT", new Object[] {accountObj});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link AccountService#backMethod1(String param)}*/
	public void backMethod1(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKMETHOD1_STRING,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKMETHOD1_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link AccountService#backMethod2(String param)}*/
	public void backMethod2(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKMETHOD2_STRING,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKMETHOD2_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link AccountService#backupMethod3(String param)}*/
	public void backupMethod3(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKUPMETHOD3_STRING,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKUPMETHOD3_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link AccountService#backupMethod4(String param)}*/
	public void backupMethod4(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKUPMETHOD4_STRING,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_BACKUPMETHOD4_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link AccountService#checkGateNum(int status)}*/
	public void checkGateNum(int status) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_CHECKGATENUM_INT,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_CHECKGATENUM_INT", new Object[] {status});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link AccountService#connCheck(long connId)}*/
	public void connCheck(long connId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_CONNCHECK_LONG,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_CONNCHECK_LONG", new Object[] {connId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link AccountService#connClosed(long connId)}*/
	public void connClosed(long connId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_CONNCLOSED_LONG,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_CONNCLOSED_LONG", new Object[] {connId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link AccountService#finishLogin(String account)}*/
	public void finishLogin(String account) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_FINISHLOGIN_STRING,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_FINISHLOGIN_STRING", new Object[] {account});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link AccountService#msgHandler(long connId, ConnectionStatus status, byte... msgbuf)}*/
	public void msgHandler(long connId, ConnectionStatus status, byte... msgbuf) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_MSGHANDLER_LONG_CONNECTIONSTATUS_BYTES,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_MSGHANDLER_LONG_CONNECTIONSTATUS_BYTES", new Object[] {connId, status, msgbuf});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link AccountService#reCreateAccount(String account, CallPoint cp)}*/
	public void reCreateAccount(String account, CallPoint cp) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_RECREATEACCOUNT_STRING_CALLPOINT,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_RECREATEACCOUNT_STRING_CALLPOINT", new Object[] {account, cp});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link AccountService#removeAccount(AccountObject obj)}*/
	public void removeAccount(AccountObject obj) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_REMOVEACCOUNT_ACCOUNTOBJECT,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_REMOVEACCOUNT_ACCOUNTOBJECT", new Object[] {obj});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link AccountService#setHumanOnlineFull(int onlineNum)}*/
	public void setHumanOnlineFull(int onlineNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_SETHUMANONLINEFULL_INT,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_SETHUMANONLINEFULL_INT", new Object[] {onlineNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link AccountService#setLoginMaxOnline(int maxOnline)}*/
	public void setLoginMaxOnline(int maxOnline) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_SETLOGINMAXONLINE_INT,"ORG_GOF_DEMO_SEAM_ACCOUNT_ACCOUNTSERVICE_SETLOGINMAXONLINE_INT", new Object[] {maxOnline});
		if(immutableOnce) immutableOnce = false;
	}
}
