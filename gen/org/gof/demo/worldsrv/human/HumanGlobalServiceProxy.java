package org.gof.demo.worldsrv.human;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import org.gof.demo.worldsrv.human.HumanGlobalInfo;
import java.util.Map;
import org.gof.demo.worldsrv.support.enumKey.HumanScopeKey;
import java.util.List;
import com.google.protobuf.Message;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.entity.FillMail;
import java.util.Set;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.inform.MarqueeInfo;
import org.gof.core.support.Param;
import org.gof.demo.worldsrv.entity.Guild;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.entity.PocketLine;

@GofGenFile
public final class HumanGlobalServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CHANGECONNPOINT_LONG_CALLPOINT = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ADDBALLINFO_INT_LONG_LONG_STRING_INT_LONG_LONG_STRING_LONG = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ADDPOCKETLINE_LONG_POCKETLINE = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_AREAENTER = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_AREAEXIT = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKMETHOD1_STRING = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKMETHOD2_STRING = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKUPMETHOD3_STRING = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKUPMETHOD4_STRING = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGEITEMUSE_LONG_INT_INT_INT = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGEITEMUSE_LONG_INT_INT_INT_MONEYITEMLOGKEY = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGELOGOUT_LONG = 12;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEENTER_LONG_LONG_OBJECTS = 13;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEHISTORYSET_LONG_LONG_INT_STRING_VECTOR2D = 14;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEINSET_LONG_BOOLEAN = 15;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEPOSUPDATE_LONG_VECTOR2D = 16;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGETOWORLDBACK_LONG_PARAM = 17;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CANCEL_LONG = 18;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CAPTUREADDENEMY_LONG_LONG = 19;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CHANNELCOUNTADD_STRING = 20;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CHECKANDCONSUME_LONG_MAP_MONEYITEMLOGKEY = 21;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CLOSEACTIVITY_INT_LIST = 22;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CREATEAREANHISTORY_LONG_LONG = 23;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CROSSWARINVADEEND_LONG_INT = 24;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CROSSWARKILLMONSTER_LONG_BOOLEAN = 25;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CROSSWARKILLPLAYER_LONG_BOOLEAN = 26;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_DENYAPPLY_LONG_STRING = 27;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ENDSHOWACTIVITY_INT_LIST = 28;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_EVENTFIRE_LIST_INT_PARAM = 29;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_FIREEVENT_INT = 30;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_FREESLAVE_LONG_LONG = 31;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETCROSSWARROBOTBRIEF = 32;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETHUMANBRIEF_LONG = 33;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETHUMANBRIEF2_LONG = 34;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFO_LONG = 35;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFO_LIST = 36;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFOS_LIST = 37;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFOSBYMAP_MAP = 38;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETONLINEHUMANLIST_LIST = 39;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETONLINEGUILDMEMBERS_LONG = 40;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETONLINEINFOLIST = 41;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETPBATTLEROLEMSG_LONG_BOOLEAN = 42;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETRECOMMENDUSERS_LONG_STRING_STRING_INT = 43;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETSYNCHUMANBRIEF_LIST = 44;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GMCALLHUMANSERVICEMETHOD_PARAM = 45;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GUILDGVEJOIN_LIST_INT_INT_LONG = 46;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GVESETTLE_SET_INT = 47;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_HELP_LONG_INT_INT = 48;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_HUMANLOGINGAMESTATE_LONG = 49;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ISINBLOCKLIST_LONG_LONG = 50;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ISLOGINED_STRING_STRING_INT = 51;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KICK_LONG_INT_OBJECTS = 52;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KICKALL = 53;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KICKCHECKROBOTHUMAN_LONG_STRING = 54;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KUNGFURACEADDBETCOIN_LONG_INT = 55;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KUNGFURACEBATTLENEWSCORE_LONG_INT = 56;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KUNGFURACERECYCLEBETCOIN = 57;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LEAVEGUILD_LONG_LONG = 58;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LEAVEGUILD_LIST_LONG = 59;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LEAVEREP_LONG = 60;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LOADSERVERID_INT = 61;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ONAUCTIONTIMEOUT_LONG_INT_INT = 62;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ONCROSSWAREND = 63;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ONTEAMMEMBERKICK_LONG_LONG = 64;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_OPENACTIVITY_INT_LIST = 65;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_OPERATION_INT_OBJECTS = 66;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_POCKLINEADD_LONG_STRING_STRING = 67;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_PRODUCEADD_LONG_INT_INT_MONEYITEMLOGKEY_OBJECTS = 68;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_PRODUCEADD_LONG_MAP_MONEYITEMLOGKEY_OBJECTS = 69;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_REGISTER_HUMANGLOBALINFO = 70;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_REGISTERTOBRIDGE_LONG_LONG_INT_STRING_STRING_STRING_INT_BOOLEAN = 71;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_RESERVEMETHOD3_STRING = 72;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_RESERVEMETHOD4_STRING = 73;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SEALACCOUNT_LONG_INT_LONG = 74;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDACTIVITYCROSSHUMANRANKSETTLE_LIST_INT_INT_INT = 75;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDALLSERVERINFORM_HUMANSCOPEKEY_LIST_INT_LIST = 76;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDARENACROSSSETTLE_MAP = 77;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDARENARANKEDSETTLE_MAP = 78;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDCROSSWARREWARD_MAP = 79;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDFACTIONMSGTO_MESSAGE = 80;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDFILLMAIL_FILLMAIL_BOOLEAN = 81;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGROUPGIFTREWARD_LIST_INT = 82;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGUILDMSGTO_LONG_MESSAGE = 83;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGUILDTREASUREBOXINFO_LIST = 84;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGVEKILLMAIL_LIST_INT_INT = 85;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDKUNGFURACEREWARD_MAP = 86;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDLEAGUESETTLESCORE_MAP = 87;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSG_LONG_MESSAGE = 88;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGCHATMESSAGE_PARAM = 89;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGPARAM_LIST_PARAM = 90;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGSERVERIDTOALL_INT_LIST_MESSAGE = 91;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGTO_LIST_MESSAGE = 92;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGTOALL_LIST_MESSAGE = 93;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSG_GUILD_SCHEDULE_S2C_LIST_INT = 94;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDSETTLEGUILDREWARD_MAP = 95;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDSETTLEHUMANREWARD_MAP = 96;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDSYSTEMMARQUEE_PARAM = 97;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SILENCE_LONG_INT_LONG_LONG = 98;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_STAGEIDMODIFY_LONG_LONG_INT_STRING_STRING_STRING_INT = 99;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SWITCHROLE_STRING_CALLPOINT = 100;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SWITCHTO_LONG_LONG_OBJECTS = 101;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SYNCINFOTIME_LONG = 102;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_TRIGGERMSGIDOPERATE_LONG_INT = 103;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE1_STRING = 104;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE2_OBJECTS = 105;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE3_PARAM = 106;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE4_STRING = 107;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATEBUILDPORPCALC_LONG = 108;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATECAMP_LONG_LONG = 109;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATEGUILDINFO_LIST_GUILD = 110;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATEGUILDPOSITION_LONG_INT = 111;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATELEVEL_LONG_INT = 112;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATENAME_LONG_STRING = 113;
		public static final int ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATESIGN_LONG_STRING = 114;
	}

	private static final String SERV_ID = "humanGlobal";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private HumanGlobalServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		HumanGlobalService serv = (HumanGlobalService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CHANGECONNPOINT_LONG_CALLPOINT: {
				return (GofFunction2<Long, CallPoint>)serv::ChangeConnPoint;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ADDBALLINFO_INT_LONG_LONG_STRING_INT_LONG_LONG_STRING_LONG: {
				return (GofFunction9<Integer, Long, Long, String, Integer, Long, Long, String, Long>)serv::addBallInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ADDPOCKETLINE_LONG_POCKETLINE: {
				return (GofFunction2<Long, PocketLine>)serv::addPocketLine;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_AREAENTER: {
				return (GofFunction0)serv::areaEnter;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_AREAEXIT: {
				return (GofFunction0)serv::areaExit;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKMETHOD1_STRING: {
				return (GofFunction1<String>)serv::backMethod1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKMETHOD2_STRING: {
				return (GofFunction1<String>)serv::backMethod2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKUPMETHOD3_STRING: {
				return (GofFunction1<String>)serv::backupMethod3;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKUPMETHOD4_STRING: {
				return (GofFunction1<String>)serv::backupMethod4;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGEITEMUSE_LONG_INT_INT_INT: {
				return (GofFunction4<Long, Integer, Integer, Integer>)serv::bridgeItemUse;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGEITEMUSE_LONG_INT_INT_INT_MONEYITEMLOGKEY: {
				return (GofFunction5<Long, Integer, Integer, Integer, MoneyItemLogKey>)serv::bridgeItemUse;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGELOGOUT_LONG: {
				return (GofFunction1<Long>)serv::bridgeLogout;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEENTER_LONG_LONG_OBJECTS: {
				return (GofFunction3<Long, Long, Object[]>)serv::bridgeStageEnter;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEHISTORYSET_LONG_LONG_INT_STRING_VECTOR2D: {
				return (GofFunction5<Long, Long, Integer, String, Vector2D>)serv::bridgeStageHistorySet;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEINSET_LONG_BOOLEAN: {
				return (GofFunction2<Long, Boolean>)serv::bridgeStageInSet;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEPOSUPDATE_LONG_VECTOR2D: {
				return (GofFunction2<Long, Vector2D>)serv::bridgeStagePosUpdate;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGETOWORLDBACK_LONG_PARAM: {
				return (GofFunction2<Long, Param>)serv::bridgeToWorldBack;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CANCEL_LONG: {
				return (GofFunction1<Long>)serv::cancel;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CAPTUREADDENEMY_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::captureAddEnemy;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CHANNELCOUNTADD_STRING: {
				return (GofFunction1<String>)serv::channelCountAdd;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CHECKANDCONSUME_LONG_MAP_MONEYITEMLOGKEY: {
				return (GofFunction3<Long, Map, MoneyItemLogKey>)serv::checkAndConsume;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CLOSEACTIVITY_INT_LIST: {
				return (GofFunction2<Integer, List>)serv::closeActivity;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CREATEAREANHISTORY_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::createAreanHistory;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CROSSWARINVADEEND_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::crossWarInvadeEnd;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CROSSWARKILLMONSTER_LONG_BOOLEAN: {
				return (GofFunction2<Long, Boolean>)serv::crossWarKillMonster;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CROSSWARKILLPLAYER_LONG_BOOLEAN: {
				return (GofFunction2<Long, Boolean>)serv::crossWarKillPlayer;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_DENYAPPLY_LONG_STRING: {
				return (GofFunction2<Long, String>)serv::denyApply;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ENDSHOWACTIVITY_INT_LIST: {
				return (GofFunction2<Integer, List>)serv::endShowActivity;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_EVENTFIRE_LIST_INT_PARAM: {
				return (GofFunction3<List, Integer, Param>)serv::eventFire;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_FIREEVENT_INT: {
				return (GofFunction1<Integer>)serv::fireEvent;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_FREESLAVE_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::freeSlave;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETCROSSWARROBOTBRIEF: {
				return (GofFunction0)serv::getCrossWarRobotBrief;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETHUMANBRIEF_LONG: {
				return (GofFunction1<Long>)serv::getHumanBrief;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETHUMANBRIEF2_LONG: {
				return (GofFunction1<Long>)serv::getHumanBrief2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFO_LONG: {
				return (GofFunction1<Long>)serv::getInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFO_LIST: {
				return (GofFunction1<List>)serv::getInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFOS_LIST: {
				return (GofFunction1<List>)serv::getInfos;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFOSBYMAP_MAP: {
				return (GofFunction1<Map>)serv::getInfosByMap;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETONLINEHUMANLIST_LIST: {
				return (GofFunction1<List>)serv::getOnLineHumanList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETONLINEGUILDMEMBERS_LONG: {
				return (GofFunction1<Long>)serv::getOnlineGuildMembers;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETONLINEINFOLIST: {
				return (GofFunction0)serv::getOnlineInfoList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETPBATTLEROLEMSG_LONG_BOOLEAN: {
				return (GofFunction2<Long, Boolean>)serv::getPBattleRoleMsg;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETRECOMMENDUSERS_LONG_STRING_STRING_INT: {
				return (GofFunction4<Long, String, String, Integer>)serv::getRecommendUsers;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETSYNCHUMANBRIEF_LIST: {
				return (GofFunction1<List>)serv::getSyncHumanBrief;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GMCALLHUMANSERVICEMETHOD_PARAM: {
				return (GofFunction1<Param>)serv::gmCallHumanServiceMethod;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GUILDGVEJOIN_LIST_INT_INT_LONG: {
				return (GofFunction4<List, Integer, Integer, Long>)serv::guildGveJoin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GVESETTLE_SET_INT: {
				return (GofFunction2<Set, Integer>)serv::gveSettle;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_HELP_LONG_INT_INT: {
				return (GofFunction3<Long, Integer, Integer>)serv::help;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_HUMANLOGINGAMESTATE_LONG: {
				return (GofFunction1<Long>)serv::humanLoginGameState;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ISINBLOCKLIST_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::isInBlockList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ISLOGINED_STRING_STRING_INT: {
				return (GofFunction3<String, String, Integer>)serv::isLogined;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KICK_LONG_INT_OBJECTS: {
				return (GofFunction3<Long, Integer, Object[]>)serv::kick;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KICKALL: {
				return (GofFunction0)serv::kickAll;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KICKCHECKROBOTHUMAN_LONG_STRING: {
				return (GofFunction2<Long, String>)serv::kickCheckRobotHuman;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KUNGFURACEADDBETCOIN_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::kungFuRaceAddBetCoin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KUNGFURACEBATTLENEWSCORE_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::kungFuRaceBattleNewScore;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KUNGFURACERECYCLEBETCOIN: {
				return (GofFunction0)serv::kungFuRaceRecycleBetCoin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LEAVEGUILD_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::leaveGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LEAVEGUILD_LIST_LONG: {
				return (GofFunction2<List, Long>)serv::leaveGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LEAVEREP_LONG: {
				return (GofFunction1<Long>)serv::leaveRep;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LOADSERVERID_INT: {
				return (GofFunction1<Integer>)serv::loadServerId;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ONAUCTIONTIMEOUT_LONG_INT_INT: {
				return (GofFunction3<Long, Integer, Integer>)serv::onAuctionTimeOut;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ONCROSSWAREND: {
				return (GofFunction0)serv::onCrossWarEnd;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ONTEAMMEMBERKICK_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::onTeamMemberKick;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_OPENACTIVITY_INT_LIST: {
				return (GofFunction2<Integer, List>)serv::openActivity;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_OPERATION_INT_OBJECTS: {
				return (GofFunction2<Integer, Object[]>)serv::operation;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_POCKLINEADD_LONG_STRING_STRING: {
				return (GofFunction3<Long, String, String>)serv::pockLineAdd;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_PRODUCEADD_LONG_INT_INT_MONEYITEMLOGKEY_OBJECTS: {
				return (GofFunction5<Long, Integer, Integer, MoneyItemLogKey, Object[]>)serv::produceAdd;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_PRODUCEADD_LONG_MAP_MONEYITEMLOGKEY_OBJECTS: {
				return (GofFunction4<Long, Map, MoneyItemLogKey, Object[]>)serv::produceAdd;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_REGISTER_HUMANGLOBALINFO: {
				return (GofFunction1<HumanGlobalInfo>)serv::register;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_REGISTERTOBRIDGE_LONG_LONG_INT_STRING_STRING_STRING_INT_BOOLEAN: {
				return (GofFunction8<Long, Long, Integer, String, String, String, Integer, Boolean>)serv::registerToBridge;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_RESERVEMETHOD3_STRING: {
				return (GofFunction1<String>)serv::reserveMethod3;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_RESERVEMETHOD4_STRING: {
				return (GofFunction1<String>)serv::reserveMethod4;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SEALACCOUNT_LONG_INT_LONG: {
				return (GofFunction3<Long, Integer, Long>)serv::sealAccount;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDACTIVITYCROSSHUMANRANKSETTLE_LIST_INT_INT_INT: {
				return (GofFunction4<List, Integer, Integer, Integer>)serv::sendActivityCrossHumanRankSettle;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDALLSERVERINFORM_HUMANSCOPEKEY_LIST_INT_LIST: {
				return (GofFunction4<HumanScopeKey, List, Integer, List>)serv::sendAllServerInform;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDARENACROSSSETTLE_MAP: {
				return (GofFunction1<Map>)serv::sendArenaCrossSettle;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDARENARANKEDSETTLE_MAP: {
				return (GofFunction1<Map>)serv::sendArenaRankedSettle;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDCROSSWARREWARD_MAP: {
				return (GofFunction1<Map>)serv::sendCrossWarReward;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDFACTIONMSGTO_MESSAGE: {
				return (GofFunction1<Message>)serv::sendFactionMsgTo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDFILLMAIL_FILLMAIL_BOOLEAN: {
				return (GofFunction2<FillMail, Boolean>)serv::sendFillMail;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGROUPGIFTREWARD_LIST_INT: {
				return (GofFunction2<List, Integer>)serv::sendGroupGiftReward;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGUILDMSGTO_LONG_MESSAGE: {
				return (GofFunction2<Long, Message>)serv::sendGuildMsgTo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGUILDTREASUREBOXINFO_LIST: {
				return (GofFunction1<List>)serv::sendGuildTreasureBoxInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGVEKILLMAIL_LIST_INT_INT: {
				return (GofFunction3<List, Integer, Integer>)serv::sendGveKillMail;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDKUNGFURACEREWARD_MAP: {
				return (GofFunction1<Map>)serv::sendKungFuRaceReward;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDLEAGUESETTLESCORE_MAP: {
				return (GofFunction1<Map>)serv::sendLeagueSettleScore;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSG_LONG_MESSAGE: {
				return (GofFunction2<Long, Message>)serv::sendMsg;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGCHATMESSAGE_PARAM: {
				return (GofFunction1<Param>)serv::sendMsgChatMessage;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGPARAM_LIST_PARAM: {
				return (GofFunction2<List, Param>)serv::sendMsgParam;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGSERVERIDTOALL_INT_LIST_MESSAGE: {
				return (GofFunction3<Integer, List, Message>)serv::sendMsgServerIdToAll;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGTO_LIST_MESSAGE: {
				return (GofFunction2<List, Message>)serv::sendMsgTo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGTOALL_LIST_MESSAGE: {
				return (GofFunction2<List, Message>)serv::sendMsgToAll;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSG_GUILD_SCHEDULE_S2C_LIST_INT: {
				return (GofFunction2<List, Integer>)serv::sendMsg_guild_schedule_s2c;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDSETTLEGUILDREWARD_MAP: {
				return (GofFunction1<Map>)serv::sendSettleGuildReward;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDSETTLEHUMANREWARD_MAP: {
				return (GofFunction1<Map>)serv::sendSettleHumanReward;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDSYSTEMMARQUEE_PARAM: {
				return (GofFunction1<Param>)serv::sendSystemMarquee;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SILENCE_LONG_INT_LONG_LONG: {
				return (GofFunction4<Long, Integer, Long, Long>)serv::silence;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_STAGEIDMODIFY_LONG_LONG_INT_STRING_STRING_STRING_INT: {
				return (GofFunction7<Long, Long, Integer, String, String, String, Integer>)serv::stageIdModify;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SWITCHROLE_STRING_CALLPOINT: {
				return (GofFunction2<String, CallPoint>)serv::switchRole;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SWITCHTO_LONG_LONG_OBJECTS: {
				return (GofFunction3<Long, Long, Object[]>)serv::switchTo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SYNCINFOTIME_LONG: {
				return (GofFunction1<Long>)serv::syncInfoTime;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_TRIGGERMSGIDOPERATE_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::triggerMsgIdOperate;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE1_STRING: {
				return (GofFunction1<String>)serv::update1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE2_OBJECTS: {
				return (GofFunction1<Object[]>)serv::update2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE3_PARAM: {
				return (GofFunction1<Param>)serv::update3;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE4_STRING: {
				return (GofFunction1<String>)serv::update4;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATEBUILDPORPCALC_LONG: {
				return (GofFunction1<Long>)serv::updateBuildPorpCalc;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATECAMP_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::updateCamp;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATEGUILDINFO_LIST_GUILD: {
				return (GofFunction2<List, Guild>)serv::updateGuildInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATEGUILDPOSITION_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::updateGuildPosition;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATELEVEL_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::updateLevel;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATENAME_LONG_STRING: {
				return (GofFunction2<Long, String>)serv::updateName;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATESIGN_LONG_STRING: {
				return (GofFunction2<Long, String>)serv::updateSign;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static HumanGlobalServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static HumanGlobalServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static HumanGlobalServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static HumanGlobalServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static HumanGlobalServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static HumanGlobalServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static HumanGlobalServiceProxy createInstance(String node, String port, Object serviceId) {
		HumanGlobalServiceProxy inst = new HumanGlobalServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link HumanGlobalService#ChangeConnPoint(Long id, CallPoint point)}*/
	public void ChangeConnPoint(Long id, CallPoint point) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CHANGECONNPOINT_LONG_CALLPOINT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CHANGECONNPOINT_LONG_CALLPOINT", new Object[] {id, point});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#addBallInfo(int type, long senderId, long targetId, String senderName, int senderLevel, long teamId, long factionId, String factionName, long senderCampType)}*/
	public void addBallInfo(int type, long senderId, long targetId, String senderName, int senderLevel, long teamId, long factionId, String factionName, long senderCampType) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ADDBALLINFO_INT_LONG_LONG_STRING_INT_LONG_LONG_STRING_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ADDBALLINFO_INT_LONG_LONG_STRING_INT_LONG_LONG_STRING_LONG", new Object[] {type, senderId, targetId, senderName, senderLevel, teamId, factionId, factionName, senderCampType});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#addPocketLine(long humanId, PocketLine pocketLine)}*/
	public void addPocketLine(long humanId, PocketLine pocketLine) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ADDPOCKETLINE_LONG_POCKETLINE,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ADDPOCKETLINE_LONG_POCKETLINE", new Object[] {humanId, pocketLine});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#areaEnter()}*/
	public void areaEnter() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_AREAENTER,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_AREAENTER", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#areaExit()}*/
	public void areaExit() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_AREAEXIT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_AREAEXIT", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#backMethod1(String param)}*/
	public void backMethod1(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKMETHOD1_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKMETHOD1_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#backMethod2(String param)}*/
	public void backMethod2(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKMETHOD2_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKMETHOD2_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#backupMethod3(String param)}*/
	public void backupMethod3(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKUPMETHOD3_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKUPMETHOD3_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#backupMethod4(String param)}*/
	public void backupMethod4(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKUPMETHOD4_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BACKUPMETHOD4_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#bridgeItemUse(long humanId, int packType, int itemSn, int num)}*/
	public void bridgeItemUse(long humanId, int packType, int itemSn, int num) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGEITEMUSE_LONG_INT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGEITEMUSE_LONG_INT_INT_INT", new Object[] {humanId, packType, itemSn, num});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#bridgeItemUse(long humanId, int packType, int itemSn, int num, MoneyItemLogKey log)}*/
	public void bridgeItemUse(long humanId, int packType, int itemSn, int num, MoneyItemLogKey log) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGEITEMUSE_LONG_INT_INT_INT_MONEYITEMLOGKEY,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGEITEMUSE_LONG_INT_INT_INT_MONEYITEMLOGKEY", new Object[] {humanId, packType, itemSn, num, log});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#bridgeLogout(long humanId)}*/
	public void bridgeLogout(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGELOGOUT_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGELOGOUT_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#bridgeStageEnter(long humanId, long stageId, Object... params)}*/
	public void bridgeStageEnter(long humanId, long stageId, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEENTER_LONG_LONG_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEENTER_LONG_LONG_OBJECTS", new Object[] {humanId, stageId, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#bridgeStageHistorySet(long humanId, long stageId, int stageSn, String stageType, Vector2D pos)}*/
	public void bridgeStageHistorySet(long humanId, long stageId, int stageSn, String stageType, Vector2D pos) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEHISTORYSET_LONG_LONG_INT_STRING_VECTOR2D,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEHISTORYSET_LONG_LONG_INT_STRING_VECTOR2D", new Object[] {humanId, stageId, stageSn, stageType, pos});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#bridgeStageInSet(long humanId, boolean stageIn)}*/
	public void bridgeStageInSet(long humanId, boolean stageIn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEINSET_LONG_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEINSET_LONG_BOOLEAN", new Object[] {humanId, stageIn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#bridgeStagePosUpdate(long humanId, Vector2D pos)}*/
	public void bridgeStagePosUpdate(long humanId, Vector2D pos) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEPOSUPDATE_LONG_VECTOR2D,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGESTAGEPOSUPDATE_LONG_VECTOR2D", new Object[] {humanId, pos});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#bridgeToWorldBack(long humanId, Param param)}*/
	public void bridgeToWorldBack(long humanId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGETOWORLDBACK_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_BRIDGETOWORLDBACK_LONG_PARAM", new Object[] {humanId, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#cancel(long humanId)}*/
	public void cancel(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CANCEL_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CANCEL_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#captureAddEnemy(long humanId, long enemyId)}*/
	public void captureAddEnemy(long humanId, long enemyId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CAPTUREADDENEMY_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CAPTUREADDENEMY_LONG_LONG", new Object[] {humanId, enemyId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#channelCountAdd(String channel)}*/
	public void channelCountAdd(String channel) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CHANNELCOUNTADD_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CHANNELCOUNTADD_STRING", new Object[] {channel});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#checkAndConsume(long humanId, Map costItemMap, MoneyItemLogKey logKey)}*/
	public void checkAndConsume(long humanId, Map costItemMap, MoneyItemLogKey logKey) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CHECKANDCONSUME_LONG_MAP_MONEYITEMLOGKEY,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CHECKANDCONSUME_LONG_MAP_MONEYITEMLOGKEY", new Object[] {humanId, costItemMap, logKey});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#closeActivity(int serverId, List activityIdList)}*/
	public void closeActivity(int serverId, List activityIdList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CLOSEACTIVITY_INT_LIST,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CLOSEACTIVITY_INT_LIST", new Object[] {serverId, activityIdList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#createAreanHistory(long humanId, long vid)}*/
	public void createAreanHistory(long humanId, long vid) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CREATEAREANHISTORY_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CREATEAREANHISTORY_LONG_LONG", new Object[] {humanId, vid});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#crossWarInvadeEnd(long humanId, int score)}*/
	public void crossWarInvadeEnd(long humanId, int score) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CROSSWARINVADEEND_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CROSSWARINVADEEND_LONG_INT", new Object[] {humanId, score});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#crossWarKillMonster(long humanId, boolean isInvade)}*/
	public void crossWarKillMonster(long humanId, boolean isInvade) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CROSSWARKILLMONSTER_LONG_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CROSSWARKILLMONSTER_LONG_BOOLEAN", new Object[] {humanId, isInvade});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#crossWarKillPlayer(long humanId, boolean isInvade)}*/
	public void crossWarKillPlayer(long humanId, boolean isInvade) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CROSSWARKILLPLAYER_LONG_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_CROSSWARKILLPLAYER_LONG_BOOLEAN", new Object[] {humanId, isInvade});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#denyApply(long humanId, String guildName)}*/
	public void denyApply(long humanId, String guildName) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_DENYAPPLY_LONG_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_DENYAPPLY_LONG_STRING", new Object[] {humanId, guildName});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#endShowActivity(int serverId, List endShowList)}*/
	public void endShowActivity(int serverId, List endShowList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ENDSHOWACTIVITY_INT_LIST,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ENDSHOWACTIVITY_INT_LIST", new Object[] {serverId, endShowList});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#eventFire(List humanIds, int eventKey, Param param)}*/
	public void eventFire(List humanIds, int eventKey, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_EVENTFIRE_LIST_INT_PARAM,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_EVENTFIRE_LIST_INT_PARAM", new Object[] {humanIds, eventKey, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#fireEvent(int key)}*/
	public void fireEvent(int key) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_FIREEVENT_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_FIREEVENT_INT", new Object[] {key});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#freeSlave(long humanId, long slaveId)}*/
	public void freeSlave(long humanId, long slaveId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_FREESLAVE_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_FREESLAVE_LONG_LONG", new Object[] {humanId, slaveId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#getCrossWarRobotBrief()}*/
	public void getCrossWarRobotBrief() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETCROSSWARROBOTBRIEF,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETCROSSWARROBOTBRIEF", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#getHumanBrief(long humanId)}*/
	public void getHumanBrief(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETHUMANBRIEF_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETHUMANBRIEF_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#getHumanBrief2(long humanId)}*/
	public void getHumanBrief2(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETHUMANBRIEF2_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETHUMANBRIEF2_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#getInfo(long humanId)}*/
	public void getInfo(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFO_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFO_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#getInfo(List humansIds)}*/
	public void getInfo(List humansIds) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFO_LIST,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFO_LIST", new Object[] {humansIds});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#getInfos(List humanIds)}*/
	public void getInfos(List humanIds) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFOS_LIST,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFOS_LIST", new Object[] {humanIds});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#getInfosByMap(Map humanIdsMap)}*/
	public void getInfosByMap(Map humanIdsMap) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFOSBYMAP_MAP,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETINFOSBYMAP_MAP", new Object[] {humanIdsMap});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#getOnLineHumanList(List humansIds)}*/
	public void getOnLineHumanList(List humansIds) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETONLINEHUMANLIST_LIST,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETONLINEHUMANLIST_LIST", new Object[] {humansIds});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#getOnlineGuildMembers(long guildId)}*/
	public void getOnlineGuildMembers(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETONLINEGUILDMEMBERS_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETONLINEGUILDMEMBERS_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#getOnlineInfoList()}*/
	public void getOnlineInfoList() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETONLINEINFOLIST,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETONLINEINFOLIST", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#getPBattleRoleMsg(long humanId, boolean containBattleData)}*/
	public void getPBattleRoleMsg(long humanId, boolean containBattleData) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETPBATTLEROLEMSG_LONG_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETPBATTLEROLEMSG_LONG_BOOLEAN", new Object[] {humanId, containBattleData});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#getRecommendUsers(long humanId, String friendListStr, String blackListStr, int combat)}*/
	public void getRecommendUsers(long humanId, String friendListStr, String blackListStr, int combat) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETRECOMMENDUSERS_LONG_STRING_STRING_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETRECOMMENDUSERS_LONG_STRING_STRING_INT", new Object[] {humanId, friendListStr, blackListStr, combat});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#getSyncHumanBrief(List humanIdList)}*/
	public void getSyncHumanBrief(List humanIdList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETSYNCHUMANBRIEF_LIST,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GETSYNCHUMANBRIEF_LIST", new Object[] {humanIdList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#gmCallHumanServiceMethod(Param param)}*/
	public void gmCallHumanServiceMethod(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GMCALLHUMANSERVICEMETHOD_PARAM,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GMCALLHUMANSERVICEMETHOD_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#guildGveJoin(List humanIdList, int gveSn, int mailSn, long time)}*/
	public void guildGveJoin(List humanIdList, int gveSn, int mailSn, long time) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GUILDGVEJOIN_LIST_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GUILDGVEJOIN_LIST_INT_INT_LONG", new Object[] {humanIdList, gveSn, mailSn, time});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#gveSettle(Set humanIdList, int gveSn)}*/
	public void gveSettle(Set humanIdList, int gveSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GVESETTLE_SET_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_GVESETTLE_SET_INT", new Object[] {humanIdList, gveSn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#help(long humanId, int type, int subType)}*/
	public void help(long humanId, int type, int subType) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_HELP_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_HELP_LONG_INT_INT", new Object[] {humanId, type, subType});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#humanLoginGameState(long humanId)}*/
	public void humanLoginGameState(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_HUMANLOGINGAMESTATE_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_HUMANLOGINGAMESTATE_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#isInBlockList(long humanId, long targetId)}*/
	public void isInBlockList(long humanId, long targetId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ISINBLOCKLIST_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ISINBLOCKLIST_LONG_LONG", new Object[] {humanId, targetId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#isLogined(String account, String channel, int serverId)}*/
	public void isLogined(String account, String channel, int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ISLOGINED_STRING_STRING_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ISLOGINED_STRING_STRING_INT", new Object[] {account, channel, serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#kick(long humanId, int serverDataSn, Object... params)}*/
	public void kick(long humanId, int serverDataSn, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KICK_LONG_INT_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KICK_LONG_INT_OBJECTS", new Object[] {humanId, serverDataSn, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#kickAll()}*/
	public void kickAll() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KICKALL,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KICKALL", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#kickCheckRobotHuman(long humanId, String reason)}*/
	public void kickCheckRobotHuman(long humanId, String reason) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KICKCHECKROBOTHUMAN_LONG_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KICKCHECKROBOTHUMAN_LONG_STRING", new Object[] {humanId, reason});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#kungFuRaceAddBetCoin(long humanId, int betNum)}*/
	public void kungFuRaceAddBetCoin(long humanId, int betNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KUNGFURACEADDBETCOIN_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KUNGFURACEADDBETCOIN_LONG_INT", new Object[] {humanId, betNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#kungFuRaceBattleNewScore(long humanId, int newScore)}*/
	public void kungFuRaceBattleNewScore(long humanId, int newScore) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KUNGFURACEBATTLENEWSCORE_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KUNGFURACEBATTLENEWSCORE_LONG_INT", new Object[] {humanId, newScore});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#kungFuRaceRecycleBetCoin()}*/
	public void kungFuRaceRecycleBetCoin() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KUNGFURACERECYCLEBETCOIN,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_KUNGFURACERECYCLEBETCOIN", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#leaveGuild(long humanId, long guildId)}*/
	public void leaveGuild(long humanId, long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LEAVEGUILD_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LEAVEGUILD_LONG_LONG", new Object[] {humanId, guildId});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#leaveGuild(List humanIdList, long guildId)}*/
	public void leaveGuild(List humanIdList, long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LEAVEGUILD_LIST_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LEAVEGUILD_LIST_LONG", new Object[] {humanIdList, guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#leaveRep(long humanId)}*/
	public void leaveRep(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LEAVEREP_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LEAVEREP_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#loadServerId(int serverId)}*/
	public void loadServerId(int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LOADSERVERID_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_LOADSERVERID_INT", new Object[] {serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#onAuctionTimeOut(long humanId, int sn, int num)}*/
	public void onAuctionTimeOut(long humanId, int sn, int num) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ONAUCTIONTIMEOUT_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ONAUCTIONTIMEOUT_LONG_INT_INT", new Object[] {humanId, sn, num});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#onCrossWarEnd()}*/
	public void onCrossWarEnd() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ONCROSSWAREND,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ONCROSSWAREND", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#onTeamMemberKick(long teamId, long memberId)}*/
	public void onTeamMemberKick(long teamId, long memberId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ONTEAMMEMBERKICK_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_ONTEAMMEMBERKICK_LONG_LONG", new Object[] {teamId, memberId});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#openActivity(int serverId, List activityVos)}*/
	public void openActivity(int serverId, List activityVos) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_OPENACTIVITY_INT_LIST,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_OPENACTIVITY_INT_LIST", new Object[] {serverId, activityVos});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#operation(int type, Object... objs)}*/
	public void operation(int type, Object... objs) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_OPERATION_INT_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_OPERATION_INT_OBJECTS", new Object[] {type, objs});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#pockLineAdd(long humanId, String pockLineTypeKey, String param)}*/
	public void pockLineAdd(long humanId, String pockLineTypeKey, String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_POCKLINEADD_LONG_STRING_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_POCKLINEADD_LONG_STRING_STRING", new Object[] {humanId, pockLineTypeKey, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#produceAdd(long humanId, int itemSn, int itemNum, MoneyItemLogKey log, Object... obj)}*/
	public void produceAdd(long humanId, int itemSn, int itemNum, MoneyItemLogKey log, Object... obj) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_PRODUCEADD_LONG_INT_INT_MONEYITEMLOGKEY_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_PRODUCEADD_LONG_INT_INT_MONEYITEMLOGKEY_OBJECTS", new Object[] {humanId, itemSn, itemNum, log, obj});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#produceAdd(long humanId, Map itemNumMap, MoneyItemLogKey log, Object... obj)}*/
	public void produceAdd(long humanId, Map itemNumMap, MoneyItemLogKey log, Object... obj) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_PRODUCEADD_LONG_MAP_MONEYITEMLOGKEY_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_PRODUCEADD_LONG_MAP_MONEYITEMLOGKEY_OBJECTS", new Object[] {humanId, itemNumMap, log, obj});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#register(HumanGlobalInfo status)}*/
	public void register(HumanGlobalInfo status) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_REGISTER_HUMANGLOBALINFO,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_REGISTER_HUMANGLOBALINFO", new Object[] {status});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#registerToBridge(long humanId, long stageIdNew, int stageSn, String stageName, String nodeId, String portId, int lineNum, boolean isLeague)}*/
	public void registerToBridge(long humanId, long stageIdNew, int stageSn, String stageName, String nodeId, String portId, int lineNum, boolean isLeague) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_REGISTERTOBRIDGE_LONG_LONG_INT_STRING_STRING_STRING_INT_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_REGISTERTOBRIDGE_LONG_LONG_INT_STRING_STRING_STRING_INT_BOOLEAN", new Object[] {humanId, stageIdNew, stageSn, stageName, nodeId, portId, lineNum, isLeague});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#reserveMethod3(String param)}*/
	public void reserveMethod3(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_RESERVEMETHOD3_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_RESERVEMETHOD3_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#reserveMethod4(String param)}*/
	public void reserveMethod4(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_RESERVEMETHOD4_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_RESERVEMETHOD4_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#sealAccount(long humanId, int type, long endTime)}*/
	public void sealAccount(long humanId, int type, long endTime) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SEALACCOUNT_LONG_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SEALACCOUNT_LONG_INT_LONG", new Object[] {humanId, type, endTime});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendActivityCrossHumanRankSettle(List infoList, int actSn, int round, int rankSn)}*/
	public void sendActivityCrossHumanRankSettle(List infoList, int actSn, int round, int rankSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDACTIVITYCROSSHUMANRANKSETTLE_LIST_INT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDACTIVITYCROSSHUMANRANKSETTLE_LIST_INT_INT_INT", new Object[] {infoList, actSn, round, rankSn});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendAllServerInform(HumanScopeKey type, List keys, int channel, List list)}*/
	public void sendAllServerInform(HumanScopeKey type, List keys, int channel, List list) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDALLSERVERINFORM_HUMANSCOPEKEY_LIST_INT_LIST,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDALLSERVERINFORM_HUMANSCOPEKEY_LIST_INT_LIST", new Object[] {type, keys, channel, list});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendArenaCrossSettle(Map map)}*/
	public void sendArenaCrossSettle(Map map) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDARENACROSSSETTLE_MAP,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDARENACROSSSETTLE_MAP", new Object[] {map});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendArenaRankedSettle(Map map)}*/
	public void sendArenaRankedSettle(Map map) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDARENARANKEDSETTLE_MAP,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDARENARANKEDSETTLE_MAP", new Object[] {map});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendCrossWarReward(Map crossWarRewardVOMap)}*/
	public void sendCrossWarReward(Map crossWarRewardVOMap) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDCROSSWARREWARD_MAP,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDCROSSWARREWARD_MAP", new Object[] {crossWarRewardVOMap});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#sendFactionMsgTo(Message msg)}*/
	public void sendFactionMsgTo(Message msg) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDFACTIONMSGTO_MESSAGE,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDFACTIONMSGTO_MESSAGE", new Object[] {msg});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#sendFillMail(FillMail fillMail, boolean isInside)}*/
	public void sendFillMail(FillMail fillMail, boolean isInside) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDFILLMAIL_FILLMAIL_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDFILLMAIL_FILLMAIL_BOOLEAN", new Object[] {fillMail, isInside});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendGroupGiftReward(List humanIdList, int groupSn)}*/
	public void sendGroupGiftReward(List humanIdList, int groupSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGROUPGIFTREWARD_LIST_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGROUPGIFTREWARD_LIST_INT", new Object[] {humanIdList, groupSn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#sendGuildMsgTo(long guildId, Message msg)}*/
	public void sendGuildMsgTo(long guildId, Message msg) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGUILDMSGTO_LONG_MESSAGE,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGUILDMSGTO_LONG_MESSAGE", new Object[] {guildId, msg});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendGuildTreasureBoxInfo(List humanIdList)}*/
	public void sendGuildTreasureBoxInfo(List humanIdList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGUILDTREASUREBOXINFO_LIST,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGUILDTREASUREBOXINFO_LIST", new Object[] {humanIdList});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendGveKillMail(List humanIdList, int gveSn, int mailSn)}*/
	public void sendGveKillMail(List humanIdList, int gveSn, int mailSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGVEKILLMAIL_LIST_INT_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDGVEKILLMAIL_LIST_INT_INT", new Object[] {humanIdList, gveSn, mailSn});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendKungFuRaceReward(Map rewardVOMap)}*/
	public void sendKungFuRaceReward(Map rewardVOMap) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDKUNGFURACEREWARD_MAP,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDKUNGFURACEREWARD_MAP", new Object[] {rewardVOMap});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendLeagueSettleScore(Map leagueVOMap)}*/
	public void sendLeagueSettleScore(Map leagueVOMap) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDLEAGUESETTLESCORE_MAP,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDLEAGUESETTLESCORE_MAP", new Object[] {leagueVOMap});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#sendMsg(long humanId, Message msg)}*/
	public void sendMsg(long humanId, Message msg) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSG_LONG_MESSAGE,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSG_LONG_MESSAGE", new Object[] {humanId, msg});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#sendMsgChatMessage(Param param)}*/
	public void sendMsgChatMessage(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGCHATMESSAGE_PARAM,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGCHATMESSAGE_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendMsgParam(List humanIds, Param param)}*/
	public void sendMsgParam(List humanIds, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGPARAM_LIST_PARAM,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGPARAM_LIST_PARAM", new Object[] {humanIds, param});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendMsgServerIdToAll(int serverId, List excludeIds, Message msg)}*/
	public void sendMsgServerIdToAll(int serverId, List excludeIds, Message msg) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGSERVERIDTOALL_INT_LIST_MESSAGE,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGSERVERIDTOALL_INT_LIST_MESSAGE", new Object[] {serverId, excludeIds, msg});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendMsgTo(List humanIds, Message msg)}*/
	public void sendMsgTo(List humanIds, Message msg) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGTO_LIST_MESSAGE,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGTO_LIST_MESSAGE", new Object[] {humanIds, msg});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendMsgToAll(List excludeIds, Message msg)}*/
	public void sendMsgToAll(List excludeIds, Message msg) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGTOALL_LIST_MESSAGE,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSGTOALL_LIST_MESSAGE", new Object[] {excludeIds, msg});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendMsg_guild_schedule_s2c(List guildIdList, int state)}*/
	public void sendMsg_guild_schedule_s2c(List guildIdList, int state) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSG_GUILD_SCHEDULE_S2C_LIST_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDMSG_GUILD_SCHEDULE_S2C_LIST_INT", new Object[] {guildIdList, state});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendSettleGuildReward(Map leagueVOMap)}*/
	public void sendSettleGuildReward(Map leagueVOMap) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDSETTLEGUILDREWARD_MAP,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDSETTLEGUILDREWARD_MAP", new Object[] {leagueVOMap});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#sendSettleHumanReward(Map leagueVOMap)}*/
	public void sendSettleHumanReward(Map leagueVOMap) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDSETTLEHUMANREWARD_MAP,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDSETTLEHUMANREWARD_MAP", new Object[] {leagueVOMap});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#sendSystemMarquee(Param param)}*/
	public void sendSystemMarquee(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDSYSTEMMARQUEE_PARAM,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SENDSYSTEMMARQUEE_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#silence(long operHumanId, int operCampType, long humanId, long keepTime)}*/
	public void silence(long operHumanId, int operCampType, long humanId, long keepTime) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SILENCE_LONG_INT_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SILENCE_LONG_INT_LONG_LONG", new Object[] {operHumanId, operCampType, humanId, keepTime});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#stageIdModify(long humanId, long stageIdNew, int mapSnNew, String stageName, String nodeId, String portId, int lineNum)}*/
	public void stageIdModify(long humanId, long stageIdNew, int mapSnNew, String stageName, String nodeId, String portId, int lineNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_STAGEIDMODIFY_LONG_LONG_INT_STRING_STRING_STRING_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_STAGEIDMODIFY_LONG_LONG_INT_STRING_STRING_STRING_INT", new Object[] {humanId, stageIdNew, mapSnNew, stageName, nodeId, portId, lineNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#switchRole(String account, CallPoint cp)}*/
	public void switchRole(String account, CallPoint cp) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SWITCHROLE_STRING_CALLPOINT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SWITCHROLE_STRING_CALLPOINT", new Object[] {account, cp});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#switchTo(long humanId, long stageId, Object... params)}*/
	public void switchTo(long humanId, long stageId, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SWITCHTO_LONG_LONG_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SWITCHTO_LONG_LONG_OBJECTS", new Object[] {humanId, stageId, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#syncInfoTime(long humanId)}*/
	public void syncInfoTime(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SYNCINFOTIME_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_SYNCINFOTIME_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#triggerMsgIdOperate(long humanId, int msgId)}*/
	public void triggerMsgIdOperate(long humanId, int msgId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_TRIGGERMSGIDOPERATE_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_TRIGGERMSGIDOPERATE_LONG_INT", new Object[] {humanId, msgId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#update1(String json)}*/
	public void update1(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE1_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE1_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#update2(Object... objs)}*/
	public void update2(Object... objs) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE2_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE2_OBJECTS", new Object[] {objs});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#update3(Param param)}*/
	public void update3(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE3_PARAM,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE3_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#update4(String json)}*/
	public void update4(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE4_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATE4_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#updateBuildPorpCalc(long humanId)}*/
	public void updateBuildPorpCalc(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATEBUILDPORPCALC_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATEBUILDPORPCALC_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#updateCamp(long humanId, long campType)}*/
	public void updateCamp(long humanId, long campType) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATECAMP_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATECAMP_LONG_LONG", new Object[] {humanId, campType});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanGlobalService#updateGuildInfo(List humanIdList, Guild guild)}*/
	public void updateGuildInfo(List humanIdList, Guild guild) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATEGUILDINFO_LIST_GUILD,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATEGUILDINFO_LIST_GUILD", new Object[] {humanIdList, guild});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#updateGuildPosition(long humanId, int guildPosition)}*/
	public void updateGuildPosition(long humanId, int guildPosition) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATEGUILDPOSITION_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATEGUILDPOSITION_LONG_INT", new Object[] {humanId, guildPosition});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#updateLevel(long humanId, int level)}*/
	public void updateLevel(long humanId, int level) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATELEVEL_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATELEVEL_LONG_INT", new Object[] {humanId, level});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#updateName(long humanId, String name)}*/
	public void updateName(long humanId, String name) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATENAME_LONG_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATENAME_LONG_STRING", new Object[] {humanId, name});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanGlobalService#updateSign(long humanId, String sign)}*/
	public void updateSign(long humanId, String sign) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATESIGN_LONG_STRING,"ORG_GOF_DEMO_WORLDSRV_HUMAN_HUMANGLOBALSERVICE_UPDATESIGN_LONG_STRING", new Object[] {humanId, sign});
		if(immutableOnce) immutableOnce = false;
	}
}
