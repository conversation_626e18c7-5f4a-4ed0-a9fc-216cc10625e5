package org.gof.demo.worldsrv.crossWar;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import java.util.List;
import org.gof.core.support.Param;
import org.gof.demo.battlesrv.support.Vector2D;

@GofGenFile
public final class CrossWarServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_BIGMAPSLIDE_LONG_VECTOR2D = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ENDBATTLE_LONG = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ENTERBIGMAP_LONG_INT_VECTOR2D = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ENTERSCENE_LONG_INT_VECTOR2D = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETCROSSWARINFO_LONG_INT = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETCROSSWARRANK_LONG_INT_INT_INT = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETKILLLIST_LONG = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETSCENEINFO_LONG = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GMCOMMAND_STRING_PARAM = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_INITAFTERCONNECTADMIN = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_LEAVESCENE_LONG = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_MOVETO_LONG_PARAM = 12;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ONPLAYERLOGINFINISH_LONG = 13;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ONPLAYERLOGOUT_LONG = 14;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ONPLAYERRECONNECT_LONG = 15;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_REVIVE_LONG_INT_BOOLEAN = 16;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_SELECTSERVER_LONG_PARAM = 17;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_SPEEDUP_LONG = 18;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_TRANSFERCLOSE_LONG = 19;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_UPDATE_STRING = 20;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_UPDATE1_OBJECTS = 21;
		public static final int ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_UPDATE2_STRING = 22;
	}

	private static final String SERV_ID = "crossWar";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private CrossWarServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings("unchecked")
	public Object getMethodFunction(Service service, int methodKey) {
		CrossWarService serv = (CrossWarService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_BIGMAPSLIDE_LONG_VECTOR2D: {
				return (GofFunction2<Long, Vector2D>)serv::bigMapSlide;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ENDBATTLE_LONG: {
				return (GofFunction1<Long>)serv::endBattle;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ENTERBIGMAP_LONG_INT_VECTOR2D: {
				return (GofFunction3<Long, Integer, Vector2D>)serv::enterBigMap;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ENTERSCENE_LONG_INT_VECTOR2D: {
				return (GofFunction3<Long, Integer, Vector2D>)serv::enterScene;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETCROSSWARINFO_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::getCrossWarInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETCROSSWARRANK_LONG_INT_INT_INT: {
				return (GofFunction4<Long, Integer, Integer, Integer>)serv::getCrossWarRank;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETKILLLIST_LONG: {
				return (GofFunction1<Long>)serv::getKillList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETSCENEINFO_LONG: {
				return (GofFunction1<Long>)serv::getSceneInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GMCOMMAND_STRING_PARAM: {
				return (GofFunction2<String, Param>)serv::gmCommand;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_INITAFTERCONNECTADMIN: {
				return (GofFunction0)serv::initAfterConnectAdmin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_LEAVESCENE_LONG: {
				return (GofFunction1<Long>)serv::leaveScene;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_MOVETO_LONG_PARAM: {
				return (GofFunction2<Long, Param>)serv::moveTo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ONPLAYERLOGINFINISH_LONG: {
				return (GofFunction1<Long>)serv::onPlayerLoginFinish;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ONPLAYERLOGOUT_LONG: {
				return (GofFunction1<Long>)serv::onPlayerLogout;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ONPLAYERRECONNECT_LONG: {
				return (GofFunction1<Long>)serv::onPlayerReconnect;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_REVIVE_LONG_INT_BOOLEAN: {
				return (GofFunction3<Long, Integer, Boolean>)serv::revive;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_SELECTSERVER_LONG_PARAM: {
				return (GofFunction2<Long, Param>)serv::selectServer;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_SPEEDUP_LONG: {
				return (GofFunction1<Long>)serv::speedUp;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_TRANSFERCLOSE_LONG: {
				return (GofFunction1<Long>)serv::transferClose;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_UPDATE_STRING: {
				return (GofFunction1<String>)serv::update;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_UPDATE1_OBJECTS: {
				return (GofFunction1<Object[]>)serv::update1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_UPDATE2_STRING: {
				return (GofFunction1<String>)serv::update2;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static CrossWarServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static CrossWarServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static CrossWarServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static CrossWarServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static CrossWarServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static CrossWarServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static CrossWarServiceProxy createInstance(String node, String port, Object serviceId) {
		CrossWarServiceProxy inst = new CrossWarServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link CrossWarService#bigMapSlide(long humanId, Vector2D pos)}*/
	public void bigMapSlide(long humanId, Vector2D pos) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_BIGMAPSLIDE_LONG_VECTOR2D,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_BIGMAPSLIDE_LONG_VECTOR2D", new Object[] {humanId, pos});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#endBattle(long humanId)}*/
	public void endBattle(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ENDBATTLE_LONG,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ENDBATTLE_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#enterBigMap(long humanId, int sceneBaseId, Vector2D pos)}*/
	public void enterBigMap(long humanId, int sceneBaseId, Vector2D pos) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ENTERBIGMAP_LONG_INT_VECTOR2D,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ENTERBIGMAP_LONG_INT_VECTOR2D", new Object[] {humanId, sceneBaseId, pos});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#enterScene(long humanId, int type, Vector2D pos)}*/
	public void enterScene(long humanId, int type, Vector2D pos) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ENTERSCENE_LONG_INT_VECTOR2D,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ENTERSCENE_LONG_INT_VECTOR2D", new Object[] {humanId, type, pos});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#getCrossWarInfo(long humanId, int serverId)}*/
	public void getCrossWarInfo(long humanId, int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETCROSSWARINFO_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETCROSSWARINFO_LONG_INT", new Object[] {humanId, serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#getCrossWarRank(long humanId, int serverId, int rankType, int page)}*/
	public void getCrossWarRank(long humanId, int serverId, int rankType, int page) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETCROSSWARRANK_LONG_INT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETCROSSWARRANK_LONG_INT_INT_INT", new Object[] {humanId, serverId, rankType, page});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#getKillList(long humanId)}*/
	public void getKillList(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETKILLLIST_LONG,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETKILLLIST_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#getSceneInfo(long humanId)}*/
	public void getSceneInfo(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETSCENEINFO_LONG,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GETSCENEINFO_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#gmCommand(String type, Param param)}*/
	public void gmCommand(String type, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GMCOMMAND_STRING_PARAM,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_GMCOMMAND_STRING_PARAM", new Object[] {type, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#initAfterConnectAdmin()}*/
	public void initAfterConnectAdmin() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_INITAFTERCONNECTADMIN,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_INITAFTERCONNECTADMIN", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#leaveScene(long humanId)}*/
	public void leaveScene(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_LEAVESCENE_LONG,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_LEAVESCENE_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#moveTo(long humanId, Param param)}*/
	public void moveTo(long humanId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_MOVETO_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_MOVETO_LONG_PARAM", new Object[] {humanId, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#onPlayerLoginFinish(long humanId)}*/
	public void onPlayerLoginFinish(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ONPLAYERLOGINFINISH_LONG,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ONPLAYERLOGINFINISH_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#onPlayerLogout(long humanId)}*/
	public void onPlayerLogout(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ONPLAYERLOGOUT_LONG,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ONPLAYERLOGOUT_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#onPlayerReconnect(long humanId)}*/
	public void onPlayerReconnect(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ONPLAYERRECONNECT_LONG,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_ONPLAYERRECONNECT_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#revive(long humanId, int type, boolean costItemEnough)}*/
	public void revive(long humanId, int type, boolean costItemEnough) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_REVIVE_LONG_INT_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_REVIVE_LONG_INT_BOOLEAN", new Object[] {humanId, type, costItemEnough});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#selectServer(long humanId, Param param)}*/
	public void selectServer(long humanId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_SELECTSERVER_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_SELECTSERVER_LONG_PARAM", new Object[] {humanId, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#speedUp(long humanId)}*/
	public void speedUp(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_SPEEDUP_LONG,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_SPEEDUP_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#transferClose(long humanId)}*/
	public void transferClose(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_TRANSFERCLOSE_LONG,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_TRANSFERCLOSE_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#update(String jo)}*/
	public void update(String jo) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_UPDATE_STRING,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_UPDATE_STRING", new Object[] {jo});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#update1(Object... objs)}*/
	public void update1(Object... objs) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_UPDATE1_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_UPDATE1_OBJECTS", new Object[] {objs});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CrossWarService#update2(String str)}*/
	public void update2(String str) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_UPDATE2_STRING,"ORG_GOF_DEMO_WORLDSRV_CROSSWAR_CROSSWARSERVICE_UPDATE2_STRING", new Object[] {str});
		if(immutableOnce) immutableOnce = false;
	}
}
