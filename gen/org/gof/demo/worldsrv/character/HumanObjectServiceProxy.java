package org.gof.demo.worldsrv.character;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import org.gof.demo.worldsrv.entity.Mail;
import org.gof.demo.worldsrv.entity.FillMail;
import java.util.Set;
import java.util.Map;
import java.util.List;
import org.gof.demo.worldsrv.instance.BridgeInstanceAidVO;
import org.gof.demo.worldsrv.match.MatchMember;
import org.gof.demo.worldsrv.monster.DeadMonsterInfo;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.entity.PocketLine;
import org.gof.demo.worldsrv.home.FarmVo;
import org.gof.demo.worldsrv.home.LandVo;
import org.gof.demo.worldsrv.home.RobberVo;
import org.gof.demo.worldsrv.entity.Guild;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.entity.PayGift;
import org.gof.demo.worldsrv.msg.Define.p_car_park_record;
import org.gof.demo.worldsrv.msg.Define.p_double_chapter_help_player;
import org.gof.demo.worldsrv.msg.Define.p_battle_role;
import org.gof.demo.worldsrv.msg.Define.p_double_chapter_teammate;
import org.gof.demo.worldsrv.msg.Define.p_double_chapter_teammate_detail;

@GofGenFile
public final class HumanObjectServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHANGECONNPOINT_CALLPOINT = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ACCEPTFILLMAIL_FILLMAIL = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDCARPARKINCOME_INT = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDDOUBLECHAPTERTEAMMATE_P_DOUBLE_CHAPTER_TEAMMATE_P_DOUBLE_CHAPTER_TEAMMATE_DETAIL_P_BATTLE_ROLE = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDFLYHYBRIDASK_INT_LONG_LONG = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDFRIENDAPPLY_LONG = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BACKMETHOD4_STRING = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGEITEMUSE_INT_INT_INT = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGEITEMUSE_INT_INT_INT_MONEYITEMLOGKEY = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGELOGOUT = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEHIDE = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEHISTORYSET_LONG_INT_STRING_VECTOR2D = 12;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEINSET_BOOLEAN = 13;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEPOSUPDATE_VECTOR2D = 14;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGESHOW_PARAM = 15;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CAPTUREADDENEMY_LONG = 16;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHARGEGS_INT = 17;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHAT2MSG_LONG = 18;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHECKANDCONSUME_MAP_MONEYITEMLOGKEY = 19;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHECKANDUPDATETEAMMATE_LONG = 20;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CLEARMODULERESET_INT = 21;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CLOSEACTIVITY_LIST = 22;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CONNCHECK_LONG = 23;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CONNCLOSED_LONG = 24;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CONNLOADPOCKETLINE = 25;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CROSSWARINVADEEND_INT = 26;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CROSSWARKILLMONSTER_BOOLEAN = 27;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CROSSWARKILLPLAYER_BOOLEAN = 28;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DEALFRIENDAPPLY_LONG_LONG = 29;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DELFRIEND_LONG = 30;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DENYAPPLY_STRING = 31;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DOLEAVEREP = 32;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ENDSHOWACTIVITY_LIST = 33;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_EVENTFIRE_INT_PARAM = 34;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_FACTIONBOSSENDEVENT = 35;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_FREESLAVE_LONG = 36;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETDEBUGHUMANINFO = 37;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETFLYPET_LONG = 38;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMAN = 39;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMANBRIEF_LONG = 40;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMANOBJCETCOPY = 41;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMANOBJCETMIRR = 42;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETPBATTLEROLEMSG_BOOLEAN = 43;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETTOP3INGUILDQUESTIONINNERRANK = 44;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GIVEGIFT_LONG = 45;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GMCALLHUMANSERVICEMETHOD_PARAM = 46;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GM_GS_INT_INT = 47;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GUILDGVEJOIN_INT_INT_LONG = 48;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GVESETTLE_INT = 49;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GVGBATTLEKILLENEMY_INT = 50;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HANDLEINVITESHARE_P_DOUBLE_CHAPTER_HELP_PLAYER = 51;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HELP_INT_INT = 52;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HELPFERTILIZE_LONG_STRING_LANDVO_INT_INT = 53;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HUMANLOGINGAMESTATE = 54;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ISINBLOCKLIST_LONG = 55;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KICKCLOSED = 56;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KICKFACTIONMEMBEROUT = 57;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KUNGFURACEADDBETCOIN_INT = 58;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KUNGFURACEBATTLENEWSCORE_INT = 59;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KUNGFURACERECYCLEBETCOIN = 60;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_LEAVE_LONG = 61;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_LEAVEGUILD_LONG = 62;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MAILACCEPT_MAIL = 63;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MEMBERSWITCHTOMIRROR_LONG_VECTOR2D_LONG_INT = 64;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MOSTERDIETOADDQUESTPROCESS_STRING_INT_BOOLEAN = 65;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MOVETOLEADER_LONG_LONG_INT_INT_DOUBLE_DOUBLE = 66;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MSGHANDLER_LONG_BYTES = 67;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_NOTIFYGUILDPAYINFO_INT_INT = 68;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONACTIVITYLISTCHANGE = 69;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONCROSSWAREND = 70;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONHUMANJOINTEAM_LONG = 71;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONHUMANLEAVETEAM = 72;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONQUESTCHANGE_SET = 73;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONSCHEDULE_INT_LONG = 74;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_OPENACTIVITY_LIST = 75;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAY_STRING = 76;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAYGIFT_STRING = 77;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAYGIFTNEW_PAYGIFT_STRING = 78;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAYTW_STRING = 79;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_POCKLINEPROCESS_POCKETLINE = 80;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PRODUCEADD_INT_INT_MONEYITEMLOGKEY_OBJECTS = 81;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PRODUCEADD_MAP_MONEYITEMLOGKEY_OBJECTS = 82;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RECEIVECARPARKREWARD_MAP_BOOLEAN_MAP_P_CAR_PARK_RECORD_BOOLEAN = 83;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RECEIVEFLOWER_LONG_INT_INT_INT = 84;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_REMOVEITEM_INT_INT = 85;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_REMOVEMONEY_INT_INT = 86;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RESERVEMETHOD5_STRING = 87;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RESERVEMETHOD6_STRING = 88;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RESETGAMESESSION_STRING = 89;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SEALACCOUNT_INT_LONG = 90;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDCHATMSG_PARAM = 91;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDFARMINFO_INT = 92;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDFILLMAIL_FILLMAIL = 93;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDGUILDTREASUREBOXINFO = 94;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDGVEKILLMAIL_INT_INT = 95;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDMSGPARAM_PARAM = 96;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SETSILENCEENDTIME = 97;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SETTEAMID_LONG = 98;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SILENCE_LONG = 99;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_STEALCOMPLETED_FARMVO_INT = 100;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_STEALFAILD_LONG_LANDVO = 101;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_STOLENCOMPLETED_FARMVO_INT_ROBBERVO = 102;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SWITCHTO_LONG_OBJECTS = 103;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEAMCHANGE_STRING_LONG = 104;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST01_STRING = 105;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST02_STRING = 106;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST03_STRING = 107;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST04_STRING = 108;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TRIGGERMSGIDOPERATE_INT = 109;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE1_STRING = 110;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE2_OBJECTS = 111;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE3_PARAM = 112;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE4_STRING = 113;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEBUILDPORPCALC = 114;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEDOUBLECHAPTERHELPLIST_P_DOUBLE_CHAPTER_HELP_PLAYER_BOOLEAN = 115;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEFLYHYBRIDPARTNER_LONG_INT = 116;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEFLYPETBORROWHYBRID_LONG = 117;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEGUILDINFO_GUILD = 118;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEGUILDPOSITION_INT = 119;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATESTAGEHISTORY_STRING = 120;
		public static final int ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_WORLDBOSSENDEVENT = 121;
	}

	private static final String SERV_ID = "";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private HumanObjectServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		HumanObjectService serv = (HumanObjectService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHANGECONNPOINT_CALLPOINT: {
				return (GofFunction1<CallPoint>)serv::ChangeConnPoint;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ACCEPTFILLMAIL_FILLMAIL: {
				return (GofFunction1<FillMail>)serv::acceptFillMail;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDCARPARKINCOME_INT: {
				return (GofFunction1<Integer>)serv::addCarParkIncome;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDDOUBLECHAPTERTEAMMATE_P_DOUBLE_CHAPTER_TEAMMATE_P_DOUBLE_CHAPTER_TEAMMATE_DETAIL_P_BATTLE_ROLE: {
				return (GofFunction3<p_double_chapter_teammate, p_double_chapter_teammate_detail, p_battle_role>)serv::addDoubleChapterTeammate;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDFLYHYBRIDASK_INT_LONG_LONG: {
				return (GofFunction3<Integer, Long, Long>)serv::addFlyHybridAsk;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDFRIENDAPPLY_LONG: {
				return (GofFunction1<Long>)serv::addFriendApply;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BACKMETHOD4_STRING: {
				return (GofFunction1<String>)serv::backMethod4;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGEITEMUSE_INT_INT_INT: {
				return (GofFunction3<Integer, Integer, Integer>)serv::bridgeItemUse;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGEITEMUSE_INT_INT_INT_MONEYITEMLOGKEY: {
				return (GofFunction4<Integer, Integer, Integer, MoneyItemLogKey>)serv::bridgeItemUse;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGELOGOUT: {
				return (GofFunction0)serv::bridgeLogout;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEHIDE: {
				return (GofFunction0)serv::bridgeStageHide;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEHISTORYSET_LONG_INT_STRING_VECTOR2D: {
				return (GofFunction4<Long, Integer, String, Vector2D>)serv::bridgeStageHistorySet;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEINSET_BOOLEAN: {
				return (GofFunction1<Boolean>)serv::bridgeStageInSet;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEPOSUPDATE_VECTOR2D: {
				return (GofFunction1<Vector2D>)serv::bridgeStagePosUpdate;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGESHOW_PARAM: {
				return (GofFunction1<Param>)serv::bridgeStageShow;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CAPTUREADDENEMY_LONG: {
				return (GofFunction1<Long>)serv::captureAddEnemy;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHARGEGS_INT: {
				return (GofFunction1<Integer>)serv::chargeGs;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHAT2MSG_LONG: {
				return (GofFunction1<Long>)serv::chat2Msg;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHECKANDCONSUME_MAP_MONEYITEMLOGKEY: {
				return (GofFunction2<Map, MoneyItemLogKey>)serv::checkAndConsume;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHECKANDUPDATETEAMMATE_LONG: {
				return (GofFunction1<Long>)serv::checkAndUpdateTeammate;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CLEARMODULERESET_INT: {
				return (GofFunction1<Integer>)serv::clearModuleReset;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CLOSEACTIVITY_LIST: {
				return (GofFunction1<List>)serv::closeActivity;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CONNCHECK_LONG: {
				return (GofFunction1<Long>)serv::connCheck;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CONNCLOSED_LONG: {
				return (GofFunction1<Long>)serv::connClosed;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CONNLOADPOCKETLINE: {
				return (GofFunction0)serv::connLoadPocketLine;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CROSSWARINVADEEND_INT: {
				return (GofFunction1<Integer>)serv::crossWarInvadeEnd;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CROSSWARKILLMONSTER_BOOLEAN: {
				return (GofFunction1<Boolean>)serv::crossWarKillMonster;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CROSSWARKILLPLAYER_BOOLEAN: {
				return (GofFunction1<Boolean>)serv::crossWarKillPlayer;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DEALFRIENDAPPLY_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::dealFriendApply;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DELFRIEND_LONG: {
				return (GofFunction1<Long>)serv::delFriend;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DENYAPPLY_STRING: {
				return (GofFunction1<String>)serv::denyApply;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DOLEAVEREP: {
				return (GofFunction0)serv::doLeaveRep;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ENDSHOWACTIVITY_LIST: {
				return (GofFunction1<List>)serv::endShowActivity;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_EVENTFIRE_INT_PARAM: {
				return (GofFunction2<Integer, Param>)serv::eventFire;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_FACTIONBOSSENDEVENT: {
				return (GofFunction0)serv::factionBossEndEvent;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_FREESLAVE_LONG: {
				return (GofFunction1<Long>)serv::freeSlave;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETDEBUGHUMANINFO: {
				return (GofFunction0)serv::getDebugHumanInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETFLYPET_LONG: {
				return (GofFunction1<Long>)serv::getFlyPet;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMAN: {
				return (GofFunction0)serv::getHuman;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMANBRIEF_LONG: {
				return (GofFunction1<Long>)serv::getHumanBrief;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMANOBJCETCOPY: {
				return (GofFunction0)serv::getHumanObjcetCopy;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMANOBJCETMIRR: {
				return (GofFunction0)serv::getHumanObjcetMirr;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETPBATTLEROLEMSG_BOOLEAN: {
				return (GofFunction1<Boolean>)serv::getPBattleRoleMsg;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETTOP3INGUILDQUESTIONINNERRANK: {
				return (GofFunction0)serv::getTop3InGuildQuestionInnerRank;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GIVEGIFT_LONG: {
				return (GofFunction1<Long>)serv::giveGift;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GMCALLHUMANSERVICEMETHOD_PARAM: {
				return (GofFunction1<Param>)serv::gmCallHumanServiceMethod;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GM_GS_INT_INT: {
				return (GofFunction2<Integer, Integer>)serv::gm_Gs;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GUILDGVEJOIN_INT_INT_LONG: {
				return (GofFunction3<Integer, Integer, Long>)serv::guildGveJoin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GVESETTLE_INT: {
				return (GofFunction1<Integer>)serv::gveSettle;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GVGBATTLEKILLENEMY_INT: {
				return (GofFunction1<Integer>)serv::gvgBattleKillEnemy;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HANDLEINVITESHARE_P_DOUBLE_CHAPTER_HELP_PLAYER: {
				return (GofFunction1<p_double_chapter_help_player>)serv::handleInviteShare;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HELP_INT_INT: {
				return (GofFunction2<Integer, Integer>)serv::help;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HELPFERTILIZE_LONG_STRING_LANDVO_INT_INT: {
				return (GofFunction5<Long, String, LandVo, Integer, Integer>)serv::helpFertilize;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HUMANLOGINGAMESTATE: {
				return (GofFunction0)serv::humanLoginGameState;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ISINBLOCKLIST_LONG: {
				return (GofFunction1<Long>)serv::isInBlockList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KICKCLOSED: {
				return (GofFunction0)serv::kickClosed;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KICKFACTIONMEMBEROUT: {
				return (GofFunction0)serv::kickFactionMemberOut;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KUNGFURACEADDBETCOIN_INT: {
				return (GofFunction1<Integer>)serv::kungFuRaceAddBetCoin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KUNGFURACEBATTLENEWSCORE_INT: {
				return (GofFunction1<Integer>)serv::kungFuRaceBattleNewScore;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KUNGFURACERECYCLEBETCOIN: {
				return (GofFunction0)serv::kungFuRaceRecycleBetCoin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_LEAVE_LONG: {
				return (GofFunction1<Long>)serv::leave;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_LEAVEGUILD_LONG: {
				return (GofFunction1<Long>)serv::leaveGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MAILACCEPT_MAIL: {
				return (GofFunction1<Mail>)serv::mailAccept;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MEMBERSWITCHTOMIRROR_LONG_VECTOR2D_LONG_INT: {
				return (GofFunction4<Long, Vector2D, Long, Integer>)serv::memberSwitchToMirror;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MOSTERDIETOADDQUESTPROCESS_STRING_INT_BOOLEAN: {
				return (GofFunction3<String, Integer, Boolean>)serv::mosterDieToAddQuestProcess;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MOVETOLEADER_LONG_LONG_INT_INT_DOUBLE_DOUBLE: {
				return (GofFunction6<Long, Long, Integer, Integer, Double, Double>)serv::moveToLeader;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MSGHANDLER_LONG_BYTES: {
				return (GofFunction2<Long, byte[]>)serv::msgHandler;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_NOTIFYGUILDPAYINFO_INT_INT: {
				return (GofFunction2<Integer, Integer>)serv::notifyGuildPayInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONACTIVITYLISTCHANGE: {
				return (GofFunction0)serv::onActivityListChange;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONCROSSWAREND: {
				return (GofFunction0)serv::onCrossWarEnd;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONHUMANJOINTEAM_LONG: {
				return (GofFunction1<Long>)serv::onHumanJoinTeam;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONHUMANLEAVETEAM: {
				return (GofFunction0)serv::onHumanLeaveTeam;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONQUESTCHANGE_SET: {
				return (GofFunction1<Set>)serv::onQuestChange;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONSCHEDULE_INT_LONG: {
				return (GofFunction2<Integer, Long>)serv::onSchedule;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_OPENACTIVITY_LIST: {
				return (GofFunction1<List>)serv::openActivity;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAY_STRING: {
				return (GofFunction1<String>)serv::pay;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAYGIFT_STRING: {
				return (GofFunction1<String>)serv::payGift;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAYGIFTNEW_PAYGIFT_STRING: {
				return (GofFunction2<PayGift, String>)serv::payGiftNew;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAYTW_STRING: {
				return (GofFunction1<String>)serv::payTw;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_POCKLINEPROCESS_POCKETLINE: {
				return (GofFunction1<PocketLine>)serv::pockLineProcess;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PRODUCEADD_INT_INT_MONEYITEMLOGKEY_OBJECTS: {
				return (GofFunction4<Integer, Integer, MoneyItemLogKey, Object[]>)serv::produceAdd;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PRODUCEADD_MAP_MONEYITEMLOGKEY_OBJECTS: {
				return (GofFunction3<Map, MoneyItemLogKey, Object[]>)serv::produceAdd;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RECEIVECARPARKREWARD_MAP_BOOLEAN_MAP_P_CAR_PARK_RECORD_BOOLEAN: {
				return (GofFunction5<Map, Boolean, Map, p_car_park_record, Boolean>)serv::receiveCarParkReward;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RECEIVEFLOWER_LONG_INT_INT_INT: {
				return (GofFunction4<Long, Integer, Integer, Integer>)serv::receiveFlower;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_REMOVEITEM_INT_INT: {
				return (GofFunction2<Integer, Integer>)serv::removeItem;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_REMOVEMONEY_INT_INT: {
				return (GofFunction2<Integer, Integer>)serv::removeMoney;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RESERVEMETHOD5_STRING: {
				return (GofFunction1<String>)serv::reserveMethod5;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RESERVEMETHOD6_STRING: {
				return (GofFunction1<String>)serv::reserveMethod6;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RESETGAMESESSION_STRING: {
				return (GofFunction1<String>)serv::resetGameSession;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SEALACCOUNT_INT_LONG: {
				return (GofFunction2<Integer, Long>)serv::sealAccount;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDCHATMSG_PARAM: {
				return (GofFunction1<Param>)serv::sendChatMsg;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDFARMINFO_INT: {
				return (GofFunction1<Integer>)serv::sendFarmInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDFILLMAIL_FILLMAIL: {
				return (GofFunction1<FillMail>)serv::sendFillMail;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDGUILDTREASUREBOXINFO: {
				return (GofFunction0)serv::sendGuildTreasureBoxInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDGVEKILLMAIL_INT_INT: {
				return (GofFunction2<Integer, Integer>)serv::sendGveKillMail;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDMSGPARAM_PARAM: {
				return (GofFunction1<Param>)serv::sendMsgParam;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SETSILENCEENDTIME: {
				return (GofFunction0)serv::setSilenceEndTime;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SETTEAMID_LONG: {
				return (GofFunction1<Long>)serv::setTeamId;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SILENCE_LONG: {
				return (GofFunction1<Long>)serv::silence;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_STEALCOMPLETED_FARMVO_INT: {
				return (GofFunction2<FarmVo, Integer>)serv::stealCompleted;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_STEALFAILD_LONG_LANDVO: {
				return (GofFunction2<Long, LandVo>)serv::stealFaild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_STOLENCOMPLETED_FARMVO_INT_ROBBERVO: {
				return (GofFunction3<FarmVo, Integer, RobberVo>)serv::stolenCompleted;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SWITCHTO_LONG_OBJECTS: {
				return (GofFunction2<Long, Object[]>)serv::switchTo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEAMCHANGE_STRING_LONG: {
				return (GofFunction2<String, Long>)serv::teamChange;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST01_STRING: {
				return (GofFunction1<String>)serv::test01;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST02_STRING: {
				return (GofFunction1<String>)serv::test02;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST03_STRING: {
				return (GofFunction1<String>)serv::test03;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST04_STRING: {
				return (GofFunction1<String>)serv::test04;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TRIGGERMSGIDOPERATE_INT: {
				return (GofFunction1<Integer>)serv::triggerMsgIdOperate;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE1_STRING: {
				return (GofFunction1<String>)serv::update1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE2_OBJECTS: {
				return (GofFunction1<Object[]>)serv::update2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE3_PARAM: {
				return (GofFunction1<Param>)serv::update3;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE4_STRING: {
				return (GofFunction1<String>)serv::update4;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEBUILDPORPCALC: {
				return (GofFunction0)serv::updateBuildPorpCalc;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEDOUBLECHAPTERHELPLIST_P_DOUBLE_CHAPTER_HELP_PLAYER_BOOLEAN: {
				return (GofFunction2<p_double_chapter_help_player, Boolean>)serv::updateDoubleChapterHelpList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEFLYHYBRIDPARTNER_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::updateFlyHybridPartner;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEFLYPETBORROWHYBRID_LONG: {
				return (GofFunction1<Long>)serv::updateFlyPetBorrowHybrid;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEGUILDINFO_GUILD: {
				return (GofFunction1<Guild>)serv::updateGuildInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEGUILDPOSITION_INT: {
				return (GofFunction1<Integer>)serv::updateGuildPosition;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATESTAGEHISTORY_STRING: {
				return (GofFunction1<String>)serv::updateStageHistory;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_WORLDBOSSENDEVENT: {
				return (GofFunction0)serv::worldBossEndEvent;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static HumanObjectServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static HumanObjectServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static HumanObjectServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static HumanObjectServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static HumanObjectServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static HumanObjectServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static HumanObjectServiceProxy createInstance(String node, String port, Object serviceId) {
		HumanObjectServiceProxy inst = new HumanObjectServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link HumanObjectService#ChangeConnPoint(CallPoint point)}*/
	public void ChangeConnPoint(CallPoint point) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHANGECONNPOINT_CALLPOINT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHANGECONNPOINT_CALLPOINT", new Object[] {point});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#acceptFillMail(FillMail fillMail)}*/
	public void acceptFillMail(FillMail fillMail) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ACCEPTFILLMAIL_FILLMAIL,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ACCEPTFILLMAIL_FILLMAIL", new Object[] {fillMail});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#addCarParkIncome(int income)}*/
	public void addCarParkIncome(int income) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDCARPARKINCOME_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDCARPARKINCOME_INT", new Object[] {income});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#addDoubleChapterTeammate(p_double_chapter_teammate inviterTeammate, p_double_chapter_teammate_detail teammateDetail, p_battle_role battleRole)}*/
	public void addDoubleChapterTeammate(p_double_chapter_teammate inviterTeammate, p_double_chapter_teammate_detail teammateDetail, p_battle_role battleRole) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDDOUBLECHAPTERTEAMMATE_P_DOUBLE_CHAPTER_TEAMMATE_P_DOUBLE_CHAPTER_TEAMMATE_DETAIL_P_BATTLE_ROLE,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDDOUBLECHAPTERTEAMMATE_P_DOUBLE_CHAPTER_TEAMMATE_P_DOUBLE_CHAPTER_TEAMMATE_DETAIL_P_BATTLE_ROLE", new Object[] {inviterTeammate, teammateDetail, battleRole});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#addFlyHybridAsk(int type, long partnerId, long petId)}*/
	public void addFlyHybridAsk(int type, long partnerId, long petId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDFLYHYBRIDASK_INT_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDFLYHYBRIDASK_INT_LONG_LONG", new Object[] {type, partnerId, petId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#addFriendApply(long friendId)}*/
	public void addFriendApply(long friendId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDFRIENDAPPLY_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ADDFRIENDAPPLY_LONG", new Object[] {friendId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#backMethod4(String param)}*/
	public void backMethod4(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BACKMETHOD4_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BACKMETHOD4_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#bridgeItemUse(int packType, int itemSn, int num)}*/
	public void bridgeItemUse(int packType, int itemSn, int num) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGEITEMUSE_INT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGEITEMUSE_INT_INT_INT", new Object[] {packType, itemSn, num});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#bridgeItemUse(int packType, int itemSn, int num, MoneyItemLogKey log)}*/
	public void bridgeItemUse(int packType, int itemSn, int num, MoneyItemLogKey log) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGEITEMUSE_INT_INT_INT_MONEYITEMLOGKEY,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGEITEMUSE_INT_INT_INT_MONEYITEMLOGKEY", new Object[] {packType, itemSn, num, log});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#bridgeLogout()}*/
	public void bridgeLogout() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGELOGOUT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGELOGOUT", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#bridgeStageHide()}*/
	public void bridgeStageHide() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEHIDE,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEHIDE", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#bridgeStageHistorySet(long stageId, int stageSn, String stageType, Vector2D pos)}*/
	public void bridgeStageHistorySet(long stageId, int stageSn, String stageType, Vector2D pos) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEHISTORYSET_LONG_INT_STRING_VECTOR2D,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEHISTORYSET_LONG_INT_STRING_VECTOR2D", new Object[] {stageId, stageSn, stageType, pos});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#bridgeStageInSet(boolean stageIn)}*/
	public void bridgeStageInSet(boolean stageIn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEINSET_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEINSET_BOOLEAN", new Object[] {stageIn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#bridgeStagePosUpdate(Vector2D pos)}*/
	public void bridgeStagePosUpdate(Vector2D pos) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEPOSUPDATE_VECTOR2D,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGEPOSUPDATE_VECTOR2D", new Object[] {pos});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#bridgeStageShow(Param param)}*/
	public void bridgeStageShow(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGESHOW_PARAM,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_BRIDGESTAGESHOW_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#captureAddEnemy(long slaveId)}*/
	public void captureAddEnemy(long slaveId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CAPTUREADDENEMY_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CAPTUREADDENEMY_LONG", new Object[] {slaveId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#chargeGs(int paySn)}*/
	public void chargeGs(int paySn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHARGEGS_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHARGEGS_INT", new Object[] {paySn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#chat2Msg(long sendHumanId)}*/
	public void chat2Msg(long sendHumanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHAT2MSG_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHAT2MSG_LONG", new Object[] {sendHumanId});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanObjectService#checkAndConsume(Map costItemMap, MoneyItemLogKey logKey)}*/
	public void checkAndConsume(Map costItemMap, MoneyItemLogKey logKey) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHECKANDCONSUME_MAP_MONEYITEMLOGKEY,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHECKANDCONSUME_MAP_MONEYITEMLOGKEY", new Object[] {costItemMap, logKey});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#checkAndUpdateTeammate(long humanId)}*/
	public void checkAndUpdateTeammate(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHECKANDUPDATETEAMMATE_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CHECKANDUPDATETEAMMATE_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#clearModuleReset(int type)}*/
	public void clearModuleReset(int type) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CLEARMODULERESET_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CLEARMODULERESET_INT", new Object[] {type});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanObjectService#closeActivity(List activityIdList)}*/
	public void closeActivity(List activityIdList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CLOSEACTIVITY_LIST,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CLOSEACTIVITY_LIST", new Object[] {activityIdList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#connCheck(long connId)}*/
	public void connCheck(long connId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CONNCHECK_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CONNCHECK_LONG", new Object[] {connId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#connClosed(long connId)}*/
	public void connClosed(long connId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CONNCLOSED_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CONNCLOSED_LONG", new Object[] {connId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#connLoadPocketLine()}*/
	public void connLoadPocketLine() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CONNLOADPOCKETLINE,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CONNLOADPOCKETLINE", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#crossWarInvadeEnd(int score)}*/
	public void crossWarInvadeEnd(int score) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CROSSWARINVADEEND_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CROSSWARINVADEEND_INT", new Object[] {score});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#crossWarKillMonster(boolean isInvade)}*/
	public void crossWarKillMonster(boolean isInvade) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CROSSWARKILLMONSTER_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CROSSWARKILLMONSTER_BOOLEAN", new Object[] {isInvade});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#crossWarKillPlayer(boolean isInvade)}*/
	public void crossWarKillPlayer(boolean isInvade) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CROSSWARKILLPLAYER_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_CROSSWARKILLPLAYER_BOOLEAN", new Object[] {isInvade});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#dealFriendApply(long friendId, long dealType)}*/
	public void dealFriendApply(long friendId, long dealType) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DEALFRIENDAPPLY_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DEALFRIENDAPPLY_LONG_LONG", new Object[] {friendId, dealType});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#delFriend(long friendId)}*/
	public void delFriend(long friendId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DELFRIEND_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DELFRIEND_LONG", new Object[] {friendId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#denyApply(String guildName)}*/
	public void denyApply(String guildName) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DENYAPPLY_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DENYAPPLY_STRING", new Object[] {guildName});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#doLeaveRep()}*/
	public void doLeaveRep() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DOLEAVEREP,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_DOLEAVEREP", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanObjectService#endShowActivity(List endShowList)}*/
	public void endShowActivity(List endShowList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ENDSHOWACTIVITY_LIST,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ENDSHOWACTIVITY_LIST", new Object[] {endShowList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#eventFire(int eventKey, Param param)}*/
	public void eventFire(int eventKey, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_EVENTFIRE_INT_PARAM,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_EVENTFIRE_INT_PARAM", new Object[] {eventKey, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#factionBossEndEvent()}*/
	public void factionBossEndEvent() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_FACTIONBOSSENDEVENT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_FACTIONBOSSENDEVENT", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#freeSlave(long slaveId)}*/
	public void freeSlave(long slaveId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_FREESLAVE_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_FREESLAVE_LONG", new Object[] {slaveId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#getDebugHumanInfo()}*/
	public void getDebugHumanInfo() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETDEBUGHUMANINFO,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETDEBUGHUMANINFO", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#getFlyPet(long petId)}*/
	public void getFlyPet(long petId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETFLYPET_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETFLYPET_LONG", new Object[] {petId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#getHuman()}*/
	public void getHuman() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMAN,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMAN", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#getHumanBrief(long humanId)}*/
	public void getHumanBrief(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMANBRIEF_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMANBRIEF_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#getHumanObjcetCopy()}*/
	public void getHumanObjcetCopy() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMANOBJCETCOPY,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMANOBJCETCOPY", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#getHumanObjcetMirr()}*/
	public void getHumanObjcetMirr() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMANOBJCETMIRR,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETHUMANOBJCETMIRR", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#getPBattleRoleMsg(boolean containBattleRole)}*/
	public void getPBattleRoleMsg(boolean containBattleRole) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETPBATTLEROLEMSG_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETPBATTLEROLEMSG_BOOLEAN", new Object[] {containBattleRole});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#getTop3InGuildQuestionInnerRank()}*/
	public void getTop3InGuildQuestionInnerRank() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETTOP3INGUILDQUESTIONINNERRANK,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GETTOP3INGUILDQUESTIONINNERRANK", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#giveGift(long friendId)}*/
	public void giveGift(long friendId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GIVEGIFT_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GIVEGIFT_LONG", new Object[] {friendId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#gmCallHumanServiceMethod(Param param)}*/
	public void gmCallHumanServiceMethod(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GMCALLHUMANSERVICEMETHOD_PARAM,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GMCALLHUMANSERVICEMETHOD_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#gm_Gs(int type, int value)}*/
	public void gm_Gs(int type, int value) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GM_GS_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GM_GS_INT_INT", new Object[] {type, value});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#guildGveJoin(int gveSn, int mailSn, long time)}*/
	public void guildGveJoin(int gveSn, int mailSn, long time) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GUILDGVEJOIN_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GUILDGVEJOIN_INT_INT_LONG", new Object[] {gveSn, mailSn, time});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#gveSettle(int gveSn)}*/
	public void gveSettle(int gveSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GVESETTLE_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GVESETTLE_INT", new Object[] {gveSn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#gvgBattleKillEnemy(int killNum)}*/
	public void gvgBattleKillEnemy(int killNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GVGBATTLEKILLENEMY_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_GVGBATTLEKILLENEMY_INT", new Object[] {killNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#handleInviteShare(p_double_chapter_help_player applyInfo)}*/
	public void handleInviteShare(p_double_chapter_help_player applyInfo) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HANDLEINVITESHARE_P_DOUBLE_CHAPTER_HELP_PLAYER,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HANDLEINVITESHARE_P_DOUBLE_CHAPTER_HELP_PLAYER", new Object[] {applyInfo});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#help(int type, int tagetId)}*/
	public void help(int type, int tagetId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HELP_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HELP_INT_INT", new Object[] {type, tagetId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#helpFertilize(long fromHumanId, String fromName, LandVo landVo, int num, int totalAccTime)}*/
	public void helpFertilize(long fromHumanId, String fromName, LandVo landVo, int num, int totalAccTime) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HELPFERTILIZE_LONG_STRING_LANDVO_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HELPFERTILIZE_LONG_STRING_LANDVO_INT_INT", new Object[] {fromHumanId, fromName, landVo, num, totalAccTime});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#humanLoginGameState()}*/
	public void humanLoginGameState() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HUMANLOGINGAMESTATE,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_HUMANLOGINGAMESTATE", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#isInBlockList(long targetId)}*/
	public void isInBlockList(long targetId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ISINBLOCKLIST_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ISINBLOCKLIST_LONG", new Object[] {targetId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#kickClosed()}*/
	public void kickClosed() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KICKCLOSED,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KICKCLOSED", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#kickFactionMemberOut()}*/
	public void kickFactionMemberOut() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KICKFACTIONMEMBEROUT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KICKFACTIONMEMBEROUT", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#kungFuRaceAddBetCoin(int betNum)}*/
	public void kungFuRaceAddBetCoin(int betNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KUNGFURACEADDBETCOIN_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KUNGFURACEADDBETCOIN_INT", new Object[] {betNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#kungFuRaceBattleNewScore(int newScore)}*/
	public void kungFuRaceBattleNewScore(int newScore) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KUNGFURACEBATTLENEWSCORE_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KUNGFURACEBATTLENEWSCORE_INT", new Object[] {newScore});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#kungFuRaceRecycleBetCoin()}*/
	public void kungFuRaceRecycleBetCoin() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KUNGFURACERECYCLEBETCOIN,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_KUNGFURACERECYCLEBETCOIN", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#leave(long stageTargetId)}*/
	public void leave(long stageTargetId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_LEAVE_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_LEAVE_LONG", new Object[] {stageTargetId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#leaveGuild(long guildId)}*/
	public void leaveGuild(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_LEAVEGUILD_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_LEAVEGUILD_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#mailAccept(Mail mail)}*/
	public void mailAccept(Mail mail) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MAILACCEPT_MAIL,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MAILACCEPT_MAIL", new Object[] {mail});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#memberSwitchToMirror(long leaderId, Vector2D leaderPos, long stageId, int mapSn)}*/
	public void memberSwitchToMirror(long leaderId, Vector2D leaderPos, long stageId, int mapSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MEMBERSWITCHTOMIRROR_LONG_VECTOR2D_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MEMBERSWITCHTOMIRROR_LONG_VECTOR2D_LONG_INT", new Object[] {leaderId, leaderPos, stageId, mapSn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#mosterDieToAddQuestProcess(String monsterSn, int mapSn, boolean sharing)}*/
	public void mosterDieToAddQuestProcess(String monsterSn, int mapSn, boolean sharing) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MOSTERDIETOADDQUESTPROCESS_STRING_INT_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MOSTERDIETOADDQUESTPROCESS_STRING_INT_BOOLEAN", new Object[] {monsterSn, mapSn, sharing});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#moveToLeader(long leaderId, long stageId, int mapSn, int repSn, double posX, double posY)}*/
	public void moveToLeader(long leaderId, long stageId, int mapSn, int repSn, double posX, double posY) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MOVETOLEADER_LONG_LONG_INT_INT_DOUBLE_DOUBLE,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MOVETOLEADER_LONG_LONG_INT_INT_DOUBLE_DOUBLE", new Object[] {leaderId, stageId, mapSn, repSn, posX, posY});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#msgHandler(long connId, byte... chunk)}*/
	public void msgHandler(long connId, byte... chunk) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MSGHANDLER_LONG_BYTES,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_MSGHANDLER_LONG_BYTES", new Object[] {connId, chunk});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#notifyGuildPayInfo(int actType, int payCount)}*/
	public void notifyGuildPayInfo(int actType, int payCount) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_NOTIFYGUILDPAYINFO_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_NOTIFYGUILDPAYINFO_INT_INT", new Object[] {actType, payCount});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#onActivityListChange()}*/
	public void onActivityListChange() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONACTIVITYLISTCHANGE,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONACTIVITYLISTCHANGE", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#onCrossWarEnd()}*/
	public void onCrossWarEnd() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONCROSSWAREND,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONCROSSWAREND", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#onHumanJoinTeam(long teamId)}*/
	public void onHumanJoinTeam(long teamId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONHUMANJOINTEAM_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONHUMANJOINTEAM_LONG", new Object[] {teamId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#onHumanLeaveTeam()}*/
	public void onHumanLeaveTeam() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONHUMANLEAVETEAM,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONHUMANLEAVETEAM", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanObjectService#onQuestChange(Set questSn)}*/
	public void onQuestChange(Set questSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONQUESTCHANGE_SET,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONQUESTCHANGE_SET", new Object[] {questSn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#onSchedule(int key, long timeLast)}*/
	public void onSchedule(int key, long timeLast) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONSCHEDULE_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_ONSCHEDULE_INT_LONG", new Object[] {key, timeLast});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanObjectService#openActivity(List activityVoList)}*/
	public void openActivity(List activityVoList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_OPENACTIVITY_LIST,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_OPENACTIVITY_LIST", new Object[] {activityVoList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#pay(String param)}*/
	public void pay(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAY_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAY_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#payGift(String json)}*/
	public void payGift(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAYGIFT_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAYGIFT_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#payGiftNew(PayGift payGiftOld, String json)}*/
	public void payGiftNew(PayGift payGiftOld, String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAYGIFTNEW_PAYGIFT_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAYGIFTNEW_PAYGIFT_STRING", new Object[] {payGiftOld, json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#payTw(String param)}*/
	public void payTw(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAYTW_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PAYTW_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#pockLineProcess(PocketLine pocketLine)}*/
	public void pockLineProcess(PocketLine pocketLine) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_POCKLINEPROCESS_POCKETLINE,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_POCKLINEPROCESS_POCKETLINE", new Object[] {pocketLine});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#produceAdd(int sn, int num, MoneyItemLogKey log, Object... obj)}*/
	public void produceAdd(int sn, int num, MoneyItemLogKey log, Object... obj) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PRODUCEADD_INT_INT_MONEYITEMLOGKEY_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PRODUCEADD_INT_INT_MONEYITEMLOGKEY_OBJECTS", new Object[] {sn, num, log, obj});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanObjectService#produceAdd(Map itemNumMap, MoneyItemLogKey log, Object... obj)}*/
	public void produceAdd(Map itemNumMap, MoneyItemLogKey log, Object... obj) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PRODUCEADD_MAP_MONEYITEMLOGKEY_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_PRODUCEADD_MAP_MONEYITEMLOGKEY_OBJECTS", new Object[] {itemNumMap, log, obj});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link HumanObjectService#receiveCarParkReward(Map reward, boolean isUpgraded, Map mountMap, p_car_park_record p_record, boolean refresh)}*/
	public void receiveCarParkReward(Map reward, boolean isUpgraded, Map mountMap, p_car_park_record p_record, boolean refresh) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RECEIVECARPARKREWARD_MAP_BOOLEAN_MAP_P_CAR_PARK_RECORD_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RECEIVECARPARKREWARD_MAP_BOOLEAN_MAP_P_CAR_PARK_RECORD_BOOLEAN", new Object[] {reward, isUpgraded, mountMap, p_record, refresh});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#receiveFlower(long roleId, int actType, int flowerSn, int num)}*/
	public void receiveFlower(long roleId, int actType, int flowerSn, int num) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RECEIVEFLOWER_LONG_INT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RECEIVEFLOWER_LONG_INT_INT_INT", new Object[] {roleId, actType, flowerSn, num});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#removeItem(int itemSn, int num)}*/
	public void removeItem(int itemSn, int num) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_REMOVEITEM_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_REMOVEITEM_INT_INT", new Object[] {itemSn, num});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#removeMoney(int itemSn, int num)}*/
	public void removeMoney(int itemSn, int num) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_REMOVEMONEY_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_REMOVEMONEY_INT_INT", new Object[] {itemSn, num});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#reserveMethod5(String param)}*/
	public void reserveMethod5(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RESERVEMETHOD5_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RESERVEMETHOD5_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#reserveMethod6(String param)}*/
	public void reserveMethod6(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RESERVEMETHOD6_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RESERVEMETHOD6_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#resetGameSession(String sessionName)}*/
	public void resetGameSession(String sessionName) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RESETGAMESESSION_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_RESETGAMESESSION_STRING", new Object[] {sessionName});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#sealAccount(int type, long timeEnd)}*/
	public void sealAccount(int type, long timeEnd) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SEALACCOUNT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SEALACCOUNT_INT_LONG", new Object[] {type, timeEnd});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#sendChatMsg(Param param)}*/
	public void sendChatMsg(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDCHATMSG_PARAM,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDCHATMSG_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#sendFarmInfo(int reson)}*/
	public void sendFarmInfo(int reson) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDFARMINFO_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDFARMINFO_INT", new Object[] {reson});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#sendFillMail(FillMail fillMail)}*/
	public void sendFillMail(FillMail fillMail) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDFILLMAIL_FILLMAIL,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDFILLMAIL_FILLMAIL", new Object[] {fillMail});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#sendGuildTreasureBoxInfo()}*/
	public void sendGuildTreasureBoxInfo() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDGUILDTREASUREBOXINFO,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDGUILDTREASUREBOXINFO", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#sendGveKillMail(int gveSn, int mailSn)}*/
	public void sendGveKillMail(int gveSn, int mailSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDGVEKILLMAIL_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDGVEKILLMAIL_INT_INT", new Object[] {gveSn, mailSn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#sendMsgParam(Param param)}*/
	public void sendMsgParam(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDMSGPARAM_PARAM,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SENDMSGPARAM_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#setSilenceEndTime()}*/
	public void setSilenceEndTime() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SETSILENCEENDTIME,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SETSILENCEENDTIME", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#setTeamId(long teamId)}*/
	public void setTeamId(long teamId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SETTEAMID_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SETTEAMID_LONG", new Object[] {teamId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#silence(long keepTime)}*/
	public void silence(long keepTime) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SILENCE_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SILENCE_LONG", new Object[] {keepTime});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#stealCompleted(FarmVo farmVo, int landId)}*/
	public void stealCompleted(FarmVo farmVo, int landId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_STEALCOMPLETED_FARMVO_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_STEALCOMPLETED_FARMVO_INT", new Object[] {farmVo, landId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#stealFaild(long farmId, LandVo landVo)}*/
	public void stealFaild(long farmId, LandVo landVo) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_STEALFAILD_LONG_LANDVO,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_STEALFAILD_LONG_LANDVO", new Object[] {farmId, landVo});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#stolenCompleted(FarmVo farmVo, int landId, RobberVo robberVo)}*/
	public void stolenCompleted(FarmVo farmVo, int landId, RobberVo robberVo) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_STOLENCOMPLETED_FARMVO_INT_ROBBERVO,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_STOLENCOMPLETED_FARMVO_INT_ROBBERVO", new Object[] {farmVo, landId, robberVo});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#switchTo(long stageId, Object... params)}*/
	public void switchTo(long stageId, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SWITCHTO_LONG_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_SWITCHTO_LONG_OBJECTS", new Object[] {stageId, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#teamChange(String leaderName, long leaderId)}*/
	public void teamChange(String leaderName, long leaderId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEAMCHANGE_STRING_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEAMCHANGE_STRING_LONG", new Object[] {leaderName, leaderId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#test01(String str)}*/
	public void test01(String str) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST01_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST01_STRING", new Object[] {str});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#test02(String str)}*/
	public void test02(String str) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST02_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST02_STRING", new Object[] {str});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#test03(String str)}*/
	public void test03(String str) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST03_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST03_STRING", new Object[] {str});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#test04(String str)}*/
	public void test04(String str) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST04_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TEST04_STRING", new Object[] {str});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#triggerMsgIdOperate(int msgId)}*/
	public void triggerMsgIdOperate(int msgId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TRIGGERMSGIDOPERATE_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_TRIGGERMSGIDOPERATE_INT", new Object[] {msgId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#update1(String json)}*/
	public void update1(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE1_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE1_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#update2(Object... objs)}*/
	public void update2(Object... objs) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE2_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE2_OBJECTS", new Object[] {objs});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#update3(Param param)}*/
	public void update3(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE3_PARAM,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE3_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#update4(String json)}*/
	public void update4(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE4_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATE4_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#updateBuildPorpCalc()}*/
	public void updateBuildPorpCalc() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEBUILDPORPCALC,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEBUILDPORPCALC", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#updateDoubleChapterHelpList(p_double_chapter_help_player help, boolean b)}*/
	public void updateDoubleChapterHelpList(p_double_chapter_help_player help, boolean b) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEDOUBLECHAPTERHELPLIST_P_DOUBLE_CHAPTER_HELP_PLAYER_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEDOUBLECHAPTERHELPLIST_P_DOUBLE_CHAPTER_HELP_PLAYER_BOOLEAN", new Object[] {help, b});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#updateFlyHybridPartner(long partnerId, int opt)}*/
	public void updateFlyHybridPartner(long partnerId, int opt) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEFLYHYBRIDPARTNER_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEFLYHYBRIDPARTNER_LONG_INT", new Object[] {partnerId, opt});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#updateFlyPetBorrowHybrid(long petId)}*/
	public void updateFlyPetBorrowHybrid(long petId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEFLYPETBORROWHYBRID_LONG,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEFLYPETBORROWHYBRID_LONG", new Object[] {petId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#updateGuildInfo(Guild guild)}*/
	public void updateGuildInfo(Guild guild) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEGUILDINFO_GUILD,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEGUILDINFO_GUILD", new Object[] {guild});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#updateGuildPosition(int guildPosition)}*/
	public void updateGuildPosition(int guildPosition) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEGUILDPOSITION_INT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATEGUILDPOSITION_INT", new Object[] {guildPosition});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#updateStageHistory(String stageHistory)}*/
	public void updateStageHistory(String stageHistory) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATESTAGEHISTORY_STRING,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_UPDATESTAGEHISTORY_STRING", new Object[] {stageHistory});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HumanObjectService#worldBossEndEvent()}*/
	public void worldBossEndEvent() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_WORLDBOSSENDEVENT,"ORG_GOF_DEMO_WORLDSRV_CHARACTER_HUMANOBJECTSERVICE_WORLDBOSSENDEVENT", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}
}
