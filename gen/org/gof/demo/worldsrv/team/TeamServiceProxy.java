package org.gof.demo.worldsrv.team;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import java.util.List;
import java.util.Map;
import org.gof.demo.worldsrv.team.TeamMember;
import org.gof.demo.worldsrv.team.TeamInfo;
import org.gof.demo.worldsrv.human.HumanData;

@GofGenFile
public final class TeamServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_ADDAPPLYID_LONG_LONG_LONG = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKMETHOD1_STRING = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKMETHOD2_STRING = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKUPMETHOD3_STRING = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKUPMETHOD4_STRING = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_CHECKTEAMID_LONG_LONG = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_CHOOSEPET_LONG_LONG_LIST = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_CREATETEAM_LONG_INT_TEAMMEMBER_INT = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_GETTEAM_LONG = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_REMOVETEAMMEMBER_LONG = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMAPPLY_LONG_LONG = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMCHOOSEDIFF_LONG_LONG_INT = 12;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMEXIT_LONG_LONG = 13;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMJOIN_LONG_LONG_INT = 14;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMMATCH_LONG_LONG_INT = 15;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMMAXDIFF_LONG_LONG_INT = 16;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMREADY_LONG_LONG_BOOLEAN = 17;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_UPDATETEAMMEMBER_LONG_TEAMMEMBER = 18;
		public static final int ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_UPDATETEAMMEMBER_TEAMMEMBER = 19;
	}

	private static final String SERV_ID = "team";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private TeamServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		TeamService serv = (TeamService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_ADDAPPLYID_LONG_LONG_LONG: {
				return (GofFunction3<Long, Long, Long>)serv::addApplyId;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKMETHOD1_STRING: {
				return (GofFunction1<String>)serv::backMethod1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKMETHOD2_STRING: {
				return (GofFunction1<String>)serv::backMethod2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKUPMETHOD3_STRING: {
				return (GofFunction1<String>)serv::backupMethod3;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKUPMETHOD4_STRING: {
				return (GofFunction1<String>)serv::backupMethod4;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_CHECKTEAMID_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::checkTeamId;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_CHOOSEPET_LONG_LONG_LIST: {
				return (GofFunction3<Long, Long, List>)serv::choosePet;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_CREATETEAM_LONG_INT_TEAMMEMBER_INT: {
				return (GofFunction4<Long, Integer, TeamMember, Integer>)serv::createTeam;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_GETTEAM_LONG: {
				return (GofFunction1<Long>)serv::getTeam;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_REMOVETEAMMEMBER_LONG: {
				return (GofFunction1<Long>)serv::removeTeamMember;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMAPPLY_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::teamApply;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMCHOOSEDIFF_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::teamChooseDiff;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMEXIT_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::teamExit;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMJOIN_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::teamJoin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMMATCH_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::teamMatch;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMMAXDIFF_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::teamMaxDiff;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMREADY_LONG_LONG_BOOLEAN: {
				return (GofFunction3<Long, Long, Boolean>)serv::teamReady;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_UPDATETEAMMEMBER_LONG_TEAMMEMBER: {
				return (GofFunction2<Long, TeamMember>)serv::updateTeamMember;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_UPDATETEAMMEMBER_TEAMMEMBER: {
				return (GofFunction1<TeamMember>)serv::updateTeamMember;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static TeamServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static TeamServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static TeamServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static TeamServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static TeamServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static TeamServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static TeamServiceProxy createInstance(String node, String port, Object serviceId) {
		TeamServiceProxy inst = new TeamServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link TeamService#addApplyId(long teamId, long humanId, long applyId)}*/
	public void addApplyId(long teamId, long humanId, long applyId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_ADDAPPLYID_LONG_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_ADDAPPLYID_LONG_LONG_LONG", new Object[] {teamId, humanId, applyId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#backMethod1(String param)}*/
	public void backMethod1(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKMETHOD1_STRING,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKMETHOD1_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#backMethod2(String param)}*/
	public void backMethod2(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKMETHOD2_STRING,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKMETHOD2_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#backupMethod3(String param)}*/
	public void backupMethod3(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKUPMETHOD3_STRING,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKUPMETHOD3_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#backupMethod4(String param)}*/
	public void backupMethod4(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKUPMETHOD4_STRING,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_BACKUPMETHOD4_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#checkTeamId(long teamId, long humanId)}*/
	public void checkTeamId(long teamId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_CHECKTEAMID_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_CHECKTEAMID_LONG_LONG", new Object[] {teamId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link TeamService#choosePet(long teamId, long humanId, List petListList)}*/
	public void choosePet(long teamId, long humanId, List petListList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_CHOOSEPET_LONG_LONG_LIST,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_CHOOSEPET_LONG_LONG_LIST", new Object[] {teamId, humanId, petListList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#createTeam(long humanId, int type, TeamMember memberNew, int diff)}*/
	public void createTeam(long humanId, int type, TeamMember memberNew, int diff) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_CREATETEAM_LONG_INT_TEAMMEMBER_INT,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_CREATETEAM_LONG_INT_TEAMMEMBER_INT", new Object[] {humanId, type, memberNew, diff});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#getTeam(long teamId)}*/
	public void getTeam(long teamId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_GETTEAM_LONG,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_GETTEAM_LONG", new Object[] {teamId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#removeTeamMember(long humanId)}*/
	public void removeTeamMember(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_REMOVETEAMMEMBER_LONG,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_REMOVETEAMMEMBER_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#teamApply(long humanId, long teamId)}*/
	public void teamApply(long humanId, long teamId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMAPPLY_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMAPPLY_LONG_LONG", new Object[] {humanId, teamId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#teamChooseDiff(long teamId, long humanId, int diff)}*/
	public void teamChooseDiff(long teamId, long humanId, int diff) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMCHOOSEDIFF_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMCHOOSEDIFF_LONG_LONG_INT", new Object[] {teamId, humanId, diff});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#teamExit(long teamId, long humanId)}*/
	public void teamExit(long teamId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMEXIT_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMEXIT_LONG_LONG", new Object[] {teamId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#teamJoin(long teamId, long humanId, int difficulty)}*/
	public void teamJoin(long teamId, long humanId, int difficulty) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMJOIN_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMJOIN_LONG_LONG_INT", new Object[] {teamId, humanId, difficulty});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#teamMatch(long teamId, long humanId, int diff)}*/
	public void teamMatch(long teamId, long humanId, int diff) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMMATCH_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMMATCH_LONG_LONG_INT", new Object[] {teamId, humanId, diff});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#teamMaxDiff(long teamId, long humanId, int diff)}*/
	public void teamMaxDiff(long teamId, long humanId, int diff) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMMAXDIFF_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMMAXDIFF_LONG_LONG_INT", new Object[] {teamId, humanId, diff});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#teamReady(long teamId, long humanId, boolean isReady)}*/
	public void teamReady(long teamId, long humanId, boolean isReady) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMREADY_LONG_LONG_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_TEAMREADY_LONG_LONG_BOOLEAN", new Object[] {teamId, humanId, isReady});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#updateTeamMember(long teamId, TeamMember member)}*/
	public void updateTeamMember(long teamId, TeamMember member) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_UPDATETEAMMEMBER_LONG_TEAMMEMBER,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_UPDATETEAMMEMBER_LONG_TEAMMEMBER", new Object[] {teamId, member});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link TeamService#updateTeamMember(TeamMember member)}*/
	public void updateTeamMember(TeamMember member) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_UPDATETEAMMEMBER_TEAMMEMBER,"ORG_GOF_DEMO_WORLDSRV_TEAM_TEAMSERVICE_UPDATETEAMMEMBER_TEAMMEMBER", new Object[] {member});
		if(immutableOnce) immutableOnce = false;
	}
}
