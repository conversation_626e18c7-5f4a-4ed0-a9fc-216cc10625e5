package org.gof.demo.worldsrv.httpPush;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import java.util.List;

@GofGenFile
public final class HttpPushServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_HTTPPUSH_HTTPPUSHSERVICE_HUMANLOGIN_LONG_INT_LONG = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_HTTPPUSH_HTTPPUSHSERVICE_HUMANLOGOUT_LONG_INT_LONG = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_HTTPPUSH_HTTPPUSHSERVICE_SETSNNEXTPUSHTIMEMAP_INT_INT = 3;
	}

	private static final String SERV_ID = "httpPush";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private HttpPushServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings("unchecked")
	public Object getMethodFunction(Service service, int methodKey) {
		HttpPushService serv = (HttpPushService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HTTPPUSH_HTTPPUSHSERVICE_HUMANLOGIN_LONG_INT_LONG: {
				return (GofFunction3<Long, Integer, Long>)serv::humanLogin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HTTPPUSH_HTTPPUSHSERVICE_HUMANLOGOUT_LONG_INT_LONG: {
				return (GofFunction3<Long, Integer, Long>)serv::humanLogout;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_HTTPPUSH_HTTPPUSHSERVICE_SETSNNEXTPUSHTIMEMAP_INT_INT: {
				return (GofFunction2<Integer, Integer>)serv::setSnNextPushTimeMap;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static HttpPushServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static HttpPushServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static HttpPushServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static HttpPushServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static HttpPushServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static HttpPushServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static HttpPushServiceProxy createInstance(String node, String port, Object serviceId) {
		HttpPushServiceProxy inst = new HttpPushServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link HttpPushService#humanLogin(long humanId, int serverID, long lastLogoutTime)}*/
	public void humanLogin(long humanId, int serverID, long lastLogoutTime) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HTTPPUSH_HTTPPUSHSERVICE_HUMANLOGIN_LONG_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_HTTPPUSH_HTTPPUSHSERVICE_HUMANLOGIN_LONG_INT_LONG", new Object[] {humanId, serverID, lastLogoutTime});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HttpPushService#humanLogout(long humanId, int serverID, Long logoutTime)}*/
	public void humanLogout(long humanId, int serverID, Long logoutTime) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HTTPPUSH_HTTPPUSHSERVICE_HUMANLOGOUT_LONG_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_HTTPPUSH_HTTPPUSHSERVICE_HUMANLOGOUT_LONG_INT_LONG", new Object[] {humanId, serverID, logoutTime});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link HttpPushService#setSnNextPushTimeMap(int sn, int nowAddTime)}*/
	public void setSnNextPushTimeMap(int sn, int nowAddTime) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_HTTPPUSH_HTTPPUSHSERVICE_SETSNNEXTPUSHTIMEMAP_INT_INT,"ORG_GOF_DEMO_WORLDSRV_HTTPPUSH_HTTPPUSHSERVICE_SETSNNEXTPUSHTIMEMAP_INT_INT", new Object[] {sn, nowAddTime});
		if(immutableOnce) immutableOnce = false;
	}
}
