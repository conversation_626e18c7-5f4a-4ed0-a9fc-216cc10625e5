package org.gof.demo.worldsrv.entity;


import co.paralleluniverse.fibers.Suspendable;
import org.apache.commons.lang3.exception.ExceptionUtils;

import org.gof.core.*;
import org.gof.core.db.DBConsts;
import org.gof.core.dbsrv.DB;
import org.gof.core.support.BufferPool;
import org.gof.core.support.S;
import org.gof.core.support.SysException;
import org.gof.core.support.log.LogCore;
import org.gof.core.entity.EntityBase;
import org.gof.core.gen.GofGenFile;
import io.vertx.core.json.JsonObject;
import org.gof.core.entity.EntityBase;
import org.gof.core.gen.GofGenFile;

import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@GofGenFile
public final class Human3 extends EntityBase {
	public static final String tableName = "demo_human3";
	public static final boolean autoCache = false;
	public static final String LISTKEY = "";

	public static final int REDIS_EXPIRE_TIME = 0;// redis过期时间

	public static final int UPDATE_DB_TYPE = 0;// 数据入库类型 0队列入库 1实时入库 2不入库

	public static long redisSyncTimeInterval = 0L;

	
	
	
	/**
	 * 属性关键字
	 */
	public static final class K {
		public static final String id = "id";	//id
		public static final String language = "language";	//语言
		public static final String timeSecOnline = "timeSecOnline";	//在线时间
		public static final String sessionKey = "sessionKey";	//角色SessionKey
		public static final String defaultEquipFigureMap = "defaultEquipFigureMap";	//默认装备外观map<部位，sn>
		public static final String atomicClock = "atomicClock";	//对时器
		public static final String totalRMB = "totalRMB";	//累计充值RMB
		public static final String chargeGold = "chargeGold";	//累计充值金额
		public static final String chargeGoldSpecial = "chargeGoldSpecial";	//累计充值金额(特殊)
		public static final String topCombat = "topCombat";	//最高战斗力
		public static final String silenceEndTime = "silenceEndTime";	//禁言截止时间
		public static final String trumpetEndTime = "trumpetEndTime";	//喇叭禁言截止时间
		public static final String vipLv = "vipLv";	//vip等级
		public static final String propVerNum = "propVerNum";	//属性版本号
		public static final String stageHistory = "stageHistory";	//地图位置信息
		public static final String stageRelive = "stageRelive";	//复活点位置信息
		public static final String bridgeStageHistory = "bridgeStageHistory";	//所在跨服地图位置信息，List<List<Integer>>,id,sn,x,y,地图类型,进入地图的时间戳
		public static final String bridgeStageIn = "bridgeStageIn";	//是否在跨服
		public static final String scene = "scene";	//当前所在场景
		public static final String unlimited = "unlimited";	//是否无限
		public static final String repTypeDifficultyMap = "repTypeDifficultyMap";	//副本类型对应通关难度
		public static final String repTypeTenDiffMap = "repTypeTenDiffMap";	//副本类型对应通关难度(组队十点防御值通关)
		public static final String repTypeNumMap = "repTypeNumMap";	//副本类型对应累计通过次数
		public static final String repTypeList = "repTypeList";	//解锁副本类型list
		public static final String totalBoxNum = "totalBoxNum";	//累计开箱次数（神灯）
		public static final String totalStatueNum = "totalStatueNum";	//累计启灵次数（雕像）
		public static final String skillSnList = "skillSnList";	//拥有的技能sn
		public static final String skillIllustratedMap = "skillIllustratedMap";	//技能图鉴:sn,等级
		public static final String skillTabNameJSON = "skillTabNameJSON";	//技能方案名字
		public static final String lotterySkillLv = "lotterySkillLv";	//抽技能等级
		public static final String totalCardSkillNum = "totalCardSkillNum";	//累计抽技能次数
		public static final String addLvSkill = "addLvSkill";	//是否强化过技能
		public static final String petIllustratedMap = "petIllustratedMap";	//同伴图鉴:sn,等级
		public static final String petTabNameJSON = "petTabNameJSON";	//同伴方案名字
		public static final String lotteryPetLv = "lotteryPetLv";	//抽同伴等级
		public static final String totalCardPetNum = "totalCardPetNum";	//累计抽同伴次数
		public static final String addLvPet = "addLvPet";	//是否强化过同伴
		public static final String guildBossTime = "guildBossTime";	//征战公会boss时间
		public static final String guildBossNum = "guildBossNum";	//boss次数
		public static final String modPowerJSON = "modPowerJSON";	//功能模块,战力
		public static final String arenaGradingNum = "arenaGradingNum";	//定级赛次数
		public static final String arenaGradingWinNum = "arenaGradingWinNum";	//定级赛胜利次数
		public static final String arenaBridgeGrad = "arenaBridgeGrad";	//排位赛段位
		public static final String arenaGradingEnd = "arenaGradingEnd";	//定级赛是否结束
		public static final String arenaGradingEndTime = "arenaGradingEndTime";	//定级赛结束时间
		public static final String checkRobot = "checkRobot";	//是否检测机器人
		public static final String gm = "gm";	//是否有gm权限
		public static final String gveSnList = "gveSnList";	//gveSn
		public static final String previewNum = "previewNum";	//预告奖励领取次数
		public static final String guildDonateNum = "guildDonateNum";	//累计公会捐献次数
		public static final String emojiSnList = "emojiSnList";	//表情包sn
		public static final String emojiSnExpiredMap = "emojiSnExpiredMap";	//表情包sn和过期时间（0永久不在此）
		public static final String isCreateReward = "isCreateReward";	//创角奖励是否发放
		public static final String fixVersion = "fixVersion";	//修复版本
		public static final String fateCurrCost = "fateCurrCost";	//邪眼升级材料当前消耗值，会随重置和分解回退
		public static final String fateMaxCost = "fateMaxCost";	//邪眼升级材料历史最大消耗值
		public static final String flags = "flags";	//常用的boolean类型变量
		public static final String zeroResetTime = "zeroResetTime";	//0点重置时间
		public static final String backLampFinishedIds = "backLampFinishedIds";	//小游戏已完成的章节sn
		public static final String backLampReceivedIds = "backLampReceivedIds";	//小游戏已领奖的章节sn
	}

	@Override
	public String getTableName() {
		return tableName;
	}

	@Override
	public boolean isAutoCache(){
        return autoCache;
    }
	
	public Human3() {
		super();
		setLanguage(1);
		setTimeSecOnline(0);
		setSessionKey(0L);
		setDefaultEquipFigureMap("{}");
		setAtomicClock("{}");
		setTotalRMB(0);
		setChargeGold(0);
		setChargeGoldSpecial(0);
		setTopCombat("1");
		setSilenceEndTime(0);
		setTrumpetEndTime(0);
		setVipLv(0);
		setPropVerNum(0);
		setStageHistory("[]");
		setStageRelive("[]");
		setBridgeStageHistory("");
		setBridgeStageIn(false);
		setScene(0);
		setUnlimited(0);
		setRepTypeDifficultyMap("");
		setRepTypeTenDiffMap("");
		setRepTypeNumMap("");
		setRepTypeList("");
		setTotalBoxNum(0);
		setTotalStatueNum(0);
		setSkillSnList("");
		setSkillIllustratedMap("");
		setSkillTabNameJSON("");
		setLotterySkillLv(1);
		setTotalCardSkillNum(0);
		setAddLvSkill(false);
		setPetIllustratedMap("");
		setPetTabNameJSON("");
		setLotteryPetLv(1);
		setTotalCardPetNum(0);
		setAddLvPet(false);
		setGuildBossTime(0L);
		setGuildBossNum(2);
		setModPowerJSON("");
		setArenaGradingNum(0);
		setArenaGradingWinNum(0);
		setArenaBridgeGrad(0);
		setArenaGradingEnd(false);
		setArenaGradingEndTime(0L);
		setCheckRobot(false);
		setGm(false);
		setGveSnList("");
		setPreviewNum(0);
		setGuildDonateNum(0);
		setEmojiSnList("");
		setEmojiSnExpiredMap("");
		setIsCreateReward(false);
		setFixVersion(0);
		setFateCurrCost(0);
		setFateMaxCost(0);
		setFlags(0);
		setZeroResetTime(0);
		setBackLampFinishedIds("[]");
		setBackLampReceivedIds("[]");
	}

	public Human3(Record record) {
		super(record);
	}

	
	/**
	 * 新增数据
	 */
	@Override
	public void persist() {
		
		if(getId() == 0){
			setTableId();
		}
		insertNew();
		
		//状态错误
		if(record.getStatus() != DBConsts.RECORD_STATUS_NEW) {
			LogCore.db.error("只有新增包能调用persist函数，请确认状态：data={}, stackTrace={}", this, ExceptionUtils.getStackTrace(new Throwable()));
			return;
		}
		
		DB prx = DB.newInstance(getTableName());
		prx.insert(record);
		
		//重置状态
		record.resetStatus();
	}

	/**
	 * 同步修改数据至DB服务器
	 * 默认不立即持久化到数据库
	 */
	@Override
	public void update() {
		update(false);
	}

	@Override
	public void updateRedis(boolean sync) {
		if(redisSyncTimeInterval == 0L){
			redisSyncTimeInterval = Port.getTime();
		}
		if(sync || (getUpdateObj() != null && Port.getTime() - redisSyncTimeInterval > 5 * 1000L)){// 避免关服瞬间所有玩家一次性写入太多
			redisSyncTimeInterval = Port.getTime();
			updateNew();//别用实时入库（会有先后顺序问题），如果需要则把入库类型改了
		}
	}

	@Override
	public void updateDB(boolean sync) {

		//新增包不能直接调用update函数 请先调用persist
		if(record.getStatus() == DBConsts.RECORD_STATUS_NEW) {
			throw new SysException("新增包不能直接调用update函数，请先调用persist：data={}", this);
		}

		//升级包
		Chunk patch = record.patchUpdateGen();
		if(patch == null || patch.length == 0) return;

		//将升级包同步至DB服务器
		DB prx = DB.newInstance(getTableName());
		prx.update(getId(), patch, sync);

		//回收缓冲包
		patch.release();

		//重置状态
		record.resetStatus();
	}
	
	/**
	 * 同步修改数据至DB服务器
	 * @param sync 是否立即同持久化到数据库
	 */
	@Override
	public void update(boolean sync) {
		
		//新增包不能直接调用update函数 请先调用persist
		if(record.getStatus() == DBConsts.RECORD_STATUS_NEW) {
			throw new SysException("新增包不能直接调用update函数，请先调用persist：data={}", this);
		}
		
		if(getUpdateObj() != null){
			redisSyncTimeInterval = Port.getTime();
			updateNew();//别用实时入库（会有先后顺序问题），如果需要则把入库类型改了
		}
		
		//升级包
		Chunk patch = record.patchUpdateGen();
		if(patch == null || patch.length == 0) return;

		//将升级包同步至DB服务器
		DB prx = DB.newInstance(getTableName());
		prx.update(getId(), patch, sync);
		
		//回收缓冲包
		patch.release();
		
		//重置状态
		record.resetStatus();
	}

	/**
	 * 删除数据
	 */
	@Override
	public void remove() {
		
		deleteNew();
	
		DB prx = DB.newInstance(getTableName());
		prx.delete(getId());
	}

	public void reset() {
		super.reset();
		record.setNewness(false);
		record.resetStatus();
	}

	protected String getKey() {
		return "Human3." + getId();
	}

	

	protected String getListKey() {
		return null;
	}
	
	protected String getListItemKey() {
		return null;
	}

	

	public void doCreate() {
		setTableId();
		insertNew();
	}

	public void setTableId() {
		setId(incrTableId("demo_human3"));
	}

	public JsonObject insertNew() {
		return super.insert(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE, LISTKEY, getListKey(), getListItemKey(), false, tableName);
	}

	public void deleteNew() {
		super.delete(getKey(), getId(), UPDATE_DB_TYPE, 0, LISTKEY, getListKey(), getListItemKey(), false, tableName);
	}

	/**
	* 根据入库类型更新
	*/
	public JsonObject updateNew() {
		return super.update(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE, 0, getAllObjNew(), LISTKEY, getListKey(), getListItemKey(), tableName);
	}

	/**
	* 实时入库
	*/
	public JsonObject updateNowNew() {
		return super.update(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE_NOW, 0, getAllObjNew(), LISTKEY, getListKey(), getListItemKey(), tableName);
	}

	public JsonObject getAllObjNew(){
		JsonObject obj = super.getAllObjNew();
		obj.put(K.id, getId()); // id
		obj.put(K.language, getLanguage()); // language
		obj.put(K.timeSecOnline, getTimeSecOnline()); // timeSecOnline
		obj.put(K.sessionKey, getSessionKey()); // sessionKey
		obj.put(K.defaultEquipFigureMap, getDefaultEquipFigureMap()); // defaultEquipFigureMap
		obj.put(K.atomicClock, getAtomicClock()); // atomicClock
		obj.put(K.totalRMB, getTotalRMB()); // totalRMB
		obj.put(K.chargeGold, getChargeGold()); // chargeGold
		obj.put(K.chargeGoldSpecial, getChargeGoldSpecial()); // chargeGoldSpecial
		obj.put(K.topCombat, getTopCombat()); // topCombat
		obj.put(K.silenceEndTime, getSilenceEndTime()); // silenceEndTime
		obj.put(K.trumpetEndTime, getTrumpetEndTime()); // trumpetEndTime
		obj.put(K.vipLv, getVipLv()); // vipLv
		obj.put(K.propVerNum, getPropVerNum()); // propVerNum
		obj.put(K.stageHistory, getStageHistory()); // stageHistory
		obj.put(K.stageRelive, getStageRelive()); // stageRelive
		obj.put(K.bridgeStageHistory, getBridgeStageHistory()); // bridgeStageHistory
		obj.put(K.bridgeStageIn, isBridgeStageIn()); // bridgeStageIn
		obj.put(K.scene, getScene()); // scene
		obj.put(K.unlimited, getUnlimited()); // unlimited
		obj.put(K.repTypeDifficultyMap, getRepTypeDifficultyMap()); // repTypeDifficultyMap
		obj.put(K.repTypeTenDiffMap, getRepTypeTenDiffMap()); // repTypeTenDiffMap
		obj.put(K.repTypeNumMap, getRepTypeNumMap()); // repTypeNumMap
		obj.put(K.repTypeList, getRepTypeList()); // repTypeList
		obj.put(K.totalBoxNum, getTotalBoxNum()); // totalBoxNum
		obj.put(K.totalStatueNum, getTotalStatueNum()); // totalStatueNum
		obj.put(K.skillSnList, getSkillSnList()); // skillSnList
		obj.put(K.skillIllustratedMap, getSkillIllustratedMap()); // skillIllustratedMap
		obj.put(K.skillTabNameJSON, getSkillTabNameJSON()); // skillTabNameJSON
		obj.put(K.lotterySkillLv, getLotterySkillLv()); // lotterySkillLv
		obj.put(K.totalCardSkillNum, getTotalCardSkillNum()); // totalCardSkillNum
		obj.put(K.addLvSkill, isAddLvSkill()); // addLvSkill
		obj.put(K.petIllustratedMap, getPetIllustratedMap()); // petIllustratedMap
		obj.put(K.petTabNameJSON, getPetTabNameJSON()); // petTabNameJSON
		obj.put(K.lotteryPetLv, getLotteryPetLv()); // lotteryPetLv
		obj.put(K.totalCardPetNum, getTotalCardPetNum()); // totalCardPetNum
		obj.put(K.addLvPet, isAddLvPet()); // addLvPet
		obj.put(K.guildBossTime, getGuildBossTime()); // guildBossTime
		obj.put(K.guildBossNum, getGuildBossNum()); // guildBossNum
		obj.put(K.modPowerJSON, getModPowerJSON()); // modPowerJSON
		obj.put(K.arenaGradingNum, getArenaGradingNum()); // arenaGradingNum
		obj.put(K.arenaGradingWinNum, getArenaGradingWinNum()); // arenaGradingWinNum
		obj.put(K.arenaBridgeGrad, getArenaBridgeGrad()); // arenaBridgeGrad
		obj.put(K.arenaGradingEnd, isArenaGradingEnd()); // arenaGradingEnd
		obj.put(K.arenaGradingEndTime, getArenaGradingEndTime()); // arenaGradingEndTime
		obj.put(K.checkRobot, isCheckRobot()); // checkRobot
		obj.put(K.gm, isGm()); // gm
		obj.put(K.gveSnList, getGveSnList()); // gveSnList
		obj.put(K.previewNum, getPreviewNum()); // previewNum
		obj.put(K.guildDonateNum, getGuildDonateNum()); // guildDonateNum
		obj.put(K.emojiSnList, getEmojiSnList()); // emojiSnList
		obj.put(K.emojiSnExpiredMap, getEmojiSnExpiredMap()); // emojiSnExpiredMap
		obj.put(K.isCreateReward, isIsCreateReward()); // isCreateReward
		obj.put(K.fixVersion, getFixVersion()); // fixVersion
		obj.put(K.fateCurrCost, getFateCurrCost()); // fateCurrCost
		obj.put(K.fateMaxCost, getFateMaxCost()); // fateMaxCost
		obj.put(K.flags, getFlags()); // flags
		obj.put(K.zeroResetTime, getZeroResetTime()); // zeroResetTime
		obj.put(K.backLampFinishedIds, getBackLampFinishedIds()); // backLampFinishedIds
		obj.put(K.backLampReceivedIds, getBackLampReceivedIds()); // backLampReceivedIds
		return obj;
	}

	/**
	 * id
	 */
	public long getId() {
		return record.get(K.id);
	}

	public void setId(final long id) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.id, id);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.id, id);
	}
	/**
	 * 语言
	 */
	public int getLanguage() {
		return record.get(K.language);
	}

	public void setLanguage(final int language) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.language, language);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.language, language);
	}
	/**
	 * 在线时间
	 */
	public int getTimeSecOnline() {
		return record.get(K.timeSecOnline);
	}

	public void setTimeSecOnline(final int timeSecOnline) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.timeSecOnline, timeSecOnline);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.timeSecOnline, timeSecOnline);
	}
	/**
	 * 角色SessionKey
	 */
	public long getSessionKey() {
		return record.get(K.sessionKey);
	}

	public void setSessionKey(final long sessionKey) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.sessionKey, sessionKey);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.sessionKey, sessionKey);
	}
	/**
	 * 默认装备外观map<部位，sn>
	 */
	public String getDefaultEquipFigureMap() {
		return record.get(K.defaultEquipFigureMap);
	}

	public void setDefaultEquipFigureMap(final String defaultEquipFigureMap) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.defaultEquipFigureMap, defaultEquipFigureMap);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.defaultEquipFigureMap, defaultEquipFigureMap);
	}
	/**
	 * 对时器
	 */
	public String getAtomicClock() {
		return record.get(K.atomicClock);
	}

	public void setAtomicClock(final String atomicClock) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.atomicClock, atomicClock);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.atomicClock, atomicClock);
	}
	/**
	 * 累计充值RMB
	 */
	public long getTotalRMB() {
		return record.get(K.totalRMB);
	}

	public void setTotalRMB(final long totalRMB) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.totalRMB, totalRMB);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.totalRMB, totalRMB);
	}
	/**
	 * 累计充值金额
	 */
	public long getChargeGold() {
		return record.get(K.chargeGold);
	}

	public void setChargeGold(final long chargeGold) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.chargeGold, chargeGold);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.chargeGold, chargeGold);
	}
	/**
	 * 累计充值金额(特殊)
	 */
	public long getChargeGoldSpecial() {
		return record.get(K.chargeGoldSpecial);
	}

	public void setChargeGoldSpecial(final long chargeGoldSpecial) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.chargeGoldSpecial, chargeGoldSpecial);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.chargeGoldSpecial, chargeGoldSpecial);
	}
	/**
	 * 最高战斗力
	 */
	public String getTopCombat() {
		return record.get(K.topCombat);
	}

	public void setTopCombat(final String topCombat) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.topCombat, topCombat);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.topCombat, topCombat);
	}
	/**
	 * 禁言截止时间
	 */
	public long getSilenceEndTime() {
		return record.get(K.silenceEndTime);
	}

	public void setSilenceEndTime(final long silenceEndTime) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.silenceEndTime, silenceEndTime);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.silenceEndTime, silenceEndTime);
	}
	/**
	 * 喇叭禁言截止时间
	 */
	public long getTrumpetEndTime() {
		return record.get(K.trumpetEndTime);
	}

	public void setTrumpetEndTime(final long trumpetEndTime) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.trumpetEndTime, trumpetEndTime);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.trumpetEndTime, trumpetEndTime);
	}
	/**
	 * vip等级
	 */
	public int getVipLv() {
		return record.get(K.vipLv);
	}

	public void setVipLv(final int vipLv) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.vipLv, vipLv);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.vipLv, vipLv);
	}
	/**
	 * 属性版本号
	 */
	public int getPropVerNum() {
		return record.get(K.propVerNum);
	}

	public void setPropVerNum(final int propVerNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.propVerNum, propVerNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.propVerNum, propVerNum);
	}
	/**
	 * 地图位置信息
	 */
	public String getStageHistory() {
		return record.get(K.stageHistory);
	}

	public void setStageHistory(final String stageHistory) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.stageHistory, stageHistory);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.stageHistory, stageHistory);
	}
	/**
	 * 复活点位置信息
	 */
	public String getStageRelive() {
		return record.get(K.stageRelive);
	}

	public void setStageRelive(final String stageRelive) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.stageRelive, stageRelive);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.stageRelive, stageRelive);
	}
	/**
	 * 所在跨服地图位置信息，List<List<Integer>>,id,sn,x,y,地图类型,进入地图的时间戳
	 */
	public String getBridgeStageHistory() {
		return record.get(K.bridgeStageHistory);
	}

	public void setBridgeStageHistory(final String bridgeStageHistory) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.bridgeStageHistory, bridgeStageHistory);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.bridgeStageHistory, bridgeStageHistory);
	}
	/**
	 * 是否在跨服
	 */
	public boolean isBridgeStageIn() {
		return record.<Integer>get("bridgeStageIn") == 1;
	}

	public void setBridgeStageIn(Object bridgeStageIn) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		boolean bridgeStageInbool = false;
		if(bridgeStageIn instanceof Boolean){
			bridgeStageInbool = (boolean)bridgeStageIn;
		} else if(bridgeStageIn instanceof Integer){
			bridgeStageInbool = (int)bridgeStageIn == 1;
		} else if(bridgeStageIn instanceof String){
			if("true".equals(bridgeStageIn) || "1".equals(bridgeStageIn)){
				bridgeStageInbool = true;
			} else if("false".equals(bridgeStageIn) || "0".equals(bridgeStageIn)){
				bridgeStageInbool = false;
			}
		}
		record.set(K.bridgeStageIn, bridgeStageInbool ? 1 : 0);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		updateRedisHashField("", K.bridgeStageIn, bridgeStageInbool);
	}
	/**
	 * 当前所在场景
	 */
	public int getScene() {
		return record.get(K.scene);
	}

	public void setScene(final int scene) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.scene, scene);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.scene, scene);
	}
	/**
	 * 是否无限
	 */
	public int getUnlimited() {
		return record.get(K.unlimited);
	}

	public void setUnlimited(final int unlimited) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.unlimited, unlimited);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.unlimited, unlimited);
	}
	/**
	 * 副本类型对应通关难度
	 */
	public String getRepTypeDifficultyMap() {
		return record.get(K.repTypeDifficultyMap);
	}

	public void setRepTypeDifficultyMap(final String repTypeDifficultyMap) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.repTypeDifficultyMap, repTypeDifficultyMap);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.repTypeDifficultyMap, repTypeDifficultyMap);
	}
	/**
	 * 副本类型对应通关难度(组队十点防御值通关)
	 */
	public String getRepTypeTenDiffMap() {
		return record.get(K.repTypeTenDiffMap);
	}

	public void setRepTypeTenDiffMap(final String repTypeTenDiffMap) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.repTypeTenDiffMap, repTypeTenDiffMap);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.repTypeTenDiffMap, repTypeTenDiffMap);
	}
	/**
	 * 副本类型对应累计通过次数
	 */
	public String getRepTypeNumMap() {
		return record.get(K.repTypeNumMap);
	}

	public void setRepTypeNumMap(final String repTypeNumMap) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.repTypeNumMap, repTypeNumMap);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.repTypeNumMap, repTypeNumMap);
	}
	/**
	 * 解锁副本类型list
	 */
	public String getRepTypeList() {
		return record.get(K.repTypeList);
	}

	public void setRepTypeList(final String repTypeList) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.repTypeList, repTypeList);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.repTypeList, repTypeList);
	}
	/**
	 * 累计开箱次数（神灯）
	 */
	public int getTotalBoxNum() {
		return record.get(K.totalBoxNum);
	}

	public void setTotalBoxNum(final int totalBoxNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.totalBoxNum, totalBoxNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.totalBoxNum, totalBoxNum);
	}
	/**
	 * 累计启灵次数（雕像）
	 */
	public int getTotalStatueNum() {
		return record.get(K.totalStatueNum);
	}

	public void setTotalStatueNum(final int totalStatueNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.totalStatueNum, totalStatueNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.totalStatueNum, totalStatueNum);
	}
	/**
	 * 拥有的技能sn
	 */
	public String getSkillSnList() {
		return record.get(K.skillSnList);
	}

	public void setSkillSnList(final String skillSnList) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.skillSnList, skillSnList);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.skillSnList, skillSnList);
	}
	/**
	 * 技能图鉴:sn,等级
	 */
	public String getSkillIllustratedMap() {
		return record.get(K.skillIllustratedMap);
	}

	public void setSkillIllustratedMap(final String skillIllustratedMap) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.skillIllustratedMap, skillIllustratedMap);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.skillIllustratedMap, skillIllustratedMap);
	}
	/**
	 * 技能方案名字
	 */
	public String getSkillTabNameJSON() {
		return record.get(K.skillTabNameJSON);
	}

	public void setSkillTabNameJSON(final String skillTabNameJSON) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.skillTabNameJSON, skillTabNameJSON);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.skillTabNameJSON, skillTabNameJSON);
	}
	/**
	 * 抽技能等级
	 */
	public int getLotterySkillLv() {
		return record.get(K.lotterySkillLv);
	}

	public void setLotterySkillLv(final int lotterySkillLv) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.lotterySkillLv, lotterySkillLv);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.lotterySkillLv, lotterySkillLv);
	}
	/**
	 * 累计抽技能次数
	 */
	public int getTotalCardSkillNum() {
		return record.get(K.totalCardSkillNum);
	}

	public void setTotalCardSkillNum(final int totalCardSkillNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.totalCardSkillNum, totalCardSkillNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.totalCardSkillNum, totalCardSkillNum);
	}
	/**
	 * 是否强化过技能
	 */
	public boolean isAddLvSkill() {
		return record.<Integer>get("addLvSkill") == 1;
	}

	public void setAddLvSkill(Object addLvSkill) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		boolean addLvSkillbool = false;
		if(addLvSkill instanceof Boolean){
			addLvSkillbool = (boolean)addLvSkill;
		} else if(addLvSkill instanceof Integer){
			addLvSkillbool = (int)addLvSkill == 1;
		} else if(addLvSkill instanceof String){
			if("true".equals(addLvSkill) || "1".equals(addLvSkill)){
				addLvSkillbool = true;
			} else if("false".equals(addLvSkill) || "0".equals(addLvSkill)){
				addLvSkillbool = false;
			}
		}
		record.set(K.addLvSkill, addLvSkillbool ? 1 : 0);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		updateRedisHashField("", K.addLvSkill, addLvSkillbool);
	}
	/**
	 * 同伴图鉴:sn,等级
	 */
	public String getPetIllustratedMap() {
		return record.get(K.petIllustratedMap);
	}

	public void setPetIllustratedMap(final String petIllustratedMap) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.petIllustratedMap, petIllustratedMap);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.petIllustratedMap, petIllustratedMap);
	}
	/**
	 * 同伴方案名字
	 */
	public String getPetTabNameJSON() {
		return record.get(K.petTabNameJSON);
	}

	public void setPetTabNameJSON(final String petTabNameJSON) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.petTabNameJSON, petTabNameJSON);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.petTabNameJSON, petTabNameJSON);
	}
	/**
	 * 抽同伴等级
	 */
	public int getLotteryPetLv() {
		return record.get(K.lotteryPetLv);
	}

	public void setLotteryPetLv(final int lotteryPetLv) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.lotteryPetLv, lotteryPetLv);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.lotteryPetLv, lotteryPetLv);
	}
	/**
	 * 累计抽同伴次数
	 */
	public int getTotalCardPetNum() {
		return record.get(K.totalCardPetNum);
	}

	public void setTotalCardPetNum(final int totalCardPetNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.totalCardPetNum, totalCardPetNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.totalCardPetNum, totalCardPetNum);
	}
	/**
	 * 是否强化过同伴
	 */
	public boolean isAddLvPet() {
		return record.<Integer>get("addLvPet") == 1;
	}

	public void setAddLvPet(Object addLvPet) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		boolean addLvPetbool = false;
		if(addLvPet instanceof Boolean){
			addLvPetbool = (boolean)addLvPet;
		} else if(addLvPet instanceof Integer){
			addLvPetbool = (int)addLvPet == 1;
		} else if(addLvPet instanceof String){
			if("true".equals(addLvPet) || "1".equals(addLvPet)){
				addLvPetbool = true;
			} else if("false".equals(addLvPet) || "0".equals(addLvPet)){
				addLvPetbool = false;
			}
		}
		record.set(K.addLvPet, addLvPetbool ? 1 : 0);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		updateRedisHashField("", K.addLvPet, addLvPetbool);
	}
	/**
	 * 征战公会boss时间
	 */
	public long getGuildBossTime() {
		return record.get(K.guildBossTime);
	}

	public void setGuildBossTime(final long guildBossTime) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.guildBossTime, guildBossTime);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.guildBossTime, guildBossTime);
	}
	/**
	 * boss次数
	 */
	public int getGuildBossNum() {
		return record.get(K.guildBossNum);
	}

	public void setGuildBossNum(final int guildBossNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.guildBossNum, guildBossNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.guildBossNum, guildBossNum);
	}
	/**
	 * 功能模块,战力
	 */
	public String getModPowerJSON() {
		return record.get(K.modPowerJSON);
	}

	public void setModPowerJSON(final String modPowerJSON) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.modPowerJSON, modPowerJSON);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.modPowerJSON, modPowerJSON);
	}
	/**
	 * 定级赛次数
	 */
	public int getArenaGradingNum() {
		return record.get(K.arenaGradingNum);
	}

	public void setArenaGradingNum(final int arenaGradingNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.arenaGradingNum, arenaGradingNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.arenaGradingNum, arenaGradingNum);
	}
	/**
	 * 定级赛胜利次数
	 */
	public int getArenaGradingWinNum() {
		return record.get(K.arenaGradingWinNum);
	}

	public void setArenaGradingWinNum(final int arenaGradingWinNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.arenaGradingWinNum, arenaGradingWinNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.arenaGradingWinNum, arenaGradingWinNum);
	}
	/**
	 * 排位赛段位
	 */
	public int getArenaBridgeGrad() {
		return record.get(K.arenaBridgeGrad);
	}

	public void setArenaBridgeGrad(final int arenaBridgeGrad) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.arenaBridgeGrad, arenaBridgeGrad);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.arenaBridgeGrad, arenaBridgeGrad);
	}
	/**
	 * 定级赛是否结束
	 */
	public boolean isArenaGradingEnd() {
		return record.<Integer>get("arenaGradingEnd") == 1;
	}

	public void setArenaGradingEnd(Object arenaGradingEnd) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		boolean arenaGradingEndbool = false;
		if(arenaGradingEnd instanceof Boolean){
			arenaGradingEndbool = (boolean)arenaGradingEnd;
		} else if(arenaGradingEnd instanceof Integer){
			arenaGradingEndbool = (int)arenaGradingEnd == 1;
		} else if(arenaGradingEnd instanceof String){
			if("true".equals(arenaGradingEnd) || "1".equals(arenaGradingEnd)){
				arenaGradingEndbool = true;
			} else if("false".equals(arenaGradingEnd) || "0".equals(arenaGradingEnd)){
				arenaGradingEndbool = false;
			}
		}
		record.set(K.arenaGradingEnd, arenaGradingEndbool ? 1 : 0);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		updateRedisHashField("", K.arenaGradingEnd, arenaGradingEndbool);
	}
	/**
	 * 定级赛结束时间
	 */
	public long getArenaGradingEndTime() {
		return record.get(K.arenaGradingEndTime);
	}

	public void setArenaGradingEndTime(final long arenaGradingEndTime) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.arenaGradingEndTime, arenaGradingEndTime);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.arenaGradingEndTime, arenaGradingEndTime);
	}
	/**
	 * 是否检测机器人
	 */
	public boolean isCheckRobot() {
		return record.<Integer>get("checkRobot") == 1;
	}

	public void setCheckRobot(Object checkRobot) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		boolean checkRobotbool = false;
		if(checkRobot instanceof Boolean){
			checkRobotbool = (boolean)checkRobot;
		} else if(checkRobot instanceof Integer){
			checkRobotbool = (int)checkRobot == 1;
		} else if(checkRobot instanceof String){
			if("true".equals(checkRobot) || "1".equals(checkRobot)){
				checkRobotbool = true;
			} else if("false".equals(checkRobot) || "0".equals(checkRobot)){
				checkRobotbool = false;
			}
		}
		record.set(K.checkRobot, checkRobotbool ? 1 : 0);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		updateRedisHashField("", K.checkRobot, checkRobotbool);
	}
	/**
	 * 是否有gm权限
	 */
	public boolean isGm() {
		return record.<Integer>get("gm") == 1;
	}

	public void setGm(Object gm) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		boolean gmbool = false;
		if(gm instanceof Boolean){
			gmbool = (boolean)gm;
		} else if(gm instanceof Integer){
			gmbool = (int)gm == 1;
		} else if(gm instanceof String){
			if("true".equals(gm) || "1".equals(gm)){
				gmbool = true;
			} else if("false".equals(gm) || "0".equals(gm)){
				gmbool = false;
			}
		}
		record.set(K.gm, gmbool ? 1 : 0);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		updateRedisHashField("", K.gm, gmbool);
	}
	/**
	 * gveSn
	 */
	public String getGveSnList() {
		return record.get(K.gveSnList);
	}

	public void setGveSnList(final String gveSnList) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.gveSnList, gveSnList);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.gveSnList, gveSnList);
	}
	/**
	 * 预告奖励领取次数
	 */
	public int getPreviewNum() {
		return record.get(K.previewNum);
	}

	public void setPreviewNum(final int previewNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.previewNum, previewNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.previewNum, previewNum);
	}
	/**
	 * 累计公会捐献次数
	 */
	public int getGuildDonateNum() {
		return record.get(K.guildDonateNum);
	}

	public void setGuildDonateNum(final int guildDonateNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.guildDonateNum, guildDonateNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.guildDonateNum, guildDonateNum);
	}
	/**
	 * 表情包sn
	 */
	public String getEmojiSnList() {
		return record.get(K.emojiSnList);
	}

	public void setEmojiSnList(final String emojiSnList) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.emojiSnList, emojiSnList);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.emojiSnList, emojiSnList);
	}
	/**
	 * 表情包sn和过期时间（0永久不在此）
	 */
	public String getEmojiSnExpiredMap() {
		return record.get(K.emojiSnExpiredMap);
	}

	public void setEmojiSnExpiredMap(final String emojiSnExpiredMap) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.emojiSnExpiredMap, emojiSnExpiredMap);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.emojiSnExpiredMap, emojiSnExpiredMap);
	}
	/**
	 * 创角奖励是否发放
	 */
	public boolean isIsCreateReward() {
		return record.<Integer>get("isCreateReward") == 1;
	}

	public void setIsCreateReward(Object isCreateReward) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		boolean isCreateRewardbool = false;
		if(isCreateReward instanceof Boolean){
			isCreateRewardbool = (boolean)isCreateReward;
		} else if(isCreateReward instanceof Integer){
			isCreateRewardbool = (int)isCreateReward == 1;
		} else if(isCreateReward instanceof String){
			if("true".equals(isCreateReward) || "1".equals(isCreateReward)){
				isCreateRewardbool = true;
			} else if("false".equals(isCreateReward) || "0".equals(isCreateReward)){
				isCreateRewardbool = false;
			}
		}
		record.set(K.isCreateReward, isCreateRewardbool ? 1 : 0);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		updateRedisHashField("", K.isCreateReward, isCreateRewardbool);
	}
	/**
	 * 修复版本
	 */
	public int getFixVersion() {
		return record.get(K.fixVersion);
	}

	public void setFixVersion(final int fixVersion) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.fixVersion, fixVersion);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.fixVersion, fixVersion);
	}
	/**
	 * 邪眼升级材料当前消耗值，会随重置和分解回退
	 */
	public long getFateCurrCost() {
		return record.get(K.fateCurrCost);
	}

	public void setFateCurrCost(final long fateCurrCost) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.fateCurrCost, fateCurrCost);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.fateCurrCost, fateCurrCost);
	}
	/**
	 * 邪眼升级材料历史最大消耗值
	 */
	public long getFateMaxCost() {
		return record.get(K.fateMaxCost);
	}

	public void setFateMaxCost(final long fateMaxCost) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.fateMaxCost, fateMaxCost);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.fateMaxCost, fateMaxCost);
	}
	/**
	 * 常用的boolean类型变量
	 */
	public long getFlags() {
		return record.get(K.flags);
	}

	public void setFlags(final long flags) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.flags, flags);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.flags, flags);
	}
	/**
	 * 0点重置时间
	 */
	public long getZeroResetTime() {
		return record.get(K.zeroResetTime);
	}

	public void setZeroResetTime(final long zeroResetTime) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.zeroResetTime, zeroResetTime);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.zeroResetTime, zeroResetTime);
	}
	/**
	 * 小游戏已完成的章节sn
	 */
	public String getBackLampFinishedIds() {
		return record.get(K.backLampFinishedIds);
	}

	public void setBackLampFinishedIds(final String backLampFinishedIds) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.backLampFinishedIds, backLampFinishedIds);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.backLampFinishedIds, backLampFinishedIds);
	}
	/**
	 * 小游戏已领奖的章节sn
	 */
	public String getBackLampReceivedIds() {
		return record.get(K.backLampReceivedIds);
	}

	public void setBackLampReceivedIds(final String backLampReceivedIds) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.backLampReceivedIds, backLampReceivedIds);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.backLampReceivedIds, backLampReceivedIds);
	}
	 
	
	@Override
	public void writeTo(OutputStream out) throws IOException {
		super.writeTo(out);
		
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		super.readFrom(in);
	}
	
	public void setNew(){
		record.setStatus(DBConsts.RECORD_STATUS_NEW);
	}

		public static String getRedisKeyStr(Object... obj){
		return "Human3." + obj[0];
	}

}