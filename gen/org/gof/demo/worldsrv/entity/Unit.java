package org.gof.demo.worldsrv.entity;

import org.gof.core.db.DBConsts;
import io.vertx.core.json.JsonObject;
import org.gof.core.Port;
import org.gof.core.Record;
import org.gof.core.entity.EntityBase;
import org.gof.core.gen.GofGenFile;
import org.gof.core.support.S;
import org.gof.core.OutputStream;
import java.io.IOException;
import org.gof.core.InputStream;

@GofGenFile
public abstract class Unit extends EntityBase {
	public Unit() {
		super();
		setSpeed(0);
		setLevel(0);
		setHpCur(0L);
		setHpMax(0L);
		setCombat("1");
		setSn("");
		setName("");
		setModelSn("");
		setExpCur(0L);
	}

	public Unit(Record record) {
		super(record);
		
		
		
		
		
		
		
		
		
		
	}
	
	
	/**
	 * 属性关键字
	 */
	public static class SuperK {
		public static final String id = "id";	//id
		public static final String speed = "speed";	//速度
		public static final String level = "level";	//当前等级
		public static final String hpCur = "hpCur";	//当前生命
		public static final String hpMax = "hpMax";	//生命值上限
		public static final String combat = "combat";	//战斗力
		public static final String sn = "sn";	//配置表SN
		public static final String name = "name";	//姓名
		public static final String modelSn = "modelSn";	//模型sn
		public static final String expCur = "expCur";	//当前经验
	}

	/**
	 * id
	 */
	public long getId() {
		return record.get(SuperK.id);
	}

	public void setId(final long id) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.id, id);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.id, id);
	}
	/**
	 * 速度
	 */
	public int getSpeed() {
		return record.get(SuperK.speed);
	}

	public void setSpeed(final int speed) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.speed, speed);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.speed, speed);
	}
	/**
	 * 当前等级
	 */
	public int getLevel() {
		return record.get(SuperK.level);
	}

	public void setLevel(final int level) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.level, level);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.level, level);
	}
	/**
	 * 当前生命
	 */
	public long getHpCur() {
		return record.get(SuperK.hpCur);
	}

	public void setHpCur(final long hpCur) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.hpCur, hpCur);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.hpCur, hpCur);
	}
	/**
	 * 生命值上限
	 */
	public long getHpMax() {
		return record.get(SuperK.hpMax);
	}

	public void setHpMax(final long hpMax) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.hpMax, hpMax);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.hpMax, hpMax);
	}
	/**
	 * 战斗力
	 */
	public String getCombat() {
		return record.get(SuperK.combat);
	}

	public void setCombat(final String combat) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.combat, combat);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.combat, combat);
	}
	/**
	 * 配置表SN
	 */
	public String getSn() {
		return record.get(SuperK.sn);
	}

	public void setSn(final String sn) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.sn, sn);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.sn, sn);
	}
	/**
	 * 姓名
	 */
	public String getName() {
		return record.get(SuperK.name);
	}

	public void setName(final String name) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.name, name);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.name, name);
	}
	/**
	 * 模型sn
	 */
	public String getModelSn() {
		return record.get(SuperK.modelSn);
	}

	public void setModelSn(final String modelSn) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.modelSn, modelSn);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.modelSn, modelSn);
	}
	/**
	 * 当前经验
	 */
	public long getExpCur() {
		return record.get(SuperK.expCur);
	}

	public void setExpCur(final long expCur) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.expCur, expCur);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.expCur, expCur);
	}
	
	public JsonObject getAllObjNew(){
		JsonObject obj = new JsonObject();
		obj.put(SuperK.id, getId());
		obj.put(SuperK.speed, getSpeed());
		obj.put(SuperK.level, getLevel());
		obj.put(SuperK.hpCur, getHpCur());
		obj.put(SuperK.hpMax, getHpMax());
		obj.put(SuperK.combat, getCombat());
		obj.put(SuperK.sn, getSn());
		obj.put(SuperK.name, getName());
		obj.put(SuperK.modelSn, getModelSn());
		obj.put(SuperK.expCur, getExpCur());
		return obj;
	}
	
	@Override
	public void writeTo(OutputStream out) throws IOException {
		super.writeTo(out);
		 
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		super.readFrom(in);
	}

}