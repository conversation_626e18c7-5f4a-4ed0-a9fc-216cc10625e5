package org.gof.demo.worldsrv.entity;


import co.paralleluniverse.fibers.Suspendable;
import org.apache.commons.lang3.exception.ExceptionUtils;

import org.gof.core.*;
import org.gof.core.db.DBConsts;
import org.gof.core.dbsrv.DB;
import org.gof.core.support.BufferPool;
import org.gof.core.support.S;
import org.gof.core.support.SysException;
import org.gof.core.support.log.LogCore;
import org.gof.core.entity.EntityBase;
import org.gof.core.gen.GofGenFile;
import io.vertx.core.json.JsonObject;
import org.gof.core.entity.EntityBase;
import org.gof.core.gen.GofGenFile;

import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@GofGenFile
public final class Guild extends EntityBase {
	public static final String tableName = "guild";
	public static final boolean autoCache = false;
	public static final String LISTKEY = "gameServerId";

	public static final int REDIS_EXPIRE_TIME = 0;// redis过期时间

	public static final int UPDATE_DB_TYPE = 0;// 数据入库类型 0队列入库 1实时入库 2不入库

	public static long redisSyncTimeInterval = 0L;

	
	
	
	/**
	 * 属性关键字
	 */
	public static final class K {
		public static final String id = "id";	//id
		public static final String createTime = "createTime";	//创建时间
		public static final String gameServerId = "gameServerId";	//游戏服务器id
		public static final String name = "name";	//公会名字
		public static final String notice = "notice";	//公会公告
		public static final String leaderId = "leaderId";	//会长
		public static final String autoJoin = "autoJoin";	//是否允许自动加入
		public static final String joinLimitLv = "joinLimitLv";	//加入等级限制
		public static final String level = "level";	//公会等级
		public static final String exp = "exp";	//公会经验
		public static final String dailyExp = "dailyExp";	//今日公会经验
		public static final String zone = "zone";	//区
		public static final String positionIdListMap = "positionIdListMap";	//职位对应玩家id
		public static final String bossSn = "bossSn";	//boss难度
		public static final String bossBoxNum = "bossBoxNum";	//boss今日宝箱数
		public static final String bossHighBoxNum = "bossHighBoxNum";	//boss今日高级宝箱数
		public static final String bossBoxResetTime = "bossBoxResetTime";	//宝箱重置时间
		public static final String bossMaxHurt = "bossMaxHurt";	//boss历史最高战力
		public static final String grade = "grade";	//段位
		public static final String gveSn = "gveSn";	//胖头鱼难度
		public static final String gveLastSn = "gveLastSn";	//上次胖头鱼难度
		public static final String gveSumHurt = "gveSumHurt";	//胖头鱼本次已经造成伤害
		public static final String gveResetTime = "gveResetTime";	//胖头鱼重置时间
		public static final String gveLastTime = "gveLastTime";	//上次胖头鱼战斗时间
		public static final String gveKey = "gveKey";	//gveRidisKey
		public static final String leaderChangeTime = "leaderChangeTime";	//更改会长时间
		public static final String extendJSON = "extendJSON";	//扩展属性(用于处理bug)
		public static final String flagJSON = "flagJSON";	//家族旗帜自定义数据信息
		public static final String power = "power";	//战力
		public static final String zeroResetTime = "zeroResetTime";	//公会0点重置时间
		public static final String removeTime = "removeTime";	//删除公会时间
		public static final String freeGuildNameNum = "freeGuildNameNum";	//免费改名次数
		public static final String kvJSON = "kvJSON";	//类型数据信息
	}

	@Override
	public String getTableName() {
		return tableName;
	}

	@Override
	public boolean isAutoCache(){
        return autoCache;
    }
	
	public Guild() {
		super();
		setCreateTime(0L);
		setGameServerId(0);
		setName("");
		setNotice("");
		setLeaderId(0L);
		setAutoJoin(0);
		setJoinLimitLv(0);
		setLevel(1);
		setExp(0);
		setDailyExp(0);
		setZone(0);
		setPositionIdListMap("");
		setBossSn(1);
		setBossBoxNum(0);
		setBossHighBoxNum(0);
		setBossBoxResetTime(0L);
		setBossMaxHurt(1);
		setGrade(1);
		setGveSn(1);
		setGveLastSn(1);
		setGveSumHurt(0);
		setGveResetTime(0L);
		setGveLastTime(0L);
		setGveKey(0);
		setLeaderChangeTime(0L);
		setExtendJSON("");
		setFlagJSON("[]");
		setPower(0L);
		setZeroResetTime(0L);
		setRemoveTime(0L);
		setFreeGuildNameNum(0);
		setKvJSON("[]");
	}

	public Guild(Record record) {
		super(record);
	}

	
	/**
	 * 新增数据
	 */
	@Override
	public void persist() {
		
		if(getId() == 0){
			setTableId();
		}
		insertNew();
		
		//状态错误
		if(record.getStatus() != DBConsts.RECORD_STATUS_NEW) {
			LogCore.db.error("只有新增包能调用persist函数，请确认状态：data={}, stackTrace={}", this, ExceptionUtils.getStackTrace(new Throwable()));
			return;
		}
		
		DB prx = DB.newInstance(getTableName());
		prx.insert(record);
		
		//重置状态
		record.resetStatus();
	}

	/**
	 * 同步修改数据至DB服务器
	 * 默认不立即持久化到数据库
	 */
	@Override
	public void update() {
		update(false);
	}

	@Override
	public void updateRedis(boolean sync) {
		if(redisSyncTimeInterval == 0L){
			redisSyncTimeInterval = Port.getTime();
		}
		if(sync || (getUpdateObj() != null && Port.getTime() - redisSyncTimeInterval > 5 * 1000L)){// 避免关服瞬间所有玩家一次性写入太多
			redisSyncTimeInterval = Port.getTime();
			updateNew();//别用实时入库（会有先后顺序问题），如果需要则把入库类型改了
		}
	}

	@Override
	public void updateDB(boolean sync) {

		//新增包不能直接调用update函数 请先调用persist
		if(record.getStatus() == DBConsts.RECORD_STATUS_NEW) {
			throw new SysException("新增包不能直接调用update函数，请先调用persist：data={}", this);
		}

		//升级包
		Chunk patch = record.patchUpdateGen();
		if(patch == null || patch.length == 0) return;

		//将升级包同步至DB服务器
		DB prx = DB.newInstance(getTableName());
		prx.update(getId(), patch, sync);

		//回收缓冲包
		patch.release();

		//重置状态
		record.resetStatus();
	}
	
	/**
	 * 同步修改数据至DB服务器
	 * @param sync 是否立即同持久化到数据库
	 */
	@Override
	public void update(boolean sync) {
		
		//新增包不能直接调用update函数 请先调用persist
		if(record.getStatus() == DBConsts.RECORD_STATUS_NEW) {
			throw new SysException("新增包不能直接调用update函数，请先调用persist：data={}", this);
		}
		
		if(getUpdateObj() != null){
			redisSyncTimeInterval = Port.getTime();
			updateNew();//别用实时入库（会有先后顺序问题），如果需要则把入库类型改了
		}
		
		//升级包
		Chunk patch = record.patchUpdateGen();
		if(patch == null || patch.length == 0) return;

		//将升级包同步至DB服务器
		DB prx = DB.newInstance(getTableName());
		prx.update(getId(), patch, sync);
		
		//回收缓冲包
		patch.release();
		
		//重置状态
		record.resetStatus();
	}

	/**
	 * 删除数据
	 */
	@Override
	public void remove() {
		
		deleteNew();
	
		DB prx = DB.newInstance(getTableName());
		prx.delete(getId());
	}

	public void reset() {
		super.reset();
		record.setNewness(false);
		record.resetStatus();
	}

	protected String getKey() {
		return "Guild." + getId();
	}

	

	protected String getListKey() {
		return "Guild."  + getGameServerId();
	}
	
	protected String getListItemKey() {
		return getId() + "";
	}

	

	public void doCreate() {
		setTableId();
		insertNew();
	}

	public void setTableId() {
		setId(incrTableId("guild"));
	}

	public JsonObject insertNew() {
		return super.insert(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE, LISTKEY, getListKey(), getListItemKey(), false, tableName);
	}

	public void deleteNew() {
		super.delete(getKey(), getId(), UPDATE_DB_TYPE, 0, LISTKEY, getListKey(), getListItemKey(), false, tableName);
	}

	/**
	* 根据入库类型更新
	*/
	public JsonObject updateNew() {
		return super.update(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE, 0, getAllObjNew(), LISTKEY, getListKey(), getListItemKey(), tableName);
	}

	/**
	* 实时入库
	*/
	public JsonObject updateNowNew() {
		return super.update(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE_NOW, 0, getAllObjNew(), LISTKEY, getListKey(), getListItemKey(), tableName);
	}

	public JsonObject getAllObjNew(){
		JsonObject obj = super.getAllObjNew();
		obj.put(K.id, getId()); // id
		obj.put(K.createTime, getCreateTime()); // createTime
		obj.put(K.gameServerId, getGameServerId()); // gameServerId
		obj.put(K.name, getName()); // name
		obj.put(K.notice, getNotice()); // notice
		obj.put(K.leaderId, getLeaderId()); // leaderId
		obj.put(K.autoJoin, getAutoJoin()); // autoJoin
		obj.put(K.joinLimitLv, getJoinLimitLv()); // joinLimitLv
		obj.put(K.level, getLevel()); // level
		obj.put(K.exp, getExp()); // exp
		obj.put(K.dailyExp, getDailyExp()); // dailyExp
		obj.put(K.zone, getZone()); // zone
		obj.put(K.positionIdListMap, getPositionIdListMap()); // positionIdListMap
		obj.put(K.bossSn, getBossSn()); // bossSn
		obj.put(K.bossBoxNum, getBossBoxNum()); // bossBoxNum
		obj.put(K.bossHighBoxNum, getBossHighBoxNum()); // bossHighBoxNum
		obj.put(K.bossBoxResetTime, getBossBoxResetTime()); // bossBoxResetTime
		obj.put(K.bossMaxHurt, getBossMaxHurt()); // bossMaxHurt
		obj.put(K.grade, getGrade()); // grade
		obj.put(K.gveSn, getGveSn()); // gveSn
		obj.put(K.gveLastSn, getGveLastSn()); // gveLastSn
		obj.put(K.gveSumHurt, getGveSumHurt()); // gveSumHurt
		obj.put(K.gveResetTime, getGveResetTime()); // gveResetTime
		obj.put(K.gveLastTime, getGveLastTime()); // gveLastTime
		obj.put(K.gveKey, getGveKey()); // gveKey
		obj.put(K.leaderChangeTime, getLeaderChangeTime()); // leaderChangeTime
		obj.put(K.extendJSON, getExtendJSON()); // extendJSON
		obj.put(K.flagJSON, getFlagJSON()); // flagJSON
		obj.put(K.power, getPower()); // power
		obj.put(K.zeroResetTime, getZeroResetTime()); // zeroResetTime
		obj.put(K.removeTime, getRemoveTime()); // removeTime
		obj.put(K.freeGuildNameNum, getFreeGuildNameNum()); // freeGuildNameNum
		obj.put(K.kvJSON, getKvJSON()); // kvJSON
		return obj;
	}

	/**
	 * id
	 */
	public long getId() {
		return record.get(K.id);
	}

	public void setId(final long id) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.id, id);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.id, id);
	}
	/**
	 * 创建时间
	 */
	public long getCreateTime() {
		return record.get(K.createTime);
	}

	public void setCreateTime(final long createTime) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.createTime, createTime);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.createTime, createTime);
	}
	/**
	 * 游戏服务器id
	 */
	public int getGameServerId() {
		return record.get(K.gameServerId);
	}

	public void setGameServerId(final int gameServerId) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.gameServerId, gameServerId);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.gameServerId, gameServerId);
	}
	/**
	 * 公会名字
	 */
	public String getName() {
		return record.get(K.name);
	}

	public void setName(final String name) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.name, name);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.name, name);
	}
	/**
	 * 公会公告
	 */
	public String getNotice() {
		return record.get(K.notice);
	}

	public void setNotice(final String notice) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.notice, notice);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.notice, notice);
	}
	/**
	 * 会长
	 */
	public long getLeaderId() {
		return record.get(K.leaderId);
	}

	public void setLeaderId(final long leaderId) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.leaderId, leaderId);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.leaderId, leaderId);
	}
	/**
	 * 是否允许自动加入
	 */
	public int getAutoJoin() {
		return record.get(K.autoJoin);
	}

	public void setAutoJoin(final int autoJoin) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.autoJoin, autoJoin);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.autoJoin, autoJoin);
	}
	/**
	 * 加入等级限制
	 */
	public int getJoinLimitLv() {
		return record.get(K.joinLimitLv);
	}

	public void setJoinLimitLv(final int joinLimitLv) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.joinLimitLv, joinLimitLv);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.joinLimitLv, joinLimitLv);
	}
	/**
	 * 公会等级
	 */
	public int getLevel() {
		return record.get(K.level);
	}

	public void setLevel(final int level) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.level, level);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.level, level);
	}
	/**
	 * 公会经验
	 */
	public int getExp() {
		return record.get(K.exp);
	}

	public void setExp(final int exp) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.exp, exp);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.exp, exp);
	}
	/**
	 * 今日公会经验
	 */
	public int getDailyExp() {
		return record.get(K.dailyExp);
	}

	public void setDailyExp(final int dailyExp) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.dailyExp, dailyExp);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.dailyExp, dailyExp);
	}
	/**
	 * 区
	 */
	public int getZone() {
		return record.get(K.zone);
	}

	public void setZone(final int zone) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.zone, zone);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.zone, zone);
	}
	/**
	 * 职位对应玩家id
	 */
	public String getPositionIdListMap() {
		return record.get(K.positionIdListMap);
	}

	public void setPositionIdListMap(final String positionIdListMap) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.positionIdListMap, positionIdListMap);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.positionIdListMap, positionIdListMap);
	}
	/**
	 * boss难度
	 */
	public int getBossSn() {
		return record.get(K.bossSn);
	}

	public void setBossSn(final int bossSn) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.bossSn, bossSn);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.bossSn, bossSn);
	}
	/**
	 * boss今日宝箱数
	 */
	public int getBossBoxNum() {
		return record.get(K.bossBoxNum);
	}

	public void setBossBoxNum(final int bossBoxNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.bossBoxNum, bossBoxNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.bossBoxNum, bossBoxNum);
	}
	/**
	 * boss今日高级宝箱数
	 */
	public int getBossHighBoxNum() {
		return record.get(K.bossHighBoxNum);
	}

	public void setBossHighBoxNum(final int bossHighBoxNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.bossHighBoxNum, bossHighBoxNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.bossHighBoxNum, bossHighBoxNum);
	}
	/**
	 * 宝箱重置时间
	 */
	public long getBossBoxResetTime() {
		return record.get(K.bossBoxResetTime);
	}

	public void setBossBoxResetTime(final long bossBoxResetTime) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.bossBoxResetTime, bossBoxResetTime);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.bossBoxResetTime, bossBoxResetTime);
	}
	/**
	 * boss历史最高战力
	 */
	public long getBossMaxHurt() {
		return record.get(K.bossMaxHurt);
	}

	public void setBossMaxHurt(final long bossMaxHurt) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.bossMaxHurt, bossMaxHurt);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.bossMaxHurt, bossMaxHurt);
	}
	/**
	 * 段位
	 */
	public int getGrade() {
		return record.get(K.grade);
	}

	public void setGrade(final int grade) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.grade, grade);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.grade, grade);
	}
	/**
	 * 胖头鱼难度
	 */
	public int getGveSn() {
		return record.get(K.gveSn);
	}

	public void setGveSn(final int gveSn) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.gveSn, gveSn);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.gveSn, gveSn);
	}
	/**
	 * 上次胖头鱼难度
	 */
	public int getGveLastSn() {
		return record.get(K.gveLastSn);
	}

	public void setGveLastSn(final int gveLastSn) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.gveLastSn, gveLastSn);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.gveLastSn, gveLastSn);
	}
	/**
	 * 胖头鱼本次已经造成伤害
	 */
	public long getGveSumHurt() {
		return record.get(K.gveSumHurt);
	}

	public void setGveSumHurt(final long gveSumHurt) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.gveSumHurt, gveSumHurt);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.gveSumHurt, gveSumHurt);
	}
	/**
	 * 胖头鱼重置时间
	 */
	public long getGveResetTime() {
		return record.get(K.gveResetTime);
	}

	public void setGveResetTime(final long gveResetTime) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.gveResetTime, gveResetTime);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.gveResetTime, gveResetTime);
	}
	/**
	 * 上次胖头鱼战斗时间
	 */
	public long getGveLastTime() {
		return record.get(K.gveLastTime);
	}

	public void setGveLastTime(final long gveLastTime) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.gveLastTime, gveLastTime);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.gveLastTime, gveLastTime);
	}
	/**
	 * gveRidisKey
	 */
	public int getGveKey() {
		return record.get(K.gveKey);
	}

	public void setGveKey(final int gveKey) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.gveKey, gveKey);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.gveKey, gveKey);
	}
	/**
	 * 更改会长时间
	 */
	public long getLeaderChangeTime() {
		return record.get(K.leaderChangeTime);
	}

	public void setLeaderChangeTime(final long leaderChangeTime) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.leaderChangeTime, leaderChangeTime);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.leaderChangeTime, leaderChangeTime);
	}
	/**
	 * 扩展属性(用于处理bug)
	 */
	public String getExtendJSON() {
		return record.get(K.extendJSON);
	}

	public void setExtendJSON(final String extendJSON) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.extendJSON, extendJSON);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.extendJSON, extendJSON);
	}
	/**
	 * 家族旗帜自定义数据信息
	 */
	public String getFlagJSON() {
		return record.get(K.flagJSON);
	}

	public void setFlagJSON(final String flagJSON) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.flagJSON, flagJSON);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.flagJSON, flagJSON);
	}
	/**
	 * 战力
	 */
	public long getPower() {
		return record.get(K.power);
	}

	public void setPower(final long power) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.power, power);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.power, power);
	}
	/**
	 * 公会0点重置时间
	 */
	public long getZeroResetTime() {
		return record.get(K.zeroResetTime);
	}

	public void setZeroResetTime(final long zeroResetTime) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.zeroResetTime, zeroResetTime);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.zeroResetTime, zeroResetTime);
	}
	/**
	 * 删除公会时间
	 */
	public long getRemoveTime() {
		return record.get(K.removeTime);
	}

	public void setRemoveTime(final long removeTime) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.removeTime, removeTime);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.removeTime, removeTime);
	}
	/**
	 * 免费改名次数
	 */
	public int getFreeGuildNameNum() {
		return record.get(K.freeGuildNameNum);
	}

	public void setFreeGuildNameNum(final int freeGuildNameNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.freeGuildNameNum, freeGuildNameNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.freeGuildNameNum, freeGuildNameNum);
	}
	/**
	 * 类型数据信息
	 */
	public String getKvJSON() {
		return record.get(K.kvJSON);
	}

	public void setKvJSON(final String kvJSON) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.kvJSON, kvJSON);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.kvJSON, kvJSON);
	}
	 
	
	@Override
	public void writeTo(OutputStream out) throws IOException {
		super.writeTo(out);
		
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		super.readFrom(in);
	}
	
	public void setNew(){
		record.setStatus(DBConsts.RECORD_STATUS_NEW);
	}

		public static String getRedisKeyStr(Object... obj){
		String[] parts = LISTKEY.split("\\.");
		StringBuilder concatStr = new StringBuilder();
		concatStr.append("Guild");
		for(int i = 0; i < parts.length; i++){
			concatStr.append(".").append(obj[i]);
		}
		return concatStr.toString();
	}

}