package org.gof.demo.worldsrv.entity;


import co.paralleluniverse.fibers.Suspendable;
import org.apache.commons.lang3.exception.ExceptionUtils;

import org.gof.core.*;
import org.gof.core.db.DBConsts;
import org.gof.core.dbsrv.DB;
import org.gof.core.support.BufferPool;
import org.gof.core.support.S;
import org.gof.core.support.SysException;
import org.gof.core.support.log.LogCore;
import org.gof.core.entity.EntityBase;
import org.gof.core.gen.GofGenFile;
import io.vertx.core.json.JsonObject;
import org.gof.core.entity.EntityBase;
import org.gof.core.gen.GofGenFile;

import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@GofGenFile
public final class HumanBrief extends EntityBase {
	public static final String tableName = "human_brief";
	public static final boolean autoCache = true;
	public static final String LISTKEY = "";

	public static final int REDIS_EXPIRE_TIME = 0;// redis过期时间

	public static final int UPDATE_DB_TYPE = 0;// 数据入库类型 0队列入库 1实时入库 2不入库

	public static long redisSyncTimeInterval = 0L;

	
	
	
	/**
	 * 属性关键字
	 */
	public static final class K {
		public static final String id = "id";	//id
		public static final String name = "name";	//名字
		public static final String level = "level";	//等级
		public static final String serverId = "serverId";	//服务器id
		public static final String headSn = "headSn";	//当前头像
		public static final String currentHeadFrameSn = "currentHeadFrameSn";	//当前头像框
		public static final String jobSn = "jobSn";	//职业sn
		public static final String wingSkillSnLv = "wingSkillSnLv";	//翅膀技能sn等级
		public static final String mountSkillSnLv = "mountSkillSnLv";	//坐骑技能sn等级
		public static final String artifactSkillSnLv = "artifactSkillSnLv";	//神器技能sn等级
		public static final String angelStar = "angelStar";	//星将星级
		public static final String currentTitleSn = "currentTitleSn";	//当前头衔
		public static final String flyPetId = "flyPetId";	//飞宠id
		public static final String flyPetSn = "flyPetSn";	//飞宠sn
		public static final String guildId = "guildId";	//公会ID
		public static final String guildName = "guildName";	//帮会名称
		public static final String guildLv = "guildLv";	//公会等级
		public static final String guildHumanNum = "guildHumanNum";	//公会人数
		public static final String topCombat = "topCombat";	//最高战斗力
		public static final String battleRole = "battleRole";	//角色战斗数据
		public static final String roleFigure = "roleFigure";	//角色外观数据
		public static final String planArrStr = "planArrStr";	//默认行装
		public static final String currentPlanId = "currentPlanId";	//当前行装id
		public static final String battleRole2 = "battleRole2";	//角色战斗数据行装2
		public static final String battleRole3 = "battleRole3";	//角色战斗数据行装3
		public static final String battleRole4 = "battleRole4";	//角色战斗数据行装4
		public static final String battleRole5 = "battleRole5";	//角色战斗数据行装5
		public static final String charmValue = "charmValue";	//美观值
		public static final String showMedalList = "showMedalList";	//展示的勋章列表
	}

	@Override
	public String getTableName() {
		return tableName;
	}

	@Override
	public boolean isAutoCache(){
        return autoCache;
    }
	
	public HumanBrief() {
		super();
		setName("");
		setLevel(1);
		setServerId(0);
		setHeadSn(0);
		setCurrentHeadFrameSn(0);
		setJobSn(0);
		setWingSkillSnLv(0);
		setMountSkillSnLv(0);
		setArtifactSkillSnLv(0);
		setAngelStar(0);
		setCurrentTitleSn(0);
		setFlyPetId(0);
		setFlyPetSn(0);
		setGuildId(0);
		setGuildName("");
		setGuildLv(1);
		setGuildHumanNum(1);
		setTopCombat("1");
        setBattleRole(new byte[0]);
        setRoleFigure(new byte[0]);
		setPlanArrStr("");
		setCurrentPlanId(0);
        setBattleRole2(new byte[0]);
        setBattleRole3(new byte[0]);
        setBattleRole4(new byte[0]);
        setBattleRole5(new byte[0]);
		setCharmValue(0);
		setShowMedalList("[]");
	}

	public HumanBrief(Record record) {
		super(record);
	}

	
	/**
	 * 新增数据
	 */
	@Override
	public void persist() {
		
		if(getId() == 0){
			setTableId();
		}
		insertNew();
		
		//状态错误
		if(record.getStatus() != DBConsts.RECORD_STATUS_NEW) {
			LogCore.db.error("只有新增包能调用persist函数，请确认状态：data={}, stackTrace={}", this, ExceptionUtils.getStackTrace(new Throwable()));
			return;
		}
		
		DB prx = DB.newInstance(getTableName());
		prx.insert(record);
		
		//重置状态
		record.resetStatus();
	}

	/**
	 * 同步修改数据至DB服务器
	 * 默认不立即持久化到数据库
	 */
	@Override
	public void update() {
		update(false);
	}

	@Override
	public void updateRedis(boolean sync) {
		if(redisSyncTimeInterval == 0L){
			redisSyncTimeInterval = Port.getTime();
		}
		if(sync || (getUpdateObj() != null && Port.getTime() - redisSyncTimeInterval > 5 * 1000L)){// 避免关服瞬间所有玩家一次性写入太多
			redisSyncTimeInterval = Port.getTime();
			updateNew();//别用实时入库（会有先后顺序问题），如果需要则把入库类型改了
		}
	}

	@Override
	public void updateDB(boolean sync) {

		//新增包不能直接调用update函数 请先调用persist
		if(record.getStatus() == DBConsts.RECORD_STATUS_NEW) {
			throw new SysException("新增包不能直接调用update函数，请先调用persist：data={}", this);
		}

		//升级包
		Chunk patch = record.patchUpdateGen();
		if(patch == null || patch.length == 0) return;

		//将升级包同步至DB服务器
		DB prx = DB.newInstance(getTableName());
		prx.update(getId(), patch, sync);

		//回收缓冲包
		patch.release();

		//重置状态
		record.resetStatus();
	}
	
	/**
	 * 同步修改数据至DB服务器
	 * @param sync 是否立即同持久化到数据库
	 */
	@Override
	public void update(boolean sync) {
		
		//新增包不能直接调用update函数 请先调用persist
		if(record.getStatus() == DBConsts.RECORD_STATUS_NEW) {
			throw new SysException("新增包不能直接调用update函数，请先调用persist：data={}", this);
		}
		
		if(getUpdateObj() != null){
			redisSyncTimeInterval = Port.getTime();
			updateNew();//别用实时入库（会有先后顺序问题），如果需要则把入库类型改了
		}
		
		//升级包
		Chunk patch = record.patchUpdateGen();
		if(patch == null || patch.length == 0) return;

		//将升级包同步至DB服务器
		DB prx = DB.newInstance(getTableName());
		prx.update(getId(), patch, sync);
		
		//回收缓冲包
		patch.release();
		
		//重置状态
		record.resetStatus();
	}

	/**
	 * 删除数据
	 */
	@Override
	public void remove() {
		
		deleteNew();
	
		DB prx = DB.newInstance(getTableName());
		prx.delete(getId());
	}

	public void reset() {
		super.reset();
		record.setNewness(false);
		record.resetStatus();
	}

	protected String getKey() {
		return "HumanBrief." + getId();
	}

	

	protected String getListKey() {
		return null;
	}
	
	protected String getListItemKey() {
		return null;
	}

	

	public void doCreate() {
		setTableId();
		insertNew();
	}

	public void setTableId() {
		setId(incrTableId("human_brief"));
	}

	public JsonObject insertNew() {
		return super.insert(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE, LISTKEY, getListKey(), getListItemKey(), false, tableName);
	}

	public void deleteNew() {
		super.delete(getKey(), getId(), UPDATE_DB_TYPE, 0, LISTKEY, getListKey(), getListItemKey(), false, tableName);
	}

	/**
	* 根据入库类型更新
	*/
	public JsonObject updateNew() {
		return super.update(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE, 0, getAllObjNew(), LISTKEY, getListKey(), getListItemKey(), tableName);
	}

	/**
	* 实时入库
	*/
	public JsonObject updateNowNew() {
		return super.update(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE_NOW, 0, getAllObjNew(), LISTKEY, getListKey(), getListItemKey(), tableName);
	}

	public JsonObject getAllObjNew(){
		JsonObject obj = super.getAllObjNew();
		obj.put(K.id, getId()); // id
		obj.put(K.name, getName()); // name
		obj.put(K.level, getLevel()); // level
		obj.put(K.serverId, getServerId()); // serverId
		obj.put(K.headSn, getHeadSn()); // headSn
		obj.put(K.currentHeadFrameSn, getCurrentHeadFrameSn()); // currentHeadFrameSn
		obj.put(K.jobSn, getJobSn()); // jobSn
		obj.put(K.wingSkillSnLv, getWingSkillSnLv()); // wingSkillSnLv
		obj.put(K.mountSkillSnLv, getMountSkillSnLv()); // mountSkillSnLv
		obj.put(K.artifactSkillSnLv, getArtifactSkillSnLv()); // artifactSkillSnLv
		obj.put(K.angelStar, getAngelStar()); // angelStar
		obj.put(K.currentTitleSn, getCurrentTitleSn()); // currentTitleSn
		obj.put(K.flyPetId, getFlyPetId()); // flyPetId
		obj.put(K.flyPetSn, getFlyPetSn()); // flyPetSn
		obj.put(K.guildId, getGuildId()); // guildId
		obj.put(K.guildName, getGuildName()); // guildName
		obj.put(K.guildLv, getGuildLv()); // guildLv
		obj.put(K.guildHumanNum, getGuildHumanNum()); // guildHumanNum
		obj.put(K.topCombat, getTopCombat()); // topCombat
		obj.put(K.battleRole, getBattleRole()); // battleRole
		obj.put(K.roleFigure, getRoleFigure()); // roleFigure
		obj.put(K.planArrStr, getPlanArrStr()); // planArrStr
		obj.put(K.currentPlanId, getCurrentPlanId()); // currentPlanId
		obj.put(K.battleRole2, getBattleRole2()); // battleRole2
		obj.put(K.battleRole3, getBattleRole3()); // battleRole3
		obj.put(K.battleRole4, getBattleRole4()); // battleRole4
		obj.put(K.battleRole5, getBattleRole5()); // battleRole5
		obj.put(K.charmValue, getCharmValue()); // charmValue
		obj.put(K.showMedalList, getShowMedalList()); // showMedalList
		return obj;
	}

	/**
	 * id
	 */
	public long getId() {
		return record.get(K.id);
	}

	public void setId(final long id) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.id, id);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.id, id);
	}
	/**
	 * 名字
	 */
	public String getName() {
		return record.get(K.name);
	}

	public void setName(final String name) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.name, name);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.name, name);
	}
	/**
	 * 等级
	 */
	public int getLevel() {
		return record.get(K.level);
	}

	public void setLevel(final int level) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.level, level);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.level, level);
	}
	/**
	 * 服务器id
	 */
	public int getServerId() {
		return record.get(K.serverId);
	}

	public void setServerId(final int serverId) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.serverId, serverId);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.serverId, serverId);
	}
	/**
	 * 当前头像
	 */
	public long getHeadSn() {
		return record.get(K.headSn);
	}

	public void setHeadSn(final long headSn) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.headSn, headSn);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.headSn, headSn);
	}
	/**
	 * 当前头像框
	 */
	public int getCurrentHeadFrameSn() {
		return record.get(K.currentHeadFrameSn);
	}

	public void setCurrentHeadFrameSn(final int currentHeadFrameSn) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.currentHeadFrameSn, currentHeadFrameSn);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.currentHeadFrameSn, currentHeadFrameSn);
	}
	/**
	 * 职业sn
	 */
	public int getJobSn() {
		return record.get(K.jobSn);
	}

	public void setJobSn(final int jobSn) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.jobSn, jobSn);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.jobSn, jobSn);
	}
	/**
	 * 翅膀技能sn等级
	 */
	public int getWingSkillSnLv() {
		return record.get(K.wingSkillSnLv);
	}

	public void setWingSkillSnLv(final int wingSkillSnLv) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.wingSkillSnLv, wingSkillSnLv);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.wingSkillSnLv, wingSkillSnLv);
	}
	/**
	 * 坐骑技能sn等级
	 */
	public int getMountSkillSnLv() {
		return record.get(K.mountSkillSnLv);
	}

	public void setMountSkillSnLv(final int mountSkillSnLv) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.mountSkillSnLv, mountSkillSnLv);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.mountSkillSnLv, mountSkillSnLv);
	}
	/**
	 * 神器技能sn等级
	 */
	public int getArtifactSkillSnLv() {
		return record.get(K.artifactSkillSnLv);
	}

	public void setArtifactSkillSnLv(final int artifactSkillSnLv) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.artifactSkillSnLv, artifactSkillSnLv);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.artifactSkillSnLv, artifactSkillSnLv);
	}
	/**
	 * 星将星级
	 */
	public int getAngelStar() {
		return record.get(K.angelStar);
	}

	public void setAngelStar(final int angelStar) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.angelStar, angelStar);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.angelStar, angelStar);
	}
	/**
	 * 当前头衔
	 */
	public int getCurrentTitleSn() {
		return record.get(K.currentTitleSn);
	}

	public void setCurrentTitleSn(final int currentTitleSn) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.currentTitleSn, currentTitleSn);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.currentTitleSn, currentTitleSn);
	}
	/**
	 * 飞宠id
	 */
	public long getFlyPetId() {
		return record.get(K.flyPetId);
	}

	public void setFlyPetId(final long flyPetId) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.flyPetId, flyPetId);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.flyPetId, flyPetId);
	}
	/**
	 * 飞宠sn
	 */
	public int getFlyPetSn() {
		return record.get(K.flyPetSn);
	}

	public void setFlyPetSn(final int flyPetSn) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.flyPetSn, flyPetSn);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.flyPetSn, flyPetSn);
	}
	/**
	 * 公会ID
	 */
	public long getGuildId() {
		return record.get(K.guildId);
	}

	public void setGuildId(final long guildId) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.guildId, guildId);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.guildId, guildId);
	}
	/**
	 * 帮会名称
	 */
	public String getGuildName() {
		return record.get(K.guildName);
	}

	public void setGuildName(final String guildName) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.guildName, guildName);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.guildName, guildName);
	}
	/**
	 * 公会等级
	 */
	public int getGuildLv() {
		return record.get(K.guildLv);
	}

	public void setGuildLv(final int guildLv) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.guildLv, guildLv);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.guildLv, guildLv);
	}
	/**
	 * 公会人数
	 */
	public int getGuildHumanNum() {
		return record.get(K.guildHumanNum);
	}

	public void setGuildHumanNum(final int guildHumanNum) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.guildHumanNum, guildHumanNum);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.guildHumanNum, guildHumanNum);
	}
	/**
	 * 最高战斗力
	 */
	public String getTopCombat() {
		return record.get(K.topCombat);
	}

	public void setTopCombat(final String topCombat) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.topCombat, topCombat);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.topCombat, topCombat);
	}
	/**
	 * 角色战斗数据
	 */
	public byte[] getBattleRole() {
		return record.get(K.battleRole);
	}

	public void setBattleRole(final byte[] battleRole) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.battleRole, battleRole);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.battleRole, battleRole);
	}
	/**
	 * 角色外观数据
	 */
	public byte[] getRoleFigure() {
		return record.get(K.roleFigure);
	}

	public void setRoleFigure(final byte[] roleFigure) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.roleFigure, roleFigure);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.roleFigure, roleFigure);
	}
	/**
	 * 默认行装
	 */
	public String getPlanArrStr() {
		return record.get(K.planArrStr);
	}

	public void setPlanArrStr(final String planArrStr) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.planArrStr, planArrStr);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.planArrStr, planArrStr);
	}
	/**
	 * 当前行装id
	 */
	public int getCurrentPlanId() {
		return record.get(K.currentPlanId);
	}

	public void setCurrentPlanId(final int currentPlanId) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.currentPlanId, currentPlanId);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.currentPlanId, currentPlanId);
	}
	/**
	 * 角色战斗数据行装2
	 */
	public byte[] getBattleRole2() {
		return record.get(K.battleRole2);
	}

	public void setBattleRole2(final byte[] battleRole2) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.battleRole2, battleRole2);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.battleRole2, battleRole2);
	}
	/**
	 * 角色战斗数据行装3
	 */
	public byte[] getBattleRole3() {
		return record.get(K.battleRole3);
	}

	public void setBattleRole3(final byte[] battleRole3) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.battleRole3, battleRole3);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.battleRole3, battleRole3);
	}
	/**
	 * 角色战斗数据行装4
	 */
	public byte[] getBattleRole4() {
		return record.get(K.battleRole4);
	}

	public void setBattleRole4(final byte[] battleRole4) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.battleRole4, battleRole4);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.battleRole4, battleRole4);
	}
	/**
	 * 角色战斗数据行装5
	 */
	public byte[] getBattleRole5() {
		return record.get(K.battleRole5);
	}

	public void setBattleRole5(final byte[] battleRole5) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.battleRole5, battleRole5);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.battleRole5, battleRole5);
	}
	/**
	 * 美观值
	 */
	public int getCharmValue() {
		return record.get(K.charmValue);
	}

	public void setCharmValue(final int charmValue) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.charmValue, charmValue);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.charmValue, charmValue);
	}
	/**
	 * 展示的勋章列表
	 */
	public String getShowMedalList() {
		return record.get(K.showMedalList);
	}

	public void setShowMedalList(final String showMedalList) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.showMedalList, showMedalList);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.showMedalList, showMedalList);
	}
	 
	
	@Override
	public void writeTo(OutputStream out) throws IOException {
		super.writeTo(out);
		
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		super.readFrom(in);
	}
	
	public void setNew(){
		record.setStatus(DBConsts.RECORD_STATUS_NEW);
	}

		public static String getRedisKeyStr(Object... obj){
		return "HumanBrief." + obj[0];
	}

}