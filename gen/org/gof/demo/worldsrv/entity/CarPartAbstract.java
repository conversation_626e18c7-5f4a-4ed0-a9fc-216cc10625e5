package org.gof.demo.worldsrv.entity;

import org.gof.core.db.DBConsts;
import io.vertx.core.json.JsonObject;
import org.gof.core.Port;
import org.gof.core.Record;
import org.gof.core.entity.EntityBase;
import org.gof.core.gen.GofGenFile;
import org.gof.core.support.S;
import org.gof.core.OutputStream;
import java.io.IOException;
import org.gof.core.InputStream;

@GofGenFile
public abstract class CarPartAbstract extends EntityBase {
	public CarPartAbstract() {
		super();
		setSpace1("{}");
		setSpace2("{}");
		setSpace3("{}");
		setSpace4("{}");
		setSpace5("{}");
		setSpace6("{}");
		setSpace7("{}");
		setSpace8("{}");
		setSpace9("{}");
		setSpace10("{}");
		setExtendJSON("");
	}

	public CarPartAbstract(Record record) {
		super(record);
		
		
		
		
		
		
		
		
		
		
		
		
	}
	
	
	/**
	 * 属性关键字
	 */
	public static class SuperK {
		public static final String id = "id";	//id
		public static final String space1 = "space1";	//车库1
		public static final String space2 = "space2";	//车库2
		public static final String space3 = "space3";	//车库3
		public static final String space4 = "space4";	//车库4
		public static final String space5 = "space5";	//车库5
		public static final String space6 = "space6";	//车库6
		public static final String space7 = "space7";	//车库7
		public static final String space8 = "space8";	//车库8
		public static final String space9 = "space9";	//车库9
		public static final String space10 = "space10";	//车库0
		public static final String extendJSON = "extendJSON";	//扩展属性(用于处理bug)
	}

	/**
	 * id
	 */
	public long getId() {
		return record.get(SuperK.id);
	}

	public void setId(final long id) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.id, id);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.id, id);
	}
	/**
	 * 车库1
	 */
	public String getSpace1() {
		return record.get(SuperK.space1);
	}

	public void setSpace1(final String space1) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.space1, space1);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.space1, space1);
	}
	/**
	 * 车库2
	 */
	public String getSpace2() {
		return record.get(SuperK.space2);
	}

	public void setSpace2(final String space2) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.space2, space2);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.space2, space2);
	}
	/**
	 * 车库3
	 */
	public String getSpace3() {
		return record.get(SuperK.space3);
	}

	public void setSpace3(final String space3) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.space3, space3);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.space3, space3);
	}
	/**
	 * 车库4
	 */
	public String getSpace4() {
		return record.get(SuperK.space4);
	}

	public void setSpace4(final String space4) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.space4, space4);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.space4, space4);
	}
	/**
	 * 车库5
	 */
	public String getSpace5() {
		return record.get(SuperK.space5);
	}

	public void setSpace5(final String space5) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.space5, space5);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.space5, space5);
	}
	/**
	 * 车库6
	 */
	public String getSpace6() {
		return record.get(SuperK.space6);
	}

	public void setSpace6(final String space6) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.space6, space6);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.space6, space6);
	}
	/**
	 * 车库7
	 */
	public String getSpace7() {
		return record.get(SuperK.space7);
	}

	public void setSpace7(final String space7) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.space7, space7);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.space7, space7);
	}
	/**
	 * 车库8
	 */
	public String getSpace8() {
		return record.get(SuperK.space8);
	}

	public void setSpace8(final String space8) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.space8, space8);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.space8, space8);
	}
	/**
	 * 车库9
	 */
	public String getSpace9() {
		return record.get(SuperK.space9);
	}

	public void setSpace9(final String space9) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.space9, space9);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.space9, space9);
	}
	/**
	 * 车库0
	 */
	public String getSpace10() {
		return record.get(SuperK.space10);
	}

	public void setSpace10(final String space10) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.space10, space10);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.space10, space10);
	}
	/**
	 * 扩展属性(用于处理bug)
	 */
	public String getExtendJSON() {
		return record.get(SuperK.extendJSON);
	}

	public void setExtendJSON(final String extendJSON) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(SuperK.extendJSON, extendJSON);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", SuperK.extendJSON, extendJSON);
	}
	
	public JsonObject getAllObjNew(){
		JsonObject obj = new JsonObject();
		obj.put(SuperK.id, getId());
		obj.put(SuperK.space1, getSpace1());
		obj.put(SuperK.space2, getSpace2());
		obj.put(SuperK.space3, getSpace3());
		obj.put(SuperK.space4, getSpace4());
		obj.put(SuperK.space5, getSpace5());
		obj.put(SuperK.space6, getSpace6());
		obj.put(SuperK.space7, getSpace7());
		obj.put(SuperK.space8, getSpace8());
		obj.put(SuperK.space9, getSpace9());
		obj.put(SuperK.space10, getSpace10());
		obj.put(SuperK.extendJSON, getExtendJSON());
		return obj;
	}
	
	@Override
	public void writeTo(OutputStream out) throws IOException {
		super.writeTo(out);
		 
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		super.readFrom(in);
	}

}