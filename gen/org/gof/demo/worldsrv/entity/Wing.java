package org.gof.demo.worldsrv.entity;


import co.paralleluniverse.fibers.Suspendable;
import org.apache.commons.lang3.exception.ExceptionUtils;

import org.gof.core.*;
import org.gof.core.db.DBConsts;
import org.gof.core.dbsrv.DB;
import org.gof.core.support.BufferPool;
import org.gof.core.support.S;
import org.gof.core.support.SysException;
import org.gof.core.support.log.LogCore;
import org.gof.core.entity.EntityBase;
import org.gof.core.gen.GofGenFile;
import io.vertx.core.json.JsonObject;
import org.gof.core.entity.EntityBase;
import org.gof.core.gen.GofGenFile;

import java.io.IOException;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@GofGenFile
public final class Wing extends EntityBase {
	public static final String tableName = "demo_wing";
	public static final boolean autoCache = false;
	public static final String LISTKEY = "";

	public static final int REDIS_EXPIRE_TIME = 0;// redis过期时间

	public static final int UPDATE_DB_TYPE = 0;// 数据入库类型 0队列入库 1实时入库 2不入库

	public static long redisSyncTimeInterval = 0L;

	
	
	
	/**
	 * 属性关键字
	 */
	public static final class K {
		public static final String id = "id";	//id
		public static final String featherMap = "featherMap";	//羽毛sn等级经验Map
		public static final String skinLvMap = "skinLvMap";	//背饰皮肤sn等级Map
		public static final String talentLvMap = "talentLvMap";	//背饰sn等级Map,加方案弃用
		public static final String currentTab = "currentTab";	//当前方案
		public static final String talentTabInfo = "talentTabInfo";	//天赋方案
		public static final String collectionList = "collectionList";	//激活的图鉴sn
		public static final String extendJSON = "extendJSON";	//扩展属性(用于处理bug)
	}

	@Override
	public String getTableName() {
		return tableName;
	}

	@Override
	public boolean isAutoCache(){
        return autoCache;
    }
	
	public Wing() {
		super();
		setFeatherMap("{}");
		setSkinLvMap("{}");
		setTalentLvMap("0");
		setCurrentTab(0);
        setTalentTabInfo(new byte[0]);
		setCollectionList("");
		setExtendJSON("");
	}

	public Wing(Record record) {
		super(record);
	}

	
	/**
	 * 新增数据
	 */
	@Override
	public void persist() {
		
		if(getId() == 0){
			setTableId();
		}
		insertNew();
		
		//状态错误
		if(record.getStatus() != DBConsts.RECORD_STATUS_NEW) {
			LogCore.db.error("只有新增包能调用persist函数，请确认状态：data={}, stackTrace={}", this, ExceptionUtils.getStackTrace(new Throwable()));
			return;
		}
		
		DB prx = DB.newInstance(getTableName());
		prx.insert(record);
		
		//重置状态
		record.resetStatus();
	}

	/**
	 * 同步修改数据至DB服务器
	 * 默认不立即持久化到数据库
	 */
	@Override
	public void update() {
		update(false);
	}

	@Override
	public void updateRedis(boolean sync) {
		if(redisSyncTimeInterval == 0L){
			redisSyncTimeInterval = Port.getTime();
		}
		if(sync || (getUpdateObj() != null && Port.getTime() - redisSyncTimeInterval > 5 * 1000L)){// 避免关服瞬间所有玩家一次性写入太多
			redisSyncTimeInterval = Port.getTime();
			updateNew();//别用实时入库（会有先后顺序问题），如果需要则把入库类型改了
		}
	}

	@Override
	public void updateDB(boolean sync) {

		//新增包不能直接调用update函数 请先调用persist
		if(record.getStatus() == DBConsts.RECORD_STATUS_NEW) {
			throw new SysException("新增包不能直接调用update函数，请先调用persist：data={}", this);
		}

		//升级包
		Chunk patch = record.patchUpdateGen();
		if(patch == null || patch.length == 0) return;

		//将升级包同步至DB服务器
		DB prx = DB.newInstance(getTableName());
		prx.update(getId(), patch, sync);

		//回收缓冲包
		patch.release();

		//重置状态
		record.resetStatus();
	}
	
	/**
	 * 同步修改数据至DB服务器
	 * @param sync 是否立即同持久化到数据库
	 */
	@Override
	public void update(boolean sync) {
		
		//新增包不能直接调用update函数 请先调用persist
		if(record.getStatus() == DBConsts.RECORD_STATUS_NEW) {
			throw new SysException("新增包不能直接调用update函数，请先调用persist：data={}", this);
		}
		
		if(getUpdateObj() != null){
			redisSyncTimeInterval = Port.getTime();
			updateNew();//别用实时入库（会有先后顺序问题），如果需要则把入库类型改了
		}
		
		//升级包
		Chunk patch = record.patchUpdateGen();
		if(patch == null || patch.length == 0) return;

		//将升级包同步至DB服务器
		DB prx = DB.newInstance(getTableName());
		prx.update(getId(), patch, sync);
		
		//回收缓冲包
		patch.release();
		
		//重置状态
		record.resetStatus();
	}

	/**
	 * 删除数据
	 */
	@Override
	public void remove() {
		
		deleteNew();
	
		DB prx = DB.newInstance(getTableName());
		prx.delete(getId());
	}

	public void reset() {
		super.reset();
		record.setNewness(false);
		record.resetStatus();
	}

	protected String getKey() {
		return "Wing." + getId();
	}

	

	protected String getListKey() {
		return null;
	}
	
	protected String getListItemKey() {
		return null;
	}

	

	public void doCreate() {
		setTableId();
		insertNew();
	}

	public void setTableId() {
		setId(incrTableId("demo_wing"));
	}

	public JsonObject insertNew() {
		return super.insert(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE, LISTKEY, getListKey(), getListItemKey(), false, tableName);
	}

	public void deleteNew() {
		super.delete(getKey(), getId(), UPDATE_DB_TYPE, 0, LISTKEY, getListKey(), getListItemKey(), false, tableName);
	}

	/**
	* 根据入库类型更新
	*/
	public JsonObject updateNew() {
		return super.update(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE, 0, getAllObjNew(), LISTKEY, getListKey(), getListItemKey(), tableName);
	}

	/**
	* 实时入库
	*/
	public JsonObject updateNowNew() {
		return super.update(getKey(), getId(), REDIS_EXPIRE_TIME, UPDATE_DB_TYPE_NOW, 0, getAllObjNew(), LISTKEY, getListKey(), getListItemKey(), tableName);
	}

	public JsonObject getAllObjNew(){
		JsonObject obj = super.getAllObjNew();
		obj.put(K.id, getId()); // id
		obj.put(K.featherMap, getFeatherMap()); // featherMap
		obj.put(K.skinLvMap, getSkinLvMap()); // skinLvMap
		obj.put(K.talentLvMap, getTalentLvMap()); // talentLvMap
		obj.put(K.currentTab, getCurrentTab()); // currentTab
		obj.put(K.talentTabInfo, getTalentTabInfo()); // talentTabInfo
		obj.put(K.collectionList, getCollectionList()); // collectionList
		obj.put(K.extendJSON, getExtendJSON()); // extendJSON
		return obj;
	}

	/**
	 * id
	 */
	public long getId() {
		return record.get(K.id);
	}

	public void setId(final long id) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.id, id);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.id, id);
	}
	/**
	 * 羽毛sn等级经验Map
	 */
	public String getFeatherMap() {
		return record.get(K.featherMap);
	}

	public void setFeatherMap(final String featherMap) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.featherMap, featherMap);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.featherMap, featherMap);
	}
	/**
	 * 背饰皮肤sn等级Map
	 */
	public String getSkinLvMap() {
		return record.get(K.skinLvMap);
	}

	public void setSkinLvMap(final String skinLvMap) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.skinLvMap, skinLvMap);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.skinLvMap, skinLvMap);
	}
	/**
	 * 背饰sn等级Map,加方案弃用
	 */
	public String getTalentLvMap() {
		return record.get(K.talentLvMap);
	}

	public void setTalentLvMap(final String talentLvMap) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.talentLvMap, talentLvMap);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.talentLvMap, talentLvMap);
	}
	/**
	 * 当前方案
	 */
	public int getCurrentTab() {
		return record.get(K.currentTab);
	}

	public void setCurrentTab(final int currentTab) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.currentTab, currentTab);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.currentTab, currentTab);
	}
	/**
	 * 天赋方案
	 */
	public byte[] getTalentTabInfo() {
		return record.get(K.talentTabInfo);
	}

	public void setTalentTabInfo(final byte[] talentTabInfo) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.talentTabInfo, talentTabInfo);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.talentTabInfo, talentTabInfo);
	}
	/**
	 * 激活的图鉴sn
	 */
	public String getCollectionList() {
		return record.get(K.collectionList);
	}

	public void setCollectionList(final String collectionList) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.collectionList, collectionList);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.collectionList, collectionList);
	}
	/**
	 * 扩展属性(用于处理bug)
	 */
	public String getExtendJSON() {
		return record.get(K.extendJSON);
	}

	public void setExtendJSON(final String extendJSON) {
		
		//更新前的数据状态
		int statusOld = record.getStatus();

		//更新属性
		record.set(K.extendJSON, extendJSON);

		//更新后的数据状态
		int statusNew = record.getStatus();
		//1.如果更新前是普通状态 and 更新后是修改状态，那么就记录这条数据，用来稍后自动提交。
		//2.哪怕之前是修改状态，只要数据是刚创建或串行化过来的新对象，则也会记录修改，因为有些时候会串行化过来一个修改状态下的数据。
		if((statusOld == DBConsts.RECORD_STATUS_NONE && statusNew == DBConsts.RECORD_STATUS_MODIFIED) ||
		   (statusOld == DBConsts.RECORD_STATUS_MODIFIED && record.isNewness())) {
			//记录修改的数据 用来稍后自动提交
			Port.getCurrent().addEntityModify(this);
			Port.getCurrent().addEntityModifyRedis(this);
			//如果是刚创建或串行化过来的新对象 取消这个标示
			if(record.isNewness()) {
				record.setNewness(false);
			}
		}
		
		
		updateRedisHashField("", K.extendJSON, extendJSON);
	}
	 
	
	@Override
	public void writeTo(OutputStream out) throws IOException {
		super.writeTo(out);
		
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		super.readFrom(in);
	}
	
	public void setNew(){
		record.setStatus(DBConsts.RECORD_STATUS_NEW);
	}

		public static String getRedisKeyStr(Object... obj){
		return "Wing." + obj[0];
	}

}