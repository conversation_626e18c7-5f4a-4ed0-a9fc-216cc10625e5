package org.gof.demo.worldsrv.arena;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import java.util.List;
import java.util.Map;

@GofGenFile
public final class ArenaServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_ADDROBOT_MAP_INT = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_ADDROBOTJOMAP_MAP_INT_BOOLEAN = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_ARENABATTLESETTLE_INT_LONG_BOOLEAN_PARAM = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANRANK_INT_LONG_INT_BOOLEAN = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANRANKOLD_INT_LONG = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANROBOTJOMAP_INT = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANROBOTMAP_INT = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETARENARIVAL_LONG_INT_INT = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_LOADSERVER_LIST = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_UPDATE_STRING = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_UPDATE1_OBJECTS = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_UPDATE2_STRING = 12;
	}

	private static final String SERV_ID = "arena";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private ArenaServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		ArenaService serv = (ArenaService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_ADDROBOT_MAP_INT: {
				return (GofFunction2<Map, Integer>)serv::addRobot;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_ADDROBOTJOMAP_MAP_INT_BOOLEAN: {
				return (GofFunction3<Map, Integer, Boolean>)serv::addRobotJoMap;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_ARENABATTLESETTLE_INT_LONG_BOOLEAN_PARAM: {
				return (GofFunction4<Integer, Long, Boolean, Param>)serv::arenaBattleSettle;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANRANK_INT_LONG_INT_BOOLEAN: {
				return (GofFunction4<Integer, Long, Integer, Boolean>)serv::getAreanRank;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANRANKOLD_INT_LONG: {
				return (GofFunction2<Integer, Long>)serv::getAreanRankOld;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANROBOTJOMAP_INT: {
				return (GofFunction1<Integer>)serv::getAreanRobotJoMap;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANROBOTMAP_INT: {
				return (GofFunction1<Integer>)serv::getAreanRobotMap;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETARENARIVAL_LONG_INT_INT: {
				return (GofFunction3<Long, Integer, Integer>)serv::getArenaRival;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_LOADSERVER_LIST: {
				return (GofFunction1<List>)serv::loadServer;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_UPDATE_STRING: {
				return (GofFunction1<String>)serv::update;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_UPDATE1_OBJECTS: {
				return (GofFunction1<Object[]>)serv::update1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_UPDATE2_STRING: {
				return (GofFunction1<String>)serv::update2;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static ArenaServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static ArenaServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static ArenaServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static ArenaServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static ArenaServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static ArenaServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static ArenaServiceProxy createInstance(String node, String port, Object serviceId) {
		ArenaServiceProxy inst = new ArenaServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	@SuppressWarnings("rawtypes")
	/** {@link ArenaService#addRobot(Map robotMap, int serverId)}*/
	public void addRobot(Map robotMap, int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_ADDROBOT_MAP_INT,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_ADDROBOT_MAP_INT", new Object[] {robotMap, serverId});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link ArenaService#addRobotJoMap(Map robotJoMap, int serverId, boolean isNext)}*/
	public void addRobotJoMap(Map robotJoMap, int serverId, boolean isNext) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_ADDROBOTJOMAP_MAP_INT_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_ADDROBOTJOMAP_MAP_INT_BOOLEAN", new Object[] {robotJoMap, serverId, isNext});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaService#arenaBattleSettle(int serverId, long humanId, boolean isWin, Param param)}*/
	public void arenaBattleSettle(int serverId, long humanId, boolean isWin, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_ARENABATTLESETTLE_INT_LONG_BOOLEAN_PARAM,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_ARENABATTLESETTLE_INT_LONG_BOOLEAN_PARAM", new Object[] {serverId, humanId, isWin, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaService#getAreanRank(int serverId, long humanId, int page, boolean isRank)}*/
	public void getAreanRank(int serverId, long humanId, int page, boolean isRank) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANRANK_INT_LONG_INT_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANRANK_INT_LONG_INT_BOOLEAN", new Object[] {serverId, humanId, page, isRank});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaService#getAreanRankOld(int serverId, long humanId)}*/
	public void getAreanRankOld(int serverId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANRANKOLD_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANRANKOLD_INT_LONG", new Object[] {serverId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaService#getAreanRobotJoMap(int serverId)}*/
	public void getAreanRobotJoMap(int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANROBOTJOMAP_INT,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANROBOTJOMAP_INT", new Object[] {serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaService#getAreanRobotMap(int serverId)}*/
	public void getAreanRobotMap(int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANROBOTMAP_INT,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETAREANROBOTMAP_INT", new Object[] {serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaService#getArenaRival(long humanId, int serverId, int winNum)}*/
	public void getArenaRival(long humanId, int serverId, int winNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETARENARIVAL_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_GETARENARIVAL_LONG_INT_INT", new Object[] {humanId, serverId, winNum});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link ArenaService#loadServer(List serverIdList)}*/
	public void loadServer(List serverIdList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_LOADSERVER_LIST,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_LOADSERVER_LIST", new Object[] {serverIdList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaService#update(String jo)}*/
	public void update(String jo) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_UPDATE_STRING,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_UPDATE_STRING", new Object[] {jo});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaService#update1(Object... objs)}*/
	public void update1(Object... objs) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_UPDATE1_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_UPDATE1_OBJECTS", new Object[] {objs});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaService#update2(String str)}*/
	public void update2(String str) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_UPDATE2_STRING,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENASERVICE_UPDATE2_STRING", new Object[] {str});
		if(immutableOnce) immutableOnce = false;
	}
}
