package org.gof.demo.worldsrv.arena;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import java.util.List;
import java.util.Map;
import org.gof.demo.worldsrv.guild.HumanBriefVO;

@GofGenFile
public final class ArenaCrossServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE__MSG_ARENA_COMBAT_C2S_INT_INT_LONG_LONG = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_ADDROBOTJOMAP_MAP = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_ARENABATTLESETTLE_INT_LONG_INT_BOOLEAN_PARAM = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_CHECKRANK_INT_LONG_INT = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANRANK_INT_LONG_INT_INT_BOOLEAN = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANRANKOLDRANK_INT_LONG_INT = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANROBOTJOMAP_INT = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANROBOTMAP_INT = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETARENARIVAL_LONG_INT_INT_INT = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETCROSSARENAROLEINFO_INT_INT_LONG = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GM_OBJECTS = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_ONNODEREGISTER = 12;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATE_STRING = 13;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATE1_OBJECTS = 14;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATE2_STRING = 15;
		public static final int ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATEHUMANBRIEF_HUMANBRIEFVO = 16;
	}

	private static final String SERV_ID = "arenaCross";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private ArenaCrossServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		ArenaCrossService serv = (ArenaCrossService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE__MSG_ARENA_COMBAT_C2S_INT_INT_LONG_LONG: {
				return (GofFunction4<Integer, Integer, Long, Long>)serv::_msg_arena_combat_c2s;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_ADDROBOTJOMAP_MAP: {
				return (GofFunction1<Map>)serv::addRobotJoMap;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_ARENABATTLESETTLE_INT_LONG_INT_BOOLEAN_PARAM: {
				return (GofFunction5<Integer, Long, Integer, Boolean, Param>)serv::arenaBattleSettle;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_CHECKRANK_INT_LONG_INT: {
				return (GofFunction3<Integer, Long, Integer>)serv::checkRank;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANRANK_INT_LONG_INT_INT_BOOLEAN: {
				return (GofFunction5<Integer, Long, Integer, Integer, Boolean>)serv::getAreanRank;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANRANKOLDRANK_INT_LONG_INT: {
				return (GofFunction3<Integer, Long, Integer>)serv::getAreanRankOldRank;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANROBOTJOMAP_INT: {
				return (GofFunction1<Integer>)serv::getAreanRobotJoMap;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANROBOTMAP_INT: {
				return (GofFunction1<Integer>)serv::getAreanRobotMap;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETARENARIVAL_LONG_INT_INT_INT: {
				return (GofFunction4<Long, Integer, Integer, Integer>)serv::getArenaRival;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETCROSSARENAROLEINFO_INT_INT_LONG: {
				return (GofFunction3<Integer, Integer, Long>)serv::getCrossArenaRoleInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GM_OBJECTS: {
				return (GofFunction1<Object[]>)serv::gm;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_ONNODEREGISTER: {
				return (GofFunction0)serv::onNodeRegister;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATE_STRING: {
				return (GofFunction1<String>)serv::update;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATE1_OBJECTS: {
				return (GofFunction1<Object[]>)serv::update1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATE2_STRING: {
				return (GofFunction1<String>)serv::update2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATEHUMANBRIEF_HUMANBRIEFVO: {
				return (GofFunction1<HumanBriefVO>)serv::updateHumanBrief;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static ArenaCrossServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static ArenaCrossServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static ArenaCrossServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static ArenaCrossServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static ArenaCrossServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static ArenaCrossServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static ArenaCrossServiceProxy createInstance(String node, String port, Object serviceId) {
		ArenaCrossServiceProxy inst = new ArenaCrossServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link ArenaCrossService#_msg_arena_combat_c2s(int serverId, int groupId, long humanId, long enemyId)}*/
	public void _msg_arena_combat_c2s(int serverId, int groupId, long humanId, long enemyId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE__MSG_ARENA_COMBAT_C2S_INT_INT_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE__MSG_ARENA_COMBAT_C2S_INT_INT_LONG_LONG", new Object[] {serverId, groupId, humanId, enemyId});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link ArenaCrossService#addRobotJoMap(Map robotJoMap)}*/
	public void addRobotJoMap(Map robotJoMap) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_ADDROBOTJOMAP_MAP,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_ADDROBOTJOMAP_MAP", new Object[] {robotJoMap});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#arenaBattleSettle(int serverId, long humanId, int group, boolean isWin, Param param)}*/
	public void arenaBattleSettle(int serverId, long humanId, int group, boolean isWin, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_ARENABATTLESETTLE_INT_LONG_INT_BOOLEAN_PARAM,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_ARENABATTLESETTLE_INT_LONG_INT_BOOLEAN_PARAM", new Object[] {serverId, humanId, group, isWin, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#checkRank(int serverId, long humanId, int groupId)}*/
	public void checkRank(int serverId, long humanId, int groupId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_CHECKRANK_INT_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_CHECKRANK_INT_LONG_INT", new Object[] {serverId, humanId, groupId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#getAreanRank(int serverId, long humanId, int group, int page, boolean isRank)}*/
	public void getAreanRank(int serverId, long humanId, int group, int page, boolean isRank) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANRANK_INT_LONG_INT_INT_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANRANK_INT_LONG_INT_INT_BOOLEAN", new Object[] {serverId, humanId, group, page, isRank});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#getAreanRankOldRank(int serverId, long humanId, int group)}*/
	public void getAreanRankOldRank(int serverId, long humanId, int group) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANRANKOLDRANK_INT_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANRANKOLDRANK_INT_LONG_INT", new Object[] {serverId, humanId, group});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#getAreanRobotJoMap(int serverId)}*/
	public void getAreanRobotJoMap(int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANROBOTJOMAP_INT,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANROBOTJOMAP_INT", new Object[] {serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#getAreanRobotMap(int serverId)}*/
	public void getAreanRobotMap(int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANROBOTMAP_INT,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETAREANROBOTMAP_INT", new Object[] {serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#getArenaRival(long humanId, int serverId, int group, int winNum)}*/
	public void getArenaRival(long humanId, int serverId, int group, int winNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETARENARIVAL_LONG_INT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETARENARIVAL_LONG_INT_INT_INT", new Object[] {humanId, serverId, group, winNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#getCrossArenaRoleInfo(int serverId, int groupId, long targetId)}*/
	public void getCrossArenaRoleInfo(int serverId, int groupId, long targetId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETCROSSARENAROLEINFO_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GETCROSSARENAROLEINFO_INT_INT_LONG", new Object[] {serverId, groupId, targetId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#gm(Object... args)}*/
	public void gm(Object... args) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GM_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_GM_OBJECTS", new Object[] {args});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#onNodeRegister()}*/
	public void onNodeRegister() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_ONNODEREGISTER,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_ONNODEREGISTER", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#update(String jo)}*/
	public void update(String jo) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATE_STRING,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATE_STRING", new Object[] {jo});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#update1(Object... objs)}*/
	public void update1(Object... objs) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATE1_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATE1_OBJECTS", new Object[] {objs});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#update2(String str)}*/
	public void update2(String str) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATE2_STRING,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATE2_STRING", new Object[] {str});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ArenaCrossService#updateHumanBrief(HumanBriefVO vo)}*/
	public void updateHumanBrief(HumanBriefVO vo) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATEHUMANBRIEF_HUMANBRIEFVO,"ORG_GOF_DEMO_WORLDSRV_ARENA_ARENACROSSSERVICE_UPDATEHUMANBRIEF_HUMANBRIEFVO", new Object[] {vo});
		if(immutableOnce) immutableOnce = false;
	}
}
