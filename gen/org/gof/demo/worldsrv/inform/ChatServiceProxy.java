package org.gof.demo.worldsrv.inform;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import java.util.Map;
import java.util.List;
import org.gof.demo.worldsrv.inform.RedInfo;
import org.gof.demo.worldsrv.msg.Define.p_brief_red;

@GofGenFile
public final class ChatServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GMNOTICE_LONG_INT_INT_STRING = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ADDCHAT_CHATINFO_INT = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ADDITEMERROR_MAP = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ALLSENDMSG_INT = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_CREATERED_INT_INT_INT_P_BRIEF_RED_STRING = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETFACTIONCHATLOG_LONG_LONG_INT = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETMARQUEELIST = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETPRIVATECHATLOG_LONG_LIST = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETPRIVATECHATLOG_LONG_LONG_LONG_INT = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETWORLDCHATLOG_LONG_INT = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_LOTTERYRED_LONG_P_BRIEF_RED = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ONLOGWORLDCHAT = 12;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_RECALLNOTICE_STRING = 13;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_SENDMARQUEE_STRING = 14;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_UPITEMERRORLOG = 15;
		public static final int ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_UPLOADCHAT = 16;
	}

	private static final String SERV_ID = "chat";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private ChatServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		ChatService serv = (ChatService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GMNOTICE_LONG_INT_INT_STRING: {
				return (GofFunction4<Long, Integer, Integer, String>)serv::GMNotice;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ADDCHAT_CHATINFO_INT: {
				return (GofFunction2<ChatInfo, Integer>)serv::addChat;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ADDITEMERROR_MAP: {
				return (GofFunction1<Map>)serv::addItemError;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ALLSENDMSG_INT: {
				return (GofFunction1<Integer>)serv::allSendMsg;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_CREATERED_INT_INT_INT_P_BRIEF_RED_STRING: {
				return (GofFunction5<Integer, Integer, Integer, p_brief_red, String>)serv::createRed;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETFACTIONCHATLOG_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::getFactionChatLog;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETMARQUEELIST: {
				return (GofFunction0)serv::getMarqueeList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETPRIVATECHATLOG_LONG_LIST: {
				return (GofFunction2<Long, List>)serv::getPrivateChatLog;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETPRIVATECHATLOG_LONG_LONG_LONG_INT: {
				return (GofFunction4<Long, Long, Long, Integer>)serv::getPrivateChatLog;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETWORLDCHATLOG_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::getWorldChatLog;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_LOTTERYRED_LONG_P_BRIEF_RED: {
				return (GofFunction2<Long, p_brief_red>)serv::lotteryRed;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ONLOGWORLDCHAT: {
				return (GofFunction0)serv::onLogworldChat;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_RECALLNOTICE_STRING: {
				return (GofFunction1<String>)serv::recallNotice;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_SENDMARQUEE_STRING: {
				return (GofFunction1<String>)serv::sendMarquee;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_UPITEMERRORLOG: {
				return (GofFunction0)serv::upItemErrorLog;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_UPLOADCHAT: {
				return (GofFunction0)serv::uploadChat;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static ChatServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static ChatServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static ChatServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static ChatServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static ChatServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static ChatServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static ChatServiceProxy createInstance(String node, String port, Object serviceId) {
		ChatServiceProxy inst = new ChatServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link ChatService#GMNotice(long time, int split, int count, String content)}*/
	public void GMNotice(long time, int split, int count, String content) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GMNOTICE_LONG_INT_INT_STRING,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GMNOTICE_LONG_INT_INT_STRING", new Object[] {time, split, count, content});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ChatService#addChat(ChatInfo chat, int level)}*/
	public void addChat(ChatInfo chat, int level) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ADDCHAT_CHATINFO_INT,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ADDCHAT_CHATINFO_INT", new Object[] {chat, level});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link ChatService#addItemError(Map params)}*/
	public void addItemError(Map params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ADDITEMERROR_MAP,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ADDITEMERROR_MAP", new Object[] {params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ChatService#allSendMsg(int zone)}*/
	public void allSendMsg(int zone) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ALLSENDMSG_INT,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ALLSENDMSG_INT", new Object[] {zone});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ChatService#createRed(int zone, int sumNum, int num, p_brief_red sendInfo, String param)}*/
	public void createRed(int zone, int sumNum, int num, p_brief_red sendInfo, String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_CREATERED_INT_INT_INT_P_BRIEF_RED_STRING,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_CREATERED_INT_INT_INT_P_BRIEF_RED_STRING", new Object[] {zone, sumNum, num, sendInfo, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ChatService#getFactionChatLog(long factionId, long timestamp, int num)}*/
	public void getFactionChatLog(long factionId, long timestamp, int num) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETFACTIONCHATLOG_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETFACTIONCHATLOG_LONG_LONG_INT", new Object[] {factionId, timestamp, num});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ChatService#getMarqueeList()}*/
	public void getMarqueeList() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETMARQUEELIST,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETMARQUEELIST", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link ChatService#getPrivateChatLog(long humanId, List toHumanIds)}*/
	public void getPrivateChatLog(long humanId, List toHumanIds) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETPRIVATECHATLOG_LONG_LIST,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETPRIVATECHATLOG_LONG_LIST", new Object[] {humanId, toHumanIds});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ChatService#getPrivateChatLog(long humanId, long toHumanId, long timeStamp, int num)}*/
	public void getPrivateChatLog(long humanId, long toHumanId, long timeStamp, int num) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETPRIVATECHATLOG_LONG_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETPRIVATECHATLOG_LONG_LONG_LONG_INT", new Object[] {humanId, toHumanId, timeStamp, num});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ChatService#getWorldChatLog(long timestamp, int num)}*/
	public void getWorldChatLog(long timestamp, int num) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETWORLDCHATLOG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_GETWORLDCHATLOG_LONG_INT", new Object[] {timestamp, num});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ChatService#lotteryRed(long id, p_brief_red dInfo)}*/
	public void lotteryRed(long id, p_brief_red dInfo) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_LOTTERYRED_LONG_P_BRIEF_RED,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_LOTTERYRED_LONG_P_BRIEF_RED", new Object[] {id, dInfo});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ChatService#onLogworldChat()}*/
	public void onLogworldChat() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ONLOGWORLDCHAT,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_ONLOGWORLDCHAT", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ChatService#recallNotice(String content)}*/
	public void recallNotice(String content) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_RECALLNOTICE_STRING,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_RECALLNOTICE_STRING", new Object[] {content});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ChatService#sendMarquee(String json)}*/
	public void sendMarquee(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_SENDMARQUEE_STRING,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_SENDMARQUEE_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ChatService#upItemErrorLog()}*/
	public void upItemErrorLog() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_UPITEMERRORLOG,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_UPITEMERRORLOG", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ChatService#uploadChat()}*/
	public void uploadChat() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_UPLOADCHAT,"ORG_GOF_DEMO_WORLDSRV_INFORM_CHATSERVICE_UPLOADCHAT", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}
}
