package org.gof.demo.worldsrv.activity;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import java.util.List;

@GofGenFile
public final class ActivityControlServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTBOSSADDDMG_INT_INT_LONG_BOOLEAN = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTCOHESIONADDPROCESS_INT_INT_INT = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTCOHESIONLEAVEGUILD_LONG_LONG = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTGUILDPAYLEAVEGUILD_INT_LONG_LONG = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTGUILDPAYRECORD_INT_INT_LONG_LONG = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_CANCELGROUPGIFT_LONG_LONG = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETGROUPGIFT_INT_INT = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETGUILDPAYCOUNT_INT_INT_LONG = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETOPENACTIVITYLIST_INT = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETSERVERACTDATA_INT_INT = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GMACTIVITY_STRINGS = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_JOINGROUPGIFT_LONG_STRING_LONG = 12;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_LOADGROUPGIFT_INT_INT = 13;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_LOADSERVER_LIST = 14;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_STARTGROUPGIFT_LONG_STRING_INT_INT_INT_LONG = 15;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_SUPPORTCAMP_INT_INT_INT_INT_LONG_BOOLEAN = 16;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_UPDATE_STRING = 17;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_UPDATE1_OBJECTS = 18;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_UPDATE2_STRING = 19;
		public static final int ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_WATERSERVERTREE_INT_INT_INT_LONG = 20;
	}

	private static final String SERV_ID = "activity";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private ActivityControlServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		ActivityControlService serv = (ActivityControlService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTBOSSADDDMG_INT_INT_LONG_BOOLEAN: {
				return (GofFunction4<Integer, Integer, Long, Boolean>)serv::actBossAddDmg;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTCOHESIONADDPROCESS_INT_INT_INT: {
				return (GofFunction3<Integer, Integer, Integer>)serv::actCohesionAddProcess;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTCOHESIONLEAVEGUILD_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::actCohesionLeaveGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTGUILDPAYLEAVEGUILD_INT_LONG_LONG: {
				return (GofFunction3<Integer, Long, Long>)serv::actGuildPayLeaveGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTGUILDPAYRECORD_INT_INT_LONG_LONG: {
				return (GofFunction4<Integer, Integer, Long, Long>)serv::actGuildPayRecord;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_CANCELGROUPGIFT_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::cancelGroupGift;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETGROUPGIFT_INT_INT: {
				return (GofFunction2<Integer, Integer>)serv::getGroupGift;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETGUILDPAYCOUNT_INT_INT_LONG: {
				return (GofFunction3<Integer, Integer, Long>)serv::getGuildPayCount;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETOPENACTIVITYLIST_INT: {
				return (GofFunction1<Integer>)serv::getOpenActivityList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETSERVERACTDATA_INT_INT: {
				return (GofFunction2<Integer, Integer>)serv::getServerActData;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GMACTIVITY_STRINGS: {
				return (GofFunction1<String[]>)serv::gmActivity;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_JOINGROUPGIFT_LONG_STRING_LONG: {
				return (GofFunction3<Long, String, Long>)serv::joinGroupGift;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_LOADGROUPGIFT_INT_INT: {
				return (GofFunction2<Integer, Integer>)serv::loadGroupGift;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_LOADSERVER_LIST: {
				return (GofFunction1<List>)serv::loadServer;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_STARTGROUPGIFT_LONG_STRING_INT_INT_INT_LONG: {
				return (GofFunction6<Long, String, Integer, Integer, Integer, Long>)serv::startGroupGift;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_SUPPORTCAMP_INT_INT_INT_INT_LONG_BOOLEAN: {
				return (GofFunction6<Integer, Integer, Integer, Integer, Long, Boolean>)serv::supportCamp;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_UPDATE_STRING: {
				return (GofFunction1<String>)serv::update;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_UPDATE1_OBJECTS: {
				return (GofFunction1<Object[]>)serv::update1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_UPDATE2_STRING: {
				return (GofFunction1<String>)serv::update2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_WATERSERVERTREE_INT_INT_INT_LONG: {
				return (GofFunction4<Integer, Integer, Integer, Long>)serv::waterServerTree;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static ActivityControlServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static ActivityControlServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static ActivityControlServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static ActivityControlServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static ActivityControlServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static ActivityControlServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static ActivityControlServiceProxy createInstance(String node, String port, Object serviceId) {
		ActivityControlServiceProxy inst = new ActivityControlServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link ActivityControlService#actBossAddDmg(int serverId, int actSn, long dmg, boolean isAuto)}*/
	public void actBossAddDmg(int serverId, int actSn, long dmg, boolean isAuto) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTBOSSADDDMG_INT_INT_LONG_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTBOSSADDDMG_INT_INT_LONG_BOOLEAN", new Object[] {serverId, actSn, dmg, isAuto});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#actCohesionAddProcess(int serverId, int actSn, int num)}*/
	public void actCohesionAddProcess(int serverId, int actSn, int num) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTCOHESIONADDPROCESS_INT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTCOHESIONADDPROCESS_INT_INT_INT", new Object[] {serverId, actSn, num});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#actCohesionLeaveGuild(long humanId, long guildId)}*/
	public void actCohesionLeaveGuild(long humanId, long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTCOHESIONLEAVEGUILD_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTCOHESIONLEAVEGUILD_LONG_LONG", new Object[] {humanId, guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#actGuildPayLeaveGuild(int serverId, long guildId, long humanId)}*/
	public void actGuildPayLeaveGuild(int serverId, long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTGUILDPAYLEAVEGUILD_INT_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTGUILDPAYLEAVEGUILD_INT_LONG_LONG", new Object[] {serverId, guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#actGuildPayRecord(int serverId, int actSn, long guildId, long humanId)}*/
	public void actGuildPayRecord(int serverId, int actSn, long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTGUILDPAYRECORD_INT_INT_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_ACTGUILDPAYRECORD_INT_INT_LONG_LONG", new Object[] {serverId, actSn, guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#cancelGroupGift(long humanId, long giftId)}*/
	public void cancelGroupGift(long humanId, long giftId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_CANCELGROUPGIFT_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_CANCELGROUPGIFT_LONG_LONG", new Object[] {humanId, giftId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#getGroupGift(int actType, int round)}*/
	public void getGroupGift(int actType, int round) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETGROUPGIFT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETGROUPGIFT_INT_INT", new Object[] {actType, round});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#getGuildPayCount(int serverId, int actSn, long guildId)}*/
	public void getGuildPayCount(int serverId, int actSn, long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETGUILDPAYCOUNT_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETGUILDPAYCOUNT_INT_INT_LONG", new Object[] {serverId, actSn, guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#getOpenActivityList(int serverId)}*/
	public void getOpenActivityList(int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETOPENACTIVITYLIST_INT,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETOPENACTIVITYLIST_INT", new Object[] {serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#getServerActData(int serverId, int actSn)}*/
	public void getServerActData(int serverId, int actSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETSERVERACTDATA_INT_INT,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GETSERVERACTDATA_INT_INT", new Object[] {serverId, actSn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#gmActivity(String... params)}*/
	public void gmActivity(String... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GMACTIVITY_STRINGS,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_GMACTIVITY_STRINGS", new Object[] {params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#joinGroupGift(long humanId, String humanName, long giftId)}*/
	public void joinGroupGift(long humanId, String humanName, long giftId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_JOINGROUPGIFT_LONG_STRING_LONG,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_JOINGROUPGIFT_LONG_STRING_LONG", new Object[] {humanId, humanName, giftId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#loadGroupGift(int actType, int round)}*/
	public void loadGroupGift(int actType, int round) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_LOADGROUPGIFT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_LOADGROUPGIFT_INT_INT", new Object[] {actType, round});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link ActivityControlService#loadServer(List serverIdList)}*/
	public void loadServer(List serverIdList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_LOADSERVER_LIST,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_LOADSERVER_LIST", new Object[] {serverIdList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#startGroupGift(long humanId, String humanName, int groupSn, int actSn, int round, long actCloseTime)}*/
	public void startGroupGift(long humanId, String humanName, int groupSn, int actSn, int round, long actCloseTime) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_STARTGROUPGIFT_LONG_STRING_INT_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_STARTGROUPGIFT_LONG_STRING_INT_INT_INT_LONG", new Object[] {humanId, humanName, groupSn, actSn, round, actCloseTime});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#supportCamp(int serverId, int actSn, int group, int campId, long dmg, boolean isAuto)}*/
	public void supportCamp(int serverId, int actSn, int group, int campId, long dmg, boolean isAuto) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_SUPPORTCAMP_INT_INT_INT_INT_LONG_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_SUPPORTCAMP_INT_INT_INT_INT_LONG_BOOLEAN", new Object[] {serverId, actSn, group, campId, dmg, isAuto});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#update(String jo)}*/
	public void update(String jo) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_UPDATE_STRING,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_UPDATE_STRING", new Object[] {jo});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#update1(Object... params)}*/
	public void update1(Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_UPDATE1_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_UPDATE1_OBJECTS", new Object[] {params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#update2(String json)}*/
	public void update2(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_UPDATE2_STRING,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_UPDATE2_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link ActivityControlService#waterServerTree(int serverId, int actSn, int groupId, long dmg)}*/
	public void waterServerTree(int serverId, int actSn, int groupId, long dmg) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_WATERSERVERTREE_INT_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_ACTIVITY_ACTIVITYCONTROLSERVICE_WATERSERVERTREE_INT_INT_INT_LONG", new Object[] {serverId, actSn, groupId, dmg});
		if(immutableOnce) immutableOnce = false;
	}
}
