package org.gof.demo.worldsrv.flyPet.hybrid;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import java.util.List;
import java.util.Map;
import org.gof.demo.worldsrv.flyPet.FlyPetInfo;
import org.gof.demo.worldsrv.flyPet.hybrid.FlyPetHybridInfo;

@GofGenFile
public final class FlyHybridServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_ADDFLYPETPARTNERASK_LONG_LONG_BOOLEAN_LONG = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_ADDFLYPETPERMISSION_LONG_LONG = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_ADDFLYPETPERMISSIONASK_LONG_LONG = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_FLYPETHYBRIDREPORT_LONG_BOOLEAN = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_GETFLYHYBRIDPETINFO_LONG = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_GETFLYHYBRIDPETINFOMAP_LIST = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_GETPARTNERFLYPETINFO_LONG_BOOLEAN = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_LOCKFLYPETHYBRID_LONG_LONG_LONG_LONG_BOOLEAN = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPDATEFLYPETATTRINFO_LONG_FLYPETINFO = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPDATEFLYPETPRIVILEGE_LONG_INT_LONG = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPDATEHUMANFLYPETPARTNERSETTING_LONG_MAP = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPLOADFLYPETINFO_LONG_FLYPETHYBRIDINFO_INT = 12;
	}

	private static final String SERV_ID = "flyHybrid";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private FlyHybridServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		FlyHybridService serv = (FlyHybridService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_ADDFLYPETPARTNERASK_LONG_LONG_BOOLEAN_LONG: {
				return (GofFunction4<Long, Long, Boolean, Long>)serv::addFlyPetPartnerAsk;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_ADDFLYPETPERMISSION_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::addFlyPetPermission;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_ADDFLYPETPERMISSIONASK_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::addFlyPetPermissionAsk;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_FLYPETHYBRIDREPORT_LONG_BOOLEAN: {
				return (GofFunction2<Long, Boolean>)serv::flyPetHybridReport;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_GETFLYHYBRIDPETINFO_LONG: {
				return (GofFunction1<Long>)serv::getFlyHybridPetInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_GETFLYHYBRIDPETINFOMAP_LIST: {
				return (GofFunction1<List>)serv::getFlyHybridPetInfoMap;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_GETPARTNERFLYPETINFO_LONG_BOOLEAN: {
				return (GofFunction2<Long, Boolean>)serv::getPartnerFlyPetInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_LOCKFLYPETHYBRID_LONG_LONG_LONG_LONG_BOOLEAN: {
				return (GofFunction5<Long, Long, Long, Long, Boolean>)serv::lockFlyPetHybrid;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPDATEFLYPETATTRINFO_LONG_FLYPETINFO: {
				return (GofFunction2<Long, FlyPetInfo>)serv::updateFlyPetAttrInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPDATEFLYPETPRIVILEGE_LONG_INT_LONG: {
				return (GofFunction3<Long, Integer, Long>)serv::updateFlyPetPrivilege;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPDATEHUMANFLYPETPARTNERSETTING_LONG_MAP: {
				return (GofFunction2<Long, Map>)serv::updateHumanFlyPetPartnerSetting;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPLOADFLYPETINFO_LONG_FLYPETHYBRIDINFO_INT: {
				return (GofFunction3<Long, FlyPetHybridInfo, Integer>)serv::uploadFlyPetInfo;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static FlyHybridServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static FlyHybridServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static FlyHybridServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static FlyHybridServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static FlyHybridServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static FlyHybridServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static FlyHybridServiceProxy createInstance(String node, String port, Object serviceId) {
		FlyHybridServiceProxy inst = new FlyHybridServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link FlyHybridService#addFlyPetPartnerAsk(long humanId, long targetId, boolean isFriend, long humanGuildId)}*/
	public void addFlyPetPartnerAsk(long humanId, long targetId, boolean isFriend, long humanGuildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_ADDFLYPETPARTNERASK_LONG_LONG_BOOLEAN_LONG,"ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_ADDFLYPETPARTNERASK_LONG_LONG_BOOLEAN_LONG", new Object[] {humanId, targetId, isFriend, humanGuildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link FlyHybridService#addFlyPetPermission(long humanId, long petId)}*/
	public void addFlyPetPermission(long humanId, long petId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_ADDFLYPETPERMISSION_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_ADDFLYPETPERMISSION_LONG_LONG", new Object[] {humanId, petId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link FlyHybridService#addFlyPetPermissionAsk(long humanId, long petId)}*/
	public void addFlyPetPermissionAsk(long humanId, long petId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_ADDFLYPETPERMISSIONASK_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_ADDFLYPETPERMISSIONASK_LONG_LONG", new Object[] {humanId, petId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link FlyHybridService#flyPetHybridReport(long petId, boolean isSuccess)}*/
	public void flyPetHybridReport(long petId, boolean isSuccess) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_FLYPETHYBRIDREPORT_LONG_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_FLYPETHYBRIDREPORT_LONG_BOOLEAN", new Object[] {petId, isSuccess});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link FlyHybridService#getFlyHybridPetInfo(long id)}*/
	public void getFlyHybridPetInfo(long id) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_GETFLYHYBRIDPETINFO_LONG,"ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_GETFLYHYBRIDPETINFO_LONG", new Object[] {id});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link FlyHybridService#getFlyHybridPetInfoMap(List idList)}*/
	public void getFlyHybridPetInfoMap(List idList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_GETFLYHYBRIDPETINFOMAP_LIST,"ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_GETFLYHYBRIDPETINFOMAP_LIST", new Object[] {idList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link FlyHybridService#getPartnerFlyPetInfo(long partnerId, boolean isPartner)}*/
	public void getPartnerFlyPetInfo(long partnerId, boolean isPartner) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_GETPARTNERFLYPETINFO_LONG_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_GETPARTNERFLYPETINFO_LONG_BOOLEAN", new Object[] {partnerId, isPartner});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link FlyHybridService#lockFlyPetHybrid(long humanId, long petId, long startTime, long endTime, boolean isParnter)}*/
	public void lockFlyPetHybrid(long humanId, long petId, long startTime, long endTime, boolean isParnter) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_LOCKFLYPETHYBRID_LONG_LONG_LONG_LONG_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_LOCKFLYPETHYBRID_LONG_LONG_LONG_LONG_BOOLEAN", new Object[] {humanId, petId, startTime, endTime, isParnter});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link FlyHybridService#updateFlyPetAttrInfo(long humanId, FlyPetInfo info)}*/
	public void updateFlyPetAttrInfo(long humanId, FlyPetInfo info) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPDATEFLYPETATTRINFO_LONG_FLYPETINFO,"ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPDATEFLYPETATTRINFO_LONG_FLYPETINFO", new Object[] {humanId, info});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link FlyHybridService#updateFlyPetPrivilege(long humanId, int effectValue, long privilegeEndTime)}*/
	public void updateFlyPetPrivilege(long humanId, int effectValue, long privilegeEndTime) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPDATEFLYPETPRIVILEGE_LONG_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPDATEFLYPETPRIVILEGE_LONG_INT_LONG", new Object[] {humanId, effectValue, privilegeEndTime});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link FlyHybridService#updateHumanFlyPetPartnerSetting(long humanId, Map settingMap)}*/
	public void updateHumanFlyPetPartnerSetting(long humanId, Map settingMap) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPDATEHUMANFLYPETPARTNERSETTING_LONG_MAP,"ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPDATEHUMANFLYPETPARTNERSETTING_LONG_MAP", new Object[] {humanId, settingMap});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link FlyHybridService#uploadFlyPetInfo(long humanId, FlyPetHybridInfo petInfo, int type)}*/
	public void uploadFlyPetInfo(long humanId, FlyPetHybridInfo petInfo, int type) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPLOADFLYPETINFO_LONG_FLYPETHYBRIDINFO_INT,"ORG_GOF_DEMO_WORLDSRV_FLYPET_HYBRID_FLYHYBRIDSERVICE_UPLOADFLYPETINFO_LONG_FLYPETHYBRIDINFO_INT", new Object[] {humanId, petInfo, type});
		if(immutableOnce) immutableOnce = false;
	}
}
