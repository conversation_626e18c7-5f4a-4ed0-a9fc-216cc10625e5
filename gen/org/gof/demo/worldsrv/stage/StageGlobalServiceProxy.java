package org.gof.demo.worldsrv.stage;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import org.gof.demo.worldsrv.character.HumanObject;
import java.util.List;
import org.gof.demo.battlesrv.support.Vector2D;
import com.google.protobuf.Message;
import org.gof.core.support.Param;
import java.util.Map;
import org.gof.demo.worldsrv.stage.StageHistory;

@GofGenFile
public final class StageGlobalServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE__SWITCHSTAGEENTER_HUMANOBJECT_LONG_PARAM = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_APPLYSTAGEBYSN_LONG_PARAM = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKMETHOD1_STRING = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKMETHOD2_STRING = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKUPMETHOD3_STRING = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKUPMETHOD4_STRING = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BATCHSTAGEOBJECTEVENT_LIST_INT_PARAM = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BRIDGESWITCHSTAGELEAVE_HUMANOBJECT_LONG = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CANLOGINBRIDGESTAGE_LIST = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CHECKSTAGEENTER_LONG_INT = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CHECKSTAGEMAPENTER_LONG_INT_INT = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CHECKSTAGEMAPENTER_LONG_INT = 12;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_DESTROY_LONG = 13;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_END_LONG = 14;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GETINFO_INT_INT = 15;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GETLEAGUEWAITTINGZONEHUMANNUM_LONG = 16;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GETSTAGEID_LONG = 17;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GMGETMAP_INT = 18;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GMKICKMAP_INT = 19;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GM_GETMAPSN_INT = 20;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_INFOCANCEL_LONG = 21;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_INFOREGISTER_LONG_INT_STRING_STRING_STRING_INT_INT = 22;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_ISSWICHEXTENSIONLINENUM_LONG = 23;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_LOGIN_LONG_PARAM = 24;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_ONSTAGEOBJECTEVENT_INT_INT_PARAM = 25;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_QUITTOCOMMON_HUMANOBJECT_INT_INT_OBJECTS = 26;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SENDMSGTOMAP_MESSAGE_INT_VECTOR2D = 27;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SETEND_LONG = 28;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SETMONSTERHP_INT_INT_LONG = 29;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_STAGEHUMANNUMADD_LONG = 30;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_STAGEHUMANNUMREDUCE_LONG = 31;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SWITCHTOSTAGE_HUMANOBJECT_LONG_OBJECTS = 32;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_UPDATETEAMIDHUMANIDLIST_LONG_LIST = 33;
	}

	private static final String SERV_ID = "stageGlobal";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private StageGlobalServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		StageGlobalService serv = (StageGlobalService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE__SWITCHSTAGEENTER_HUMANOBJECT_LONG_PARAM: {
				return (GofFunction3<HumanObject, Long, Param>)serv::_switchStageEnter;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_APPLYSTAGEBYSN_LONG_PARAM: {
				return (GofFunction2<Long, Param>)serv::applyStageBySn;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKMETHOD1_STRING: {
				return (GofFunction1<String>)serv::backMethod1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKMETHOD2_STRING: {
				return (GofFunction1<String>)serv::backMethod2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKUPMETHOD3_STRING: {
				return (GofFunction1<String>)serv::backupMethod3;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKUPMETHOD4_STRING: {
				return (GofFunction1<String>)serv::backupMethod4;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BATCHSTAGEOBJECTEVENT_LIST_INT_PARAM: {
				return (GofFunction3<List, Integer, Param>)serv::batchStageObjectEvent;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BRIDGESWITCHSTAGELEAVE_HUMANOBJECT_LONG: {
				return (GofFunction2<HumanObject, Long>)serv::bridgeSwitchStageLeave;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CANLOGINBRIDGESTAGE_LIST: {
				return (GofFunction1<List>)serv::canLoginBridgeStage;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CHECKSTAGEENTER_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::checkStageEnter;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CHECKSTAGEMAPENTER_LONG_INT_INT: {
				return (GofFunction3<Long, Integer, Integer>)serv::checkStageMapEnter;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CHECKSTAGEMAPENTER_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::checkStageMapEnter;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_DESTROY_LONG: {
				return (GofFunction1<Long>)serv::destroy;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_END_LONG: {
				return (GofFunction1<Long>)serv::end;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GETINFO_INT_INT: {
				return (GofFunction2<Integer, Integer>)serv::getInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GETLEAGUEWAITTINGZONEHUMANNUM_LONG: {
				return (GofFunction1<Long>)serv::getLeagueWaittingZoneHumanNum;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GETSTAGEID_LONG: {
				return (GofFunction1<Long>)serv::getStageId;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GMGETMAP_INT: {
				return (GofFunction1<Integer>)serv::gmGetMap;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GMKICKMAP_INT: {
				return (GofFunction1<Integer>)serv::gmKickMap;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GM_GETMAPSN_INT: {
				return (GofFunction1<Integer>)serv::gm_getMapSn;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_INFOCANCEL_LONG: {
				return (GofFunction1<Long>)serv::infoCancel;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_INFOREGISTER_LONG_INT_STRING_STRING_STRING_INT_INT: {
				return (GofFunction7<Long, Integer, String, String, String, Integer, Integer>)serv::infoRegister;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_ISSWICHEXTENSIONLINENUM_LONG: {
				return (GofFunction1<Long>)serv::isSwichExtensionLineNum;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_LOGIN_LONG_PARAM: {
				return (GofFunction2<Long, Param>)serv::login;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_ONSTAGEOBJECTEVENT_INT_INT_PARAM: {
				return (GofFunction3<Integer, Integer, Param>)serv::onStageObjectEvent;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_QUITTOCOMMON_HUMANOBJECT_INT_INT_OBJECTS: {
				return (GofFunction4<HumanObject, Integer, Integer, Object[]>)serv::quitToCommon;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SENDMSGTOMAP_MESSAGE_INT_VECTOR2D: {
				return (GofFunction3<Message, Integer, Vector2D>)serv::sendMsgToMap;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SETEND_LONG: {
				return (GofFunction1<Long>)serv::setEnd;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SETMONSTERHP_INT_INT_LONG: {
				return (GofFunction3<Integer, Integer, Long>)serv::setMonsterHp;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_STAGEHUMANNUMADD_LONG: {
				return (GofFunction1<Long>)serv::stageHumanNumAdd;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_STAGEHUMANNUMREDUCE_LONG: {
				return (GofFunction1<Long>)serv::stageHumanNumReduce;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SWITCHTOSTAGE_HUMANOBJECT_LONG_OBJECTS: {
				return (GofFunction3<HumanObject, Long, Object[]>)serv::switchToStage;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_UPDATETEAMIDHUMANIDLIST_LONG_LIST: {
				return (GofFunction2<Long, List>)serv::updateTeamIdHumanIdList;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static StageGlobalServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static StageGlobalServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static StageGlobalServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static StageGlobalServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static StageGlobalServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static StageGlobalServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static StageGlobalServiceProxy createInstance(String node, String port, Object serviceId) {
		StageGlobalServiceProxy inst = new StageGlobalServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link StageGlobalService#_switchStageEnter(HumanObject humanObj, long stageTargetId, Param param)}*/
	public void _switchStageEnter(HumanObject humanObj, long stageTargetId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE__SWITCHSTAGEENTER_HUMANOBJECT_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE__SWITCHSTAGEENTER_HUMANOBJECT_LONG_PARAM", new Object[] {humanObj, stageTargetId, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#applyStageBySn(long stageId, Param param)}*/
	public void applyStageBySn(long stageId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_APPLYSTAGEBYSN_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_APPLYSTAGEBYSN_LONG_PARAM", new Object[] {stageId, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#backMethod1(String param)}*/
	public void backMethod1(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKMETHOD1_STRING,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKMETHOD1_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#backMethod2(String param)}*/
	public void backMethod2(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKMETHOD2_STRING,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKMETHOD2_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#backupMethod3(String param)}*/
	public void backupMethod3(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKUPMETHOD3_STRING,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKUPMETHOD3_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#backupMethod4(String param)}*/
	public void backupMethod4(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKUPMETHOD4_STRING,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BACKUPMETHOD4_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link StageGlobalService#batchStageObjectEvent(List mapList, int key, Param param)}*/
	public void batchStageObjectEvent(List mapList, int key, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BATCHSTAGEOBJECTEVENT_LIST_INT_PARAM,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BATCHSTAGEOBJECTEVENT_LIST_INT_PARAM", new Object[] {mapList, key, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#bridgeSwitchStageLeave(HumanObject humanObj, long stageTargetId)}*/
	public void bridgeSwitchStageLeave(HumanObject humanObj, long stageTargetId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BRIDGESWITCHSTAGELEAVE_HUMANOBJECT_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_BRIDGESWITCHSTAGELEAVE_HUMANOBJECT_LONG", new Object[] {humanObj, stageTargetId});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link StageGlobalService#canLoginBridgeStage(List stageHistoryList)}*/
	public void canLoginBridgeStage(List stageHistoryList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CANLOGINBRIDGESTAGE_LIST,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CANLOGINBRIDGESTAGE_LIST", new Object[] {stageHistoryList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#checkStageEnter(long stageId, int repSn)}*/
	public void checkStageEnter(long stageId, int repSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CHECKSTAGEENTER_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CHECKSTAGEENTER_LONG_INT", new Object[] {stageId, repSn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#checkStageMapEnter(long stageId, int mapSn, int type)}*/
	public void checkStageMapEnter(long stageId, int mapSn, int type) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CHECKSTAGEMAPENTER_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CHECKSTAGEMAPENTER_LONG_INT_INT", new Object[] {stageId, mapSn, type});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#checkStageMapEnter(long stageId, int mapSn)}*/
	public void checkStageMapEnter(long stageId, int mapSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CHECKSTAGEMAPENTER_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_CHECKSTAGEMAPENTER_LONG_INT", new Object[] {stageId, mapSn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#destroy(long stageId)}*/
	public void destroy(long stageId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_DESTROY_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_DESTROY_LONG", new Object[] {stageId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#end(long stageId)}*/
	public void end(long stageId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_END_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_END_LONG", new Object[] {stageId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#getInfo(int mapSn, int line)}*/
	public void getInfo(int mapSn, int line) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GETINFO_INT_INT,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GETINFO_INT_INT", new Object[] {mapSn, line});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#getLeagueWaittingZoneHumanNum(long stageId)}*/
	public void getLeagueWaittingZoneHumanNum(long stageId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GETLEAGUEWAITTINGZONEHUMANNUM_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GETLEAGUEWAITTINGZONEHUMANNUM_LONG", new Object[] {stageId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#getStageId(long stageId)}*/
	public void getStageId(long stageId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GETSTAGEID_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GETSTAGEID_LONG", new Object[] {stageId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#gmGetMap(int mapSn)}*/
	public void gmGetMap(int mapSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GMGETMAP_INT,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GMGETMAP_INT", new Object[] {mapSn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#gmKickMap(int mapSn)}*/
	public void gmKickMap(int mapSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GMKICKMAP_INT,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GMKICKMAP_INT", new Object[] {mapSn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#gm_getMapSn(int mapSn)}*/
	public void gm_getMapSn(int mapSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GM_GETMAPSN_INT,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_GM_GETMAPSN_INT", new Object[] {mapSn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#infoCancel(long stageId)}*/
	public void infoCancel(long stageId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_INFOCANCEL_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_INFOCANCEL_LONG", new Object[] {stageId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#infoRegister(long stageId, int mapSn, String stageName, String nodeId, String portId, int zone, int lineNum)}*/
	public void infoRegister(long stageId, int mapSn, String stageName, String nodeId, String portId, int zone, int lineNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_INFOREGISTER_LONG_INT_STRING_STRING_STRING_INT_INT,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_INFOREGISTER_LONG_INT_STRING_STRING_STRING_INT_INT", new Object[] {stageId, mapSn, stageName, nodeId, portId, zone, lineNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#isSwichExtensionLineNum(long stageId)}*/
	public void isSwichExtensionLineNum(long stageId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_ISSWICHEXTENSIONLINENUM_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_ISSWICHEXTENSIONLINENUM_LONG", new Object[] {stageId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#login(long humanId, Param param)}*/
	public void login(long humanId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_LOGIN_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_LOGIN_LONG_PARAM", new Object[] {humanId, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#onStageObjectEvent(int mapSn, int key, Param param)}*/
	public void onStageObjectEvent(int mapSn, int key, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_ONSTAGEOBJECTEVENT_INT_INT_PARAM,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_ONSTAGEOBJECTEVENT_INT_INT_PARAM", new Object[] {mapSn, key, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#quitToCommon(HumanObject humanObj, int nowSn, int repSn, Object... params)}*/
	public void quitToCommon(HumanObject humanObj, int nowSn, int repSn, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_QUITTOCOMMON_HUMANOBJECT_INT_INT_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_QUITTOCOMMON_HUMANOBJECT_INT_INT_OBJECTS", new Object[] {humanObj, nowSn, repSn, params});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#sendMsgToMap(Message msg, int mapSn, Vector2D pos)}*/
	public void sendMsgToMap(Message msg, int mapSn, Vector2D pos) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SENDMSGTOMAP_MESSAGE_INT_VECTOR2D,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SENDMSGTOMAP_MESSAGE_INT_VECTOR2D", new Object[] {msg, mapSn, pos});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#setEnd(long stageId)}*/
	public void setEnd(long stageId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SETEND_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SETEND_LONG", new Object[] {stageId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#setMonsterHp(int mapSn, int sn, long hpCur)}*/
	public void setMonsterHp(int mapSn, int sn, long hpCur) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SETMONSTERHP_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SETMONSTERHP_INT_INT_LONG", new Object[] {mapSn, sn, hpCur});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#stageHumanNumAdd(long stageId)}*/
	public void stageHumanNumAdd(long stageId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_STAGEHUMANNUMADD_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_STAGEHUMANNUMADD_LONG", new Object[] {stageId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#stageHumanNumReduce(long stageId)}*/
	public void stageHumanNumReduce(long stageId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_STAGEHUMANNUMREDUCE_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_STAGEHUMANNUMREDUCE_LONG", new Object[] {stageId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageGlobalService#switchToStage(HumanObject humanObj, long stageTargetId, Object... params)}*/
	public void switchToStage(HumanObject humanObj, long stageTargetId, Object... params) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SWITCHTOSTAGE_HUMANOBJECT_LONG_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_SWITCHTOSTAGE_HUMANOBJECT_LONG_OBJECTS", new Object[] {humanObj, stageTargetId, params});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link StageGlobalService#updateTeamIdHumanIdList(long teamId, List humanIdList)}*/
	public void updateTeamIdHumanIdList(long teamId, List humanIdList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_UPDATETEAMIDHUMANIDLIST_LONG_LIST,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEGLOBALSERVICE_UPDATETEAMIDHUMANIDLIST_LONG_LIST", new Object[] {teamId, humanIdList});
		if(immutableOnce) immutableOnce = false;
	}
}
