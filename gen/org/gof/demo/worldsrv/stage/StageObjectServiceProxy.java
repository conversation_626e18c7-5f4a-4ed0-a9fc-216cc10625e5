package org.gof.demo.worldsrv.stage;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.battlesrv.support.Vector2D;
import com.google.protobuf.Message;
import java.util.List;
import java.util.Map;
import java.util.List;
import org.gof.core.support.Param;

@GofGenFile
public final class StageObjectServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_CLEARERROR_LONG = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_END = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_KICKHUMANOBJ = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_LOGIN_LONG_LONG_PARAM = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_NPCSTAGECREATE = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_ONSTAGEOBJECTEVENT_INT_PARAM = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_REGISTER_HUMANOBJECT_BOOLEAN_LONG = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_REMOVEHUMANRESIDUAL_LONG_VECTOR2D_MESSAGE = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_SENDMSG_MESSAGE_VECTOR2D = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_SETMONSTERHP_INT_LONG = 10;
	}

	private static final String SERV_ID = "";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private StageObjectServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings("unchecked")
	public Object getMethodFunction(Service service, int methodKey) {
		StageObjectService serv = (StageObjectService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_CLEARERROR_LONG: {
				return (GofFunction1<Long>)serv::clearError;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_END: {
				return (GofFunction0)serv::end;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_KICKHUMANOBJ: {
				return (GofFunction0)serv::kickHumanObj;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_LOGIN_LONG_LONG_PARAM: {
				return (GofFunction3<Long, Long, Param>)serv::login;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_NPCSTAGECREATE: {
				return (GofFunction0)serv::npcStageCreate;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_ONSTAGEOBJECTEVENT_INT_PARAM: {
				return (GofFunction2<Integer, Param>)serv::onStageObjectEvent;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_REGISTER_HUMANOBJECT_BOOLEAN_LONG: {
				return (GofFunction3<HumanObject, Boolean, Long>)serv::register;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_REMOVEHUMANRESIDUAL_LONG_VECTOR2D_MESSAGE: {
				return (GofFunction3<Long, Vector2D, Message>)serv::removeHumanResidual;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_SENDMSG_MESSAGE_VECTOR2D: {
				return (GofFunction2<Message, Vector2D>)serv::sendMsg;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_SETMONSTERHP_INT_LONG: {
				return (GofFunction2<Integer, Long>)serv::setMonsterHp;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static StageObjectServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static StageObjectServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static StageObjectServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static StageObjectServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static StageObjectServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static StageObjectServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static StageObjectServiceProxy createInstance(String node, String port, Object serviceId) {
		StageObjectServiceProxy inst = new StageObjectServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link StageObjectService#clearError(long curr)}*/
	public void clearError(long curr) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_CLEARERROR_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_CLEARERROR_LONG", new Object[] {curr});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageObjectService#end()}*/
	public void end() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_END,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_END", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageObjectService#kickHumanObj()}*/
	public void kickHumanObj() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_KICKHUMANOBJ,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_KICKHUMANOBJ", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageObjectService#login(long humanId, long stageId, Param param)}*/
	public void login(long humanId, long stageId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_LOGIN_LONG_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_LOGIN_LONG_LONG_PARAM", new Object[] {humanId, stageId, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageObjectService#npcStageCreate()}*/
	public void npcStageCreate() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_NPCSTAGECREATE,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_NPCSTAGECREATE", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageObjectService#onStageObjectEvent(int key, Param param)}*/
	public void onStageObjectEvent(int key, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_ONSTAGEOBJECTEVENT_INT_PARAM,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_ONSTAGEOBJECTEVENT_INT_PARAM", new Object[] {key, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageObjectService#register(HumanObject humanObj, boolean isRevive, long stageTargetId)}*/
	public void register(HumanObject humanObj, boolean isRevive, long stageTargetId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_REGISTER_HUMANOBJECT_BOOLEAN_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_REGISTER_HUMANOBJECT_BOOLEAN_LONG", new Object[] {humanObj, isRevive, stageTargetId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageObjectService#removeHumanResidual(long humanId, Vector2D pos, Message message)}*/
	public void removeHumanResidual(long humanId, Vector2D pos, Message message) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_REMOVEHUMANRESIDUAL_LONG_VECTOR2D_MESSAGE,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_REMOVEHUMANRESIDUAL_LONG_VECTOR2D_MESSAGE", new Object[] {humanId, pos, message});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageObjectService#sendMsg(Message msg, Vector2D pos)}*/
	public void sendMsg(Message msg, Vector2D pos) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_SENDMSG_MESSAGE_VECTOR2D,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_SENDMSG_MESSAGE_VECTOR2D", new Object[] {msg, pos});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link StageObjectService#setMonsterHp(int sn, long hpCur)}*/
	public void setMonsterHp(int sn, long hpCur) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_SETMONSTERHP_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_STAGE_STAGEOBJECTSERVICE_SETMONSTERHP_INT_LONG", new Object[] {sn, hpCur});
		if(immutableOnce) immutableOnce = false;
	}
}
