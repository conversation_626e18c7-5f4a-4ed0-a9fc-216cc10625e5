package org.gof.demo.worldsrv.guild.league;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import java.util.List;
import java.util.Map;
import org.gof.demo.worldsrv.guild.league.match.LeagueMatchGroup;
import org.gof.demo.worldsrv.guild.HumanBriefVO;

@GofGenFile
public final class GuildLeagueServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_ADDGUILDSCORE_MAP = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_AUTOJOINHUMANIDLIST_LONG_LIST = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETCROSSHUMANINFO_LONG_INT = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETGVGRANKLIST_INT_INT_LONG = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETGVGRANKLIST_INT_LONG = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETHUMANBRIEF_LONG = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETMSG_GVG_FIGHT_SETTLEMENT_S2C_LONG_LONG = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETMSG_GVG_SETTLEMENT_SEASON_S2C_LONG_LONG = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETMSG_GVG_SETTLEMENT_WEEK_S2C_LONG_LONG = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETRANKLIST_INT_INT_LONG = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETROLEOTHERSGUILDINFO_LONG_LONG = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETWEEKRANK_INT_INT_LONG_LONG = 12;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GM_INT_STRING = 13;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GM2_STRING = 14;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GMUPDATE = 15;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GUILDBATTLEREGISTER_LIST = 16;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGFIGHTINFO_LONG_INT = 17;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGFIGHTRESULT_LONG_INT = 18;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGINFO_LONG_LONG_INT = 19;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGPLAYVIDEO_LONG_LONG_INT = 20;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGROAD_LONG_INT_INT = 21;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGROADCHANGE_LONG_INT_LIST = 22;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGROADCHANGEALL_LONG_INT_INT_LONG = 23;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGSELECTROAD_LONG_LONG_INT = 24;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVG_HALL_C2S_INT_LONG = 25;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_REMOVEGUILD_LONG = 26;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_REMOVEGUILDENROLL_LONG = 27;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_SEASONINFO_LONG_LONG_INT = 28;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_SENDBRIDGEGUILDRANK_INT_INT_INT_LONG = 29;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UDPATELEAGUEGROUP_INT_INT = 30;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE_STRING = 31;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE1_OBJECTS = 32;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE2_PARAM = 33;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE3_PARAM = 34;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATEGUILDINFO_LONG_PARAM = 35;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATEHUMANBRIEF_HUMANBRIEFVO = 36;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATEHUMANBRIEFLIST_LIST = 37;
	}

	private static final String SERV_ID = "guildLeague";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private GuildLeagueServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		GuildLeagueService serv = (GuildLeagueService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_ADDGUILDSCORE_MAP: {
				return (GofFunction1<Map>)serv::addGuildScore;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_AUTOJOINHUMANIDLIST_LONG_LIST: {
				return (GofFunction2<Long, List>)serv::autoJoinHumanIdList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETCROSSHUMANINFO_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::getCrossHumanInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETGVGRANKLIST_INT_INT_LONG: {
				return (GofFunction3<Integer, Integer, Long>)serv::getGvgRankList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETGVGRANKLIST_INT_LONG: {
				return (GofFunction2<Integer, Long>)serv::getGvgRankList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETHUMANBRIEF_LONG: {
				return (GofFunction1<Long>)serv::getHumanBrief;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETMSG_GVG_FIGHT_SETTLEMENT_S2C_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::getMsg_gvg_fight_settlement_s2c;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETMSG_GVG_SETTLEMENT_SEASON_S2C_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::getMsg_gvg_settlement_season_s2c;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETMSG_GVG_SETTLEMENT_WEEK_S2C_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::getMsg_gvg_settlement_week_s2c;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETRANKLIST_INT_INT_LONG: {
				return (GofFunction3<Integer, Integer, Long>)serv::getRankList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETROLEOTHERSGUILDINFO_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::getRoleOthersGuildInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETWEEKRANK_INT_INT_LONG_LONG: {
				return (GofFunction4<Integer, Integer, Long, Long>)serv::getWeekRank;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GM_INT_STRING: {
				return (GofFunction2<Integer, String>)serv::gm;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GM2_STRING: {
				return (GofFunction1<String>)serv::gm2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GMUPDATE: {
				return (GofFunction0)serv::gmUpdate;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GUILDBATTLEREGISTER_LIST: {
				return (GofFunction1<List>)serv::guildBattleRegister;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGFIGHTINFO_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::gvgFightInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGFIGHTRESULT_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::gvgFightResult;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGINFO_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::gvgInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGPLAYVIDEO_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::gvgPlayVideo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGROAD_LONG_INT_INT: {
				return (GofFunction3<Long, Integer, Integer>)serv::gvgRoad;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGROADCHANGE_LONG_INT_LIST: {
				return (GofFunction3<Long, Integer, List>)serv::gvgRoadChange;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGROADCHANGEALL_LONG_INT_INT_LONG: {
				return (GofFunction4<Long, Integer, Integer, Long>)serv::gvgRoadChangeAll;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGSELECTROAD_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::gvgSelectRoad;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVG_HALL_C2S_INT_LONG: {
				return (GofFunction2<Integer, Long>)serv::gvg_hall_c2s;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_REMOVEGUILD_LONG: {
				return (GofFunction1<Long>)serv::removeGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_REMOVEGUILDENROLL_LONG: {
				return (GofFunction1<Long>)serv::removeGuildEnroll;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_SEASONINFO_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::seasonInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_SENDBRIDGEGUILDRANK_INT_INT_INT_LONG: {
				return (GofFunction4<Integer, Integer, Integer, Long>)serv::sendBridgeGuildRank;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UDPATELEAGUEGROUP_INT_INT: {
				return (GofFunction2<Integer, Integer>)serv::udpateLeagueGroup;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE_STRING: {
				return (GofFunction1<String>)serv::update;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE1_OBJECTS: {
				return (GofFunction1<Object[]>)serv::update1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE2_PARAM: {
				return (GofFunction1<Param>)serv::update2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE3_PARAM: {
				return (GofFunction1<Param>)serv::update3;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATEGUILDINFO_LONG_PARAM: {
				return (GofFunction2<Long, Param>)serv::updateGuildInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATEHUMANBRIEF_HUMANBRIEFVO: {
				return (GofFunction1<HumanBriefVO>)serv::updateHumanBrief;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATEHUMANBRIEFLIST_LIST: {
				return (GofFunction1<List>)serv::updateHumanBriefList;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static GuildLeagueServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static GuildLeagueServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static GuildLeagueServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static GuildLeagueServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static GuildLeagueServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static GuildLeagueServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static GuildLeagueServiceProxy createInstance(String node, String port, Object serviceId) {
		GuildLeagueServiceProxy inst = new GuildLeagueServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildLeagueService#addGuildScore(Map guildIdAddScoreMap)}*/
	public void addGuildScore(Map guildIdAddScoreMap) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_ADDGUILDSCORE_MAP,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_ADDGUILDSCORE_MAP", new Object[] {guildIdAddScoreMap});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildLeagueService#autoJoinHumanIdList(long guildId, List humanIdList)}*/
	public void autoJoinHumanIdList(long guildId, List humanIdList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_AUTOJOINHUMANIDLIST_LONG_LIST,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_AUTOJOINHUMANIDLIST_LONG_LIST", new Object[] {guildId, humanIdList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#getCrossHumanInfo(long humanId, int type)}*/
	public void getCrossHumanInfo(long humanId, int type) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETCROSSHUMANINFO_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETCROSSHUMANINFO_LONG_INT", new Object[] {humanId, type});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#getGvgRankList(int grade, int groupIndex, long guildId)}*/
	public void getGvgRankList(int grade, int groupIndex, long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETGVGRANKLIST_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETGVGRANKLIST_INT_INT_LONG", new Object[] {grade, groupIndex, guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#getGvgRankList(int groupIndex, long guildId)}*/
	public void getGvgRankList(int groupIndex, long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETGVGRANKLIST_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETGVGRANKLIST_INT_LONG", new Object[] {groupIndex, guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#getHumanBrief(long humanId)}*/
	public void getHumanBrief(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETHUMANBRIEF_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETHUMANBRIEF_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#getMsg_gvg_fight_settlement_s2c(long guildId, long humanId)}*/
	public void getMsg_gvg_fight_settlement_s2c(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETMSG_GVG_FIGHT_SETTLEMENT_S2C_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETMSG_GVG_FIGHT_SETTLEMENT_S2C_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#getMsg_gvg_settlement_season_s2c(long guildId, long humanId)}*/
	public void getMsg_gvg_settlement_season_s2c(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETMSG_GVG_SETTLEMENT_SEASON_S2C_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETMSG_GVG_SETTLEMENT_SEASON_S2C_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#getMsg_gvg_settlement_week_s2c(long guildId, long humanId)}*/
	public void getMsg_gvg_settlement_week_s2c(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETMSG_GVG_SETTLEMENT_WEEK_S2C_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETMSG_GVG_SETTLEMENT_WEEK_S2C_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#getRankList(int type, int group, long tempId)}*/
	public void getRankList(int type, int group, long tempId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETRANKLIST_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETRANKLIST_INT_INT_LONG", new Object[] {type, group, tempId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#getRoleOthersGuildInfo(long guildId, long humanId)}*/
	public void getRoleOthersGuildInfo(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETROLEOTHERSGUILDINFO_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETROLEOTHERSGUILDINFO_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#getWeekRank(int type, int serverId, long guildId, long humanId)}*/
	public void getWeekRank(int type, int serverId, long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETWEEKRANK_INT_INT_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GETWEEKRANK_INT_INT_LONG_LONG", new Object[] {type, serverId, guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#gm(int type, String json)}*/
	public void gm(int type, String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GM_INT_STRING,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GM_INT_STRING", new Object[] {type, json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#gm2(String json)}*/
	public void gm2(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GM2_STRING,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GM2_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#gmUpdate()}*/
	public void gmUpdate() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GMUPDATE,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GMUPDATE", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildLeagueService#guildBattleRegister(List dataList)}*/
	public void guildBattleRegister(List dataList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GUILDBATTLEREGISTER_LIST,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GUILDBATTLEREGISTER_LIST", new Object[] {dataList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#gvgFightInfo(long guildId, int road)}*/
	public void gvgFightInfo(long guildId, int road) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGFIGHTINFO_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGFIGHTINFO_LONG_INT", new Object[] {guildId, road});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#gvgFightResult(long guildId, int road)}*/
	public void gvgFightResult(long guildId, int road) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGFIGHTRESULT_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGFIGHTRESULT_LONG_INT", new Object[] {guildId, road});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#gvgInfo(long guildId, long humanId, int serverId)}*/
	public void gvgInfo(long guildId, long humanId, int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGINFO_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGINFO_LONG_LONG_INT", new Object[] {guildId, humanId, serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#gvgPlayVideo(long guildId, long vid, int source)}*/
	public void gvgPlayVideo(long guildId, long vid, int source) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGPLAYVIDEO_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGPLAYVIDEO_LONG_LONG_INT", new Object[] {guildId, vid, source});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#gvgRoad(long guildId, int type, int road)}*/
	public void gvgRoad(long guildId, int type, int road) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGROAD_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGROAD_LONG_INT_INT", new Object[] {guildId, type, road});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildLeagueService#gvgRoadChange(long guildId, int road, List roadInfoList)}*/
	public void gvgRoadChange(long guildId, int road, List roadInfoList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGROADCHANGE_LONG_INT_LIST,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGROADCHANGE_LONG_INT_LIST", new Object[] {guildId, road, roadInfoList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#gvgRoadChangeAll(long guildId, int road, int road1, long humanId)}*/
	public void gvgRoadChangeAll(long guildId, int road, int road1, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGROADCHANGEALL_LONG_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGROADCHANGEALL_LONG_INT_INT_LONG", new Object[] {guildId, road, road1, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#gvgSelectRoad(long guildId, long humanId, int road)}*/
	public void gvgSelectRoad(long guildId, long humanId, int road) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGSELECTROAD_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVGSELECTROAD_LONG_LONG_INT", new Object[] {guildId, humanId, road});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#gvg_hall_c2s(int serverId, long guildId)}*/
	public void gvg_hall_c2s(int serverId, long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVG_HALL_C2S_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_GVG_HALL_C2S_INT_LONG", new Object[] {serverId, guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#removeGuild(long guildId)}*/
	public void removeGuild(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_REMOVEGUILD_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_REMOVEGUILD_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#removeGuildEnroll(long guildId)}*/
	public void removeGuildEnroll(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_REMOVEGUILDENROLL_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_REMOVEGUILDENROLL_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#seasonInfo(long guildId, long humanId, int serverId)}*/
	public void seasonInfo(long guildId, long humanId, int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_SEASONINFO_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_SEASONINFO_LONG_LONG_INT", new Object[] {guildId, humanId, serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#sendBridgeGuildRank(int serverId, int type, int page, long guildId)}*/
	public void sendBridgeGuildRank(int serverId, int type, int page, long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_SENDBRIDGEGUILDRANK_INT_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_SENDBRIDGEGUILDRANK_INT_INT_INT_LONG", new Object[] {serverId, type, page, guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#udpateLeagueGroup(int serverId, int groupIndex)}*/
	public void udpateLeagueGroup(int serverId, int groupIndex) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UDPATELEAGUEGROUP_INT_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UDPATELEAGUEGROUP_INT_INT", new Object[] {serverId, groupIndex});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#update(String json)}*/
	public void update(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE_STRING,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#update1(Object... objs)}*/
	public void update1(Object... objs) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE1_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE1_OBJECTS", new Object[] {objs});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#update2(Param param)}*/
	public void update2(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE2_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE2_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#update3(Param param)}*/
	public void update3(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE3_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATE3_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#updateGuildInfo(long guildId, Param param)}*/
	public void updateGuildInfo(long guildId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATEGUILDINFO_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATEGUILDINFO_LONG_PARAM", new Object[] {guildId, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueService#updateHumanBrief(HumanBriefVO vo)}*/
	public void updateHumanBrief(HumanBriefVO vo) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATEHUMANBRIEF_HUMANBRIEFVO,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATEHUMANBRIEF_HUMANBRIEFVO", new Object[] {vo});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildLeagueService#updateHumanBriefList(List briefList)}*/
	public void updateHumanBriefList(List briefList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATEHUMANBRIEFLIST_LIST,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUESERVICE_UPDATEHUMANBRIEFLIST_LIST", new Object[] {briefList});
		if(immutableOnce) immutableOnce = false;
	}
}
