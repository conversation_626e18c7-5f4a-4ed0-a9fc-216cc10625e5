package org.gof.demo.worldsrv.guild.league;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import java.util.List;
import java.util.Map;
import org.gof.demo.worldsrv.guild.league.match.LeagueMatchGroup;

@GofGenFile
public final class GuildLeagueWarmUpServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_AUTOJOINHUMANIDLIST_LONG_LIST = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETGVGRANKLIST_INT_INT_LONG = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETGVGRANKLIST_INT_LONG = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETMSG_GVG_FIGHT_SETTLEMENT_S2C_LONG_LONG = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETMSG_GVG_SETTLEMENT_SEASON_S2C_LONG_LONG = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETMSG_GVG_SETTLEMENT_WEEK_S2C_LONG_LONG = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETRANKLIST_INT_INT_LONG = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETWEEKRANK_INT_INT_LONG_LONG = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GM_INT_STRING = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GMAFRESHMATCH = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GUILDBATTLEREGISTER_LIST = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGFIGHTINFO_LONG_INT = 12;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGFIGHTRESULT_LONG_INT = 13;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGINFO_LONG_LONG_INT = 14;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGPLAYVIDEO_LONG_LONG_INT = 15;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGROAD_LONG_INT_INT = 16;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGROADCHANGE_LONG_INT_LIST = 17;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGROADCHANGEALL_LONG_INT_INT = 18;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGSELECTROAD_LONG_LONG_INT = 19;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_LOADSERVER_LIST = 20;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_REMOVEGUILD_LONG = 21;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_REMOVEGUILDENROLL_LONG = 22;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_SEASONINFO_LONG_LONG_INT = 23;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE_STRING = 24;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE1_OBJECTS = 25;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE2_PARAM = 26;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE3_PARAM = 27;
	}

	private static final String SERV_ID = "guildLeagueWarmUp";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private GuildLeagueWarmUpServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		GuildLeagueWarmUpService serv = (GuildLeagueWarmUpService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_AUTOJOINHUMANIDLIST_LONG_LIST: {
				return (GofFunction2<Long, List>)serv::autoJoinHumanIdList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETGVGRANKLIST_INT_INT_LONG: {
				return (GofFunction3<Integer, Integer, Long>)serv::getGvgRankList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETGVGRANKLIST_INT_LONG: {
				return (GofFunction2<Integer, Long>)serv::getGvgRankList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETMSG_GVG_FIGHT_SETTLEMENT_S2C_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::getMsg_gvg_fight_settlement_s2c;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETMSG_GVG_SETTLEMENT_SEASON_S2C_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::getMsg_gvg_settlement_season_s2c;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETMSG_GVG_SETTLEMENT_WEEK_S2C_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::getMsg_gvg_settlement_week_s2c;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETRANKLIST_INT_INT_LONG: {
				return (GofFunction3<Integer, Integer, Long>)serv::getRankList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETWEEKRANK_INT_INT_LONG_LONG: {
				return (GofFunction4<Integer, Integer, Long, Long>)serv::getWeekRank;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GM_INT_STRING: {
				return (GofFunction2<Integer, String>)serv::gm;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GMAFRESHMATCH: {
				return (GofFunction0)serv::gmAfreshMatch;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GUILDBATTLEREGISTER_LIST: {
				return (GofFunction1<List>)serv::guildBattleRegister;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGFIGHTINFO_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::gvgFightInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGFIGHTRESULT_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::gvgFightResult;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGINFO_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::gvgInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGPLAYVIDEO_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::gvgPlayVideo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGROAD_LONG_INT_INT: {
				return (GofFunction3<Long, Integer, Integer>)serv::gvgRoad;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGROADCHANGE_LONG_INT_LIST: {
				return (GofFunction3<Long, Integer, List>)serv::gvgRoadChange;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGROADCHANGEALL_LONG_INT_INT: {
				return (GofFunction3<Long, Integer, Integer>)serv::gvgRoadChangeAll;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGSELECTROAD_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::gvgSelectRoad;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_LOADSERVER_LIST: {
				return (GofFunction1<List>)serv::loadServer;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_REMOVEGUILD_LONG: {
				return (GofFunction1<Long>)serv::removeGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_REMOVEGUILDENROLL_LONG: {
				return (GofFunction1<Long>)serv::removeGuildEnroll;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_SEASONINFO_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::seasonInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE_STRING: {
				return (GofFunction1<String>)serv::update;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE1_OBJECTS: {
				return (GofFunction1<Object[]>)serv::update1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE2_PARAM: {
				return (GofFunction1<Param>)serv::update2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE3_PARAM: {
				return (GofFunction1<Param>)serv::update3;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static GuildLeagueWarmUpServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static GuildLeagueWarmUpServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static GuildLeagueWarmUpServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static GuildLeagueWarmUpServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static GuildLeagueWarmUpServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static GuildLeagueWarmUpServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static GuildLeagueWarmUpServiceProxy createInstance(String node, String port, Object serviceId) {
		GuildLeagueWarmUpServiceProxy inst = new GuildLeagueWarmUpServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildLeagueWarmUpService#autoJoinHumanIdList(long guildId, List humanIdList)}*/
	public void autoJoinHumanIdList(long guildId, List humanIdList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_AUTOJOINHUMANIDLIST_LONG_LIST,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_AUTOJOINHUMANIDLIST_LONG_LIST", new Object[] {guildId, humanIdList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#getGvgRankList(int grade, int groupIndex, long guildId)}*/
	public void getGvgRankList(int grade, int groupIndex, long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETGVGRANKLIST_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETGVGRANKLIST_INT_INT_LONG", new Object[] {grade, groupIndex, guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#getGvgRankList(int groupIndex, long guildId)}*/
	public void getGvgRankList(int groupIndex, long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETGVGRANKLIST_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETGVGRANKLIST_INT_LONG", new Object[] {groupIndex, guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#getMsg_gvg_fight_settlement_s2c(long guildId, long humanId)}*/
	public void getMsg_gvg_fight_settlement_s2c(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETMSG_GVG_FIGHT_SETTLEMENT_S2C_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETMSG_GVG_FIGHT_SETTLEMENT_S2C_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#getMsg_gvg_settlement_season_s2c(long guildId, long humanId)}*/
	public void getMsg_gvg_settlement_season_s2c(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETMSG_GVG_SETTLEMENT_SEASON_S2C_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETMSG_GVG_SETTLEMENT_SEASON_S2C_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#getMsg_gvg_settlement_week_s2c(long guildId, long humanId)}*/
	public void getMsg_gvg_settlement_week_s2c(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETMSG_GVG_SETTLEMENT_WEEK_S2C_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETMSG_GVG_SETTLEMENT_WEEK_S2C_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#getRankList(int type, int group, long tempId)}*/
	public void getRankList(int type, int group, long tempId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETRANKLIST_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETRANKLIST_INT_INT_LONG", new Object[] {type, group, tempId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#getWeekRank(int type, int serverId, long guildId, long humanId)}*/
	public void getWeekRank(int type, int serverId, long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETWEEKRANK_INT_INT_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GETWEEKRANK_INT_INT_LONG_LONG", new Object[] {type, serverId, guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#gm(int type, String json)}*/
	public void gm(int type, String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GM_INT_STRING,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GM_INT_STRING", new Object[] {type, json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#gmAfreshMatch()}*/
	public void gmAfreshMatch() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GMAFRESHMATCH,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GMAFRESHMATCH", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildLeagueWarmUpService#guildBattleRegister(List groupList)}*/
	public void guildBattleRegister(List groupList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GUILDBATTLEREGISTER_LIST,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GUILDBATTLEREGISTER_LIST", new Object[] {groupList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#gvgFightInfo(long guildId, int road)}*/
	public void gvgFightInfo(long guildId, int road) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGFIGHTINFO_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGFIGHTINFO_LONG_INT", new Object[] {guildId, road});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#gvgFightResult(long guildId, int road)}*/
	public void gvgFightResult(long guildId, int road) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGFIGHTRESULT_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGFIGHTRESULT_LONG_INT", new Object[] {guildId, road});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#gvgInfo(long guildId, long humanId, int serverId)}*/
	public void gvgInfo(long guildId, long humanId, int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGINFO_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGINFO_LONG_LONG_INT", new Object[] {guildId, humanId, serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#gvgPlayVideo(long guildId, long vid, int source)}*/
	public void gvgPlayVideo(long guildId, long vid, int source) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGPLAYVIDEO_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGPLAYVIDEO_LONG_LONG_INT", new Object[] {guildId, vid, source});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#gvgRoad(long guildId, int type, int road)}*/
	public void gvgRoad(long guildId, int type, int road) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGROAD_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGROAD_LONG_INT_INT", new Object[] {guildId, type, road});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildLeagueWarmUpService#gvgRoadChange(long guildId, int road, List roadInfoList)}*/
	public void gvgRoadChange(long guildId, int road, List roadInfoList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGROADCHANGE_LONG_INT_LIST,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGROADCHANGE_LONG_INT_LIST", new Object[] {guildId, road, roadInfoList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#gvgRoadChangeAll(long guildId, int road, int road1)}*/
	public void gvgRoadChangeAll(long guildId, int road, int road1) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGROADCHANGEALL_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGROADCHANGEALL_LONG_INT_INT", new Object[] {guildId, road, road1});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#gvgSelectRoad(long guildId, long humanId, int road)}*/
	public void gvgSelectRoad(long guildId, long humanId, int road) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGSELECTROAD_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_GVGSELECTROAD_LONG_LONG_INT", new Object[] {guildId, humanId, road});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildLeagueWarmUpService#loadServer(List serverIdList)}*/
	public void loadServer(List serverIdList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_LOADSERVER_LIST,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_LOADSERVER_LIST", new Object[] {serverIdList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#removeGuild(long guildId)}*/
	public void removeGuild(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_REMOVEGUILD_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_REMOVEGUILD_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#removeGuildEnroll(long guildId)}*/
	public void removeGuildEnroll(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_REMOVEGUILDENROLL_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_REMOVEGUILDENROLL_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#seasonInfo(long guildId, long humanId, int serverId)}*/
	public void seasonInfo(long guildId, long humanId, int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_SEASONINFO_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_SEASONINFO_LONG_LONG_INT", new Object[] {guildId, humanId, serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#update(String json)}*/
	public void update(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE_STRING,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#update1(Object... objs)}*/
	public void update1(Object... objs) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE1_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE1_OBJECTS", new Object[] {objs});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#update2(Param param)}*/
	public void update2(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE2_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE2_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildLeagueWarmUpService#update3(Param param)}*/
	public void update3(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE3_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_LEAGUE_GUILDLEAGUEWARMUPSERVICE_UPDATE3_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}
}
