package org.gof.demo.worldsrv.guild;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import org.gof.core.support.Param;
import java.util.List;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.rank.RankInfo;
import org.gof.demo.worldsrv.guild.league.LeagueVO;
import java.util.Map;

@GofGenFile
public final class GuildServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_APPLYGUILDIDLIST_LONG = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_AREAENTER_LONG_LONG_PARAM = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_AREAEXIT_LONG_LONG_PARAM = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_BOXOPEN_LONG_LONG_INT_INT = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CACHEGVESIGNHUMANBRIEF_LONG_LONG_BYTES = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILD_LONG_STRING_INT = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILDAPPLY_PARAM = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILDHELP_PARAM = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILDMEMBER_PARAM = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DELUPGRADEFINISHHELP_PARAM = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DUNGEONBATTLE_LONG_INT_LONG_PARAM = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DUNGEON_LEAGUE_SOLO_INFO_S2C_LONG_BOOLEAN = 12;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DUNGEON_LEAGUE_SOLO_UPDATE_BOX_S2C_LONG_LONG_INT_INT = 13;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_ENTERGUILDAREA_LONG = 14;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETCROSSHUMANINFO_LONG_INT = 15;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETELEM_LONG_LONG = 16;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILD_LONG = 17;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDAPPLYLIST_LONG_LONG = 18;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDHUMANNUM_LONG = 19;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDID_LONG = 20;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDIDLIST_INT = 21;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDINFO_INT_INT = 22;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDINFO_LONG_LONG = 23;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDRANKINFO_LIST_LONG_LONG_INT = 24;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETRANKLIST_INT_INT_LONG_INT = 25;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETRANKLIST_INT_INT_LONG = 26;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETROLEOTHERSGUILDINFO_LONG = 27;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETTOPCOMBATGUILDFLAGLIST_INT = 28;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GM_INT_STRING = 29;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GMAUTOGUILDLEAGUE = 30;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GMGVEAUTOSIGN_INT = 31;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDANSWERQUESTION_LONG_LONG_STRING = 32;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDAPPROVE_LONG_LONG_LONG_INT = 33;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDBOSS_LONG_LONG_LONG_INT_INT = 34;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDBOSSGETREWARD_LONG_LONG_INT_INT = 35;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDBOSSGVEINFO_LONG_LONG = 36;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDCHANGEPOS_LONG_LONG_LONG_INT = 37;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDICEINFO_LONG_LONG = 38;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDISSOLVE_LONG_LONG = 39;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDONATE_LONG_LONG_INT = 40;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDONATEINFO_LONG_LONG = 41;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDGVEENTER_LONG_LONG_BYTES = 42;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDGVEEXIT_LONG_LONG = 43;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDHELP_LONG_LONG_LONG_INT = 44;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDHELPLIST_LONG_INT = 45;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDHELPSTATUS_LONG_LONG = 46;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDJOIN_LONG_PARAM = 47;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDKICK_LONG_LONG_LONG = 48;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDLEAVE_LONG_LONG = 49;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDLOG_LONG_LONG = 50;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDMEMBER_LONG = 51;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDQUESTIONINFO_LONG_LONG = 52;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDQUICKJOIN_PARAM = 53;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDRANK_LONG_INT_LONG_INT = 54;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDRANKMYINFO_LONG_LONG = 55;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDROLLDICE_LONG_LONG = 56;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDSCHEDULE_LONG = 57;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDSETTING_PARAM = 58;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILD_AREA_MOVE_LONG_LONG_PARAM = 59;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GVEEND = 60;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GVESIGN_LONG_LONG = 61;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_HALLGUILD_LONG_INT = 62;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_HUMANLOGIN_LONG_LONG = 63;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_HUMANLOGOUT_LONG_LONG_BYTES = 64;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_LOADSERVER_LIST = 65;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_NAMECHANGE_LONG_LONG_STRING = 66;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_REMOVEGUILD_LONG = 67;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_ROLLBACKCREATEGUILD_LONG = 68;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SEARCHGUILD_INT_INT_STRING_INT = 69;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMAILTOGUILD_LONG_BOOLEAN_LONG_INT_STRING_STRING_STRING_PARAM_OBJECTS = 70;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMAILTOGUILDLEADER_LONG_LONG_INT_STRING_STRING_STRING_PARAM_OBJECTS = 71;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMSG_GUILD_SCHEDULE_S2C_LIST_INT = 72;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMSG_GUILD_SCHEDULE_S2CWARM_LIST_INT = 73;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDSEASONRESETMAILSN_LIST = 74;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDSETTLEGUILDREWARD_MAP = 75;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_TREASUREBOX_LONG = 76;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE_STRING = 77;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE1_OBJECTS = 78;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE2_PARAM = 79;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE3_STRING = 80;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATEGUILDMEMBERHEAD_LONG_LONG_STRING = 81;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATEGUILDMEMBERNAME_LONG_LONG_STRING = 82;
		public static final int ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATEMEMBERCOMBAT_LONG_LONG_PARAM = 83;
	}

	private static final String SERV_ID = "guild";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private GuildServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		GuildService serv = (GuildService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_APPLYGUILDIDLIST_LONG: {
				return (GofFunction1<Long>)serv::applyGuildIdList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_AREAENTER_LONG_LONG_PARAM: {
				return (GofFunction3<Long, Long, Param>)serv::areaEnter;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_AREAEXIT_LONG_LONG_PARAM: {
				return (GofFunction3<Long, Long, Param>)serv::areaExit;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_BOXOPEN_LONG_LONG_INT_INT: {
				return (GofFunction4<Long, Long, Integer, Integer>)serv::boxOpen;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CACHEGVESIGNHUMANBRIEF_LONG_LONG_BYTES: {
				return (GofFunction3<Long, Long, byte[]>)serv::cacheGveSignHumanBrief;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILD_LONG_STRING_INT: {
				return (GofFunction3<Long, String, Integer>)serv::createGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILDAPPLY_PARAM: {
				return (GofFunction1<Param>)serv::createGuildApply;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILDHELP_PARAM: {
				return (GofFunction1<Param>)serv::createGuildHelp;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILDMEMBER_PARAM: {
				return (GofFunction1<Param>)serv::createGuildMember;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DELUPGRADEFINISHHELP_PARAM: {
				return (GofFunction1<Param>)serv::delUpgradeFinishHelp;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DUNGEONBATTLE_LONG_INT_LONG_PARAM: {
				return (GofFunction4<Long, Integer, Long, Param>)serv::dungeonBattle;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DUNGEON_LEAGUE_SOLO_INFO_S2C_LONG_BOOLEAN: {
				return (GofFunction2<Long, Boolean>)serv::dungeon_league_solo_info_s2c;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DUNGEON_LEAGUE_SOLO_UPDATE_BOX_S2C_LONG_LONG_INT_INT: {
				return (GofFunction4<Long, Long, Integer, Integer>)serv::dungeon_league_solo_update_box_s2c;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_ENTERGUILDAREA_LONG: {
				return (GofFunction1<Long>)serv::enterGuildArea;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETCROSSHUMANINFO_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::getCrossHumanInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETELEM_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::getElem;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILD_LONG: {
				return (GofFunction1<Long>)serv::getGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDAPPLYLIST_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::getGuildApplyList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDHUMANNUM_LONG: {
				return (GofFunction1<Long>)serv::getGuildHumanNum;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDID_LONG: {
				return (GofFunction1<Long>)serv::getGuildId;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDIDLIST_INT: {
				return (GofFunction1<Integer>)serv::getGuildIdList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDINFO_INT_INT: {
				return (GofFunction2<Integer, Integer>)serv::getGuildInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDINFO_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::getGuildInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDRANKINFO_LIST_LONG_LONG_INT: {
				return (GofFunction4<List, Long, Long, Integer>)serv::getGuildRankInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETRANKLIST_INT_INT_LONG_INT: {
				return (GofFunction4<Integer, Integer, Long, Integer>)serv::getRankList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETRANKLIST_INT_INT_LONG: {
				return (GofFunction3<Integer, Integer, Long>)serv::getRankList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETROLEOTHERSGUILDINFO_LONG: {
				return (GofFunction1<Long>)serv::getRoleOthersGuildInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETTOPCOMBATGUILDFLAGLIST_INT: {
				return (GofFunction1<Integer>)serv::getTopCombatGuildFlagList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GM_INT_STRING: {
				return (GofFunction2<Integer, String>)serv::gm;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GMAUTOGUILDLEAGUE: {
				return (GofFunction0)serv::gmAutoGuildLeague;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GMGVEAUTOSIGN_INT: {
				return (GofFunction1<Integer>)serv::gmGveAutoSign;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDANSWERQUESTION_LONG_LONG_STRING: {
				return (GofFunction3<Long, Long, String>)serv::guildAnswerQuestion;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDAPPROVE_LONG_LONG_LONG_INT: {
				return (GofFunction4<Long, Long, Long, Integer>)serv::guildApprove;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDBOSS_LONG_LONG_LONG_INT_INT: {
				return (GofFunction5<Long, Long, Long, Integer, Integer>)serv::guildBoss;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDBOSSGETREWARD_LONG_LONG_INT_INT: {
				return (GofFunction4<Long, Long, Integer, Integer>)serv::guildBossGetReward;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDBOSSGVEINFO_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::guildBossGveInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDCHANGEPOS_LONG_LONG_LONG_INT: {
				return (GofFunction4<Long, Long, Long, Integer>)serv::guildChangePos;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDICEINFO_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::guildDiceInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDISSOLVE_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::guildDissolve;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDONATE_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::guildDonate;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDONATEINFO_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::guildDonateInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDGVEENTER_LONG_LONG_BYTES: {
				return (GofFunction3<Long, Long, byte[]>)serv::guildGveEnter;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDGVEEXIT_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::guildGveExit;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDHELP_LONG_LONG_LONG_INT: {
				return (GofFunction4<Long, Long, Long, Integer>)serv::guildHelp;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDHELPLIST_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::guildHelpList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDHELPSTATUS_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::guildHelpStatus;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDJOIN_LONG_PARAM: {
				return (GofFunction2<Long, Param>)serv::guildJoin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDKICK_LONG_LONG_LONG: {
				return (GofFunction3<Long, Long, Long>)serv::guildKick;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDLEAVE_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::guildLeave;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDLOG_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::guildLog;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDMEMBER_LONG: {
				return (GofFunction1<Long>)serv::guildMember;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDQUESTIONINFO_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::guildQuestionInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDQUICKJOIN_PARAM: {
				return (GofFunction1<Param>)serv::guildQuickJoin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDRANK_LONG_INT_LONG_INT: {
				return (GofFunction4<Long, Integer, Long, Integer>)serv::guildRank;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDRANKMYINFO_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::guildRankMyInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDROLLDICE_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::guildRollDice;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDSCHEDULE_LONG: {
				return (GofFunction1<Long>)serv::guildSchedule;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDSETTING_PARAM: {
				return (GofFunction1<Param>)serv::guildSetting;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILD_AREA_MOVE_LONG_LONG_PARAM: {
				return (GofFunction3<Long, Long, Param>)serv::guild_area_move;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GVEEND: {
				return (GofFunction0)serv::gveEnd;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GVESIGN_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::gveSign;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_HALLGUILD_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::hallGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_HUMANLOGIN_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::humanLogin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_HUMANLOGOUT_LONG_LONG_BYTES: {
				return (GofFunction3<Long, Long, byte[]>)serv::humanLogout;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_LOADSERVER_LIST: {
				return (GofFunction1<List>)serv::loadServer;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_NAMECHANGE_LONG_LONG_STRING: {
				return (GofFunction3<Long, Long, String>)serv::nameChange;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_REMOVEGUILD_LONG: {
				return (GofFunction1<Long>)serv::removeGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_ROLLBACKCREATEGUILD_LONG: {
				return (GofFunction1<Long>)serv::rollBackCreateGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SEARCHGUILD_INT_INT_STRING_INT: {
				return (GofFunction4<Integer, Integer, String, Integer>)serv::searchGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMAILTOGUILD_LONG_BOOLEAN_LONG_INT_STRING_STRING_STRING_PARAM_OBJECTS: {
				return (GofFunction9<Long, Boolean, Long, Integer, String, String, String, Param, Object[]>)serv::sendMailToGuild;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMAILTOGUILDLEADER_LONG_LONG_INT_STRING_STRING_STRING_PARAM_OBJECTS: {
				return (GofFunction8<Long, Long, Integer, String, String, String, Param, Object[]>)serv::sendMailToGuildLeader;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMSG_GUILD_SCHEDULE_S2C_LIST_INT: {
				return (GofFunction2<List, Integer>)serv::sendMsg_guild_schedule_s2c;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMSG_GUILD_SCHEDULE_S2CWARM_LIST_INT: {
				return (GofFunction2<List, Integer>)serv::sendMsg_guild_schedule_s2cWarm;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDSEASONRESETMAILSN_LIST: {
				return (GofFunction1<List>)serv::sendSeasonResetMailSn;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDSETTLEGUILDREWARD_MAP: {
				return (GofFunction1<Map>)serv::sendSettleGuildReward;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_TREASUREBOX_LONG: {
				return (GofFunction1<Long>)serv::treasureBox;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE_STRING: {
				return (GofFunction1<String>)serv::update;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE1_OBJECTS: {
				return (GofFunction1<Object[]>)serv::update1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE2_PARAM: {
				return (GofFunction1<Param>)serv::update2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE3_STRING: {
				return (GofFunction1<String>)serv::update3;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATEGUILDMEMBERHEAD_LONG_LONG_STRING: {
				return (GofFunction3<Long, Long, String>)serv::updateGuildMemberHead;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATEGUILDMEMBERNAME_LONG_LONG_STRING: {
				return (GofFunction3<Long, Long, String>)serv::updateGuildMemberName;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATEMEMBERCOMBAT_LONG_LONG_PARAM: {
				return (GofFunction3<Long, Long, Param>)serv::updateMemberCombat;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static GuildServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static GuildServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static GuildServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static GuildServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static GuildServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static GuildServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static GuildServiceProxy createInstance(String node, String port, Object serviceId) {
		GuildServiceProxy inst = new GuildServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link GuildService#applyGuildIdList(long humanId)}*/
	public void applyGuildIdList(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_APPLYGUILDIDLIST_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_APPLYGUILDIDLIST_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#areaEnter(long humanId, long guildId, Param param)}*/
	public void areaEnter(long humanId, long guildId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_AREAENTER_LONG_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_AREAENTER_LONG_LONG_PARAM", new Object[] {humanId, guildId, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#areaExit(long humanId, long guildId, Param param)}*/
	public void areaExit(long humanId, long guildId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_AREAEXIT_LONG_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_AREAEXIT_LONG_LONG_PARAM", new Object[] {humanId, guildId, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#boxOpen(long guildId, long humanId, int boxId, int dailyNum)}*/
	public void boxOpen(long guildId, long humanId, int boxId, int dailyNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_BOXOPEN_LONG_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_BOXOPEN_LONG_LONG_INT_INT", new Object[] {guildId, humanId, boxId, dailyNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#cacheGveSignHumanBrief(long guildId, long humanId, byte... battleRole)}*/
	public void cacheGveSignHumanBrief(long guildId, long humanId, byte... battleRole) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CACHEGVESIGNHUMANBRIEF_LONG_LONG_BYTES,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CACHEGVESIGNHUMANBRIEF_LONG_LONG_BYTES", new Object[] {guildId, humanId, battleRole});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#createGuild(long humanId, String name, int serverId)}*/
	public void createGuild(long humanId, String name, int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILD_LONG_STRING_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILD_LONG_STRING_INT", new Object[] {humanId, name, serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#createGuildApply(Param param)}*/
	public void createGuildApply(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILDAPPLY_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILDAPPLY_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#createGuildHelp(Param param)}*/
	public void createGuildHelp(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILDHELP_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILDHELP_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#createGuildMember(Param param)}*/
	public void createGuildMember(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILDMEMBER_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_CREATEGUILDMEMBER_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#delUpgradeFinishHelp(Param param)}*/
	public void delUpgradeFinishHelp(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DELUPGRADEFINISHHELP_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DELUPGRADEFINISHHELP_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#dungeonBattle(long guildId, int type, long humanId, Param param)}*/
	public void dungeonBattle(long guildId, int type, long humanId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DUNGEONBATTLE_LONG_INT_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DUNGEONBATTLE_LONG_INT_LONG_PARAM", new Object[] {guildId, type, humanId, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#dungeon_league_solo_info_s2c(long guildId, boolean isAll)}*/
	public void dungeon_league_solo_info_s2c(long guildId, boolean isAll) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DUNGEON_LEAGUE_SOLO_INFO_S2C_LONG_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DUNGEON_LEAGUE_SOLO_INFO_S2C_LONG_BOOLEAN", new Object[] {guildId, isAll});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#dungeon_league_solo_update_box_s2c(long guildId, long humanId, int boxGotNum, int highBoxGotNum)}*/
	public void dungeon_league_solo_update_box_s2c(long guildId, long humanId, int boxGotNum, int highBoxGotNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DUNGEON_LEAGUE_SOLO_UPDATE_BOX_S2C_LONG_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_DUNGEON_LEAGUE_SOLO_UPDATE_BOX_S2C_LONG_LONG_INT_INT", new Object[] {guildId, humanId, boxGotNum, highBoxGotNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#enterGuildArea(long guildId)}*/
	public void enterGuildArea(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_ENTERGUILDAREA_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_ENTERGUILDAREA_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#getCrossHumanInfo(long humanId, int type)}*/
	public void getCrossHumanInfo(long humanId, int type) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETCROSSHUMANINFO_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETCROSSHUMANINFO_LONG_INT", new Object[] {humanId, type});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#getElem(long guildId, long humanId)}*/
	public void getElem(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETELEM_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETELEM_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#getGuild(long guildId)}*/
	public void getGuild(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILD_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILD_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#getGuildApplyList(long guildId, long humanId)}*/
	public void getGuildApplyList(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDAPPLYLIST_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDAPPLYLIST_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#getGuildHumanNum(long guildId)}*/
	public void getGuildHumanNum(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDHUMANNUM_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDHUMANNUM_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#getGuildId(long humanId)}*/
	public void getGuildId(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDID_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDID_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#getGuildIdList(int serverId)}*/
	public void getGuildIdList(int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDIDLIST_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDIDLIST_INT", new Object[] {serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#getGuildInfo(int serverId, int num)}*/
	public void getGuildInfo(int serverId, int num) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDINFO_INT_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDINFO_INT_INT", new Object[] {serverId, num});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#getGuildInfo(long guildId, long humanId)}*/
	public void getGuildInfo(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDINFO_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDINFO_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildService#getGuildRankInfo(List rankInfoList, long guildId, long humanId, int type)}*/
	public void getGuildRankInfo(List rankInfoList, long guildId, long humanId, int type) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDRANKINFO_LIST_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETGUILDRANKINFO_LIST_LONG_LONG_INT", new Object[] {rankInfoList, guildId, humanId, type});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#getRankList(int serverId, int page, long myGuildId, int rankSn)}*/
	public void getRankList(int serverId, int page, long myGuildId, int rankSn) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETRANKLIST_INT_INT_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETRANKLIST_INT_INT_LONG_INT", new Object[] {serverId, page, myGuildId, rankSn});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#getRankList(int serverId, int page, long myGuildId)}*/
	public void getRankList(int serverId, int page, long myGuildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETRANKLIST_INT_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETRANKLIST_INT_INT_LONG", new Object[] {serverId, page, myGuildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#getRoleOthersGuildInfo(long guildId)}*/
	public void getRoleOthersGuildInfo(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETROLEOTHERSGUILDINFO_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETROLEOTHERSGUILDINFO_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#getTopCombatGuildFlagList(int serverId)}*/
	public void getTopCombatGuildFlagList(int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETTOPCOMBATGUILDFLAGLIST_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GETTOPCOMBATGUILDFLAGLIST_INT", new Object[] {serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#gm(int type, String json)}*/
	public void gm(int type, String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GM_INT_STRING,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GM_INT_STRING", new Object[] {type, json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#gmAutoGuildLeague()}*/
	public void gmAutoGuildLeague() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GMAUTOGUILDLEAGUE,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GMAUTOGUILDLEAGUE", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#gmGveAutoSign(int signNum)}*/
	public void gmGveAutoSign(int signNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GMGVEAUTOSIGN_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GMGVEAUTOSIGN_INT", new Object[] {signNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildAnswerQuestion(long guildId, long humanId, String answer)}*/
	public void guildAnswerQuestion(long guildId, long humanId, String answer) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDANSWERQUESTION_LONG_LONG_STRING,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDANSWERQUESTION_LONG_LONG_STRING", new Object[] {guildId, humanId, answer});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildApprove(long guildId, long humanId, long applyUid, int type)}*/
	public void guildApprove(long guildId, long humanId, long applyUid, int type) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDAPPROVE_LONG_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDAPPROVE_LONG_LONG_LONG_INT", new Object[] {guildId, humanId, applyUid, type});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildBoss(long humanId, long guildId, long hurt, int boxNum, int highBoxNum)}*/
	public void guildBoss(long humanId, long guildId, long hurt, int boxNum, int highBoxNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDBOSS_LONG_LONG_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDBOSS_LONG_LONG_LONG_INT_INT", new Object[] {humanId, guildId, hurt, boxNum, highBoxNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildBossGetReward(long humanId, long guildId, int type, int yetBoxNum)}*/
	public void guildBossGetReward(long humanId, long guildId, int type, int yetBoxNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDBOSSGETREWARD_LONG_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDBOSSGETREWARD_LONG_LONG_INT_INT", new Object[] {humanId, guildId, type, yetBoxNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildBossGveInfo(long guildId, long humanId)}*/
	public void guildBossGveInfo(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDBOSSGVEINFO_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDBOSSGVEINFO_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildChangePos(long guildId, long humanId, long targetId, int pos)}*/
	public void guildChangePos(long guildId, long humanId, long targetId, int pos) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDCHANGEPOS_LONG_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDCHANGEPOS_LONG_LONG_LONG_INT", new Object[] {guildId, humanId, targetId, pos});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildDiceInfo(long guildId, long humanId)}*/
	public void guildDiceInfo(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDICEINFO_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDICEINFO_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildDissolve(long guildId, long humanId)}*/
	public void guildDissolve(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDISSOLVE_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDISSOLVE_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildDonate(long guildId, long humanId, int addExp)}*/
	public void guildDonate(long guildId, long humanId, int addExp) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDONATE_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDONATE_LONG_LONG_INT", new Object[] {guildId, humanId, addExp});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildDonateInfo(long guildId, long humanId)}*/
	public void guildDonateInfo(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDONATEINFO_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDDONATEINFO_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildGveEnter(long guildId, long humanId, byte... brief)}*/
	public void guildGveEnter(long guildId, long humanId, byte... brief) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDGVEENTER_LONG_LONG_BYTES,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDGVEENTER_LONG_LONG_BYTES", new Object[] {guildId, humanId, brief});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildGveExit(long guildId, long humanId)}*/
	public void guildGveExit(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDGVEEXIT_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDGVEEXIT_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildHelp(long humanId, long helpId, long guildId, int dailyNum)}*/
	public void guildHelp(long humanId, long helpId, long guildId, int dailyNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDHELP_LONG_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDHELP_LONG_LONG_LONG_INT", new Object[] {humanId, helpId, guildId, dailyNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildHelpList(long guildId, int type)}*/
	public void guildHelpList(long guildId, int type) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDHELPLIST_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDHELPLIST_LONG_INT", new Object[] {guildId, type});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildHelpStatus(long guildId, long humanId)}*/
	public void guildHelpStatus(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDHELPSTATUS_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDHELPSTATUS_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildJoin(long guildId, Param param)}*/
	public void guildJoin(long guildId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDJOIN_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDJOIN_LONG_PARAM", new Object[] {guildId, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildKick(long guildId, long humanId, long kickId)}*/
	public void guildKick(long guildId, long humanId, long kickId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDKICK_LONG_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDKICK_LONG_LONG_LONG", new Object[] {guildId, humanId, kickId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildLeave(long guildId, long humanId)}*/
	public void guildLeave(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDLEAVE_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDLEAVE_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildLog(long guildId, long guildLogId)}*/
	public void guildLog(long guildId, long guildLogId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDLOG_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDLOG_LONG_LONG", new Object[] {guildId, guildLogId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildMember(long guildId)}*/
	public void guildMember(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDMEMBER_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDMEMBER_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildQuestionInfo(long guildId, long humanId)}*/
	public void guildQuestionInfo(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDQUESTIONINFO_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDQUESTIONINFO_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildQuickJoin(Param param)}*/
	public void guildQuickJoin(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDQUICKJOIN_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDQUICKJOIN_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildRank(long guildId, int rankType, long humanId, int page)}*/
	public void guildRank(long guildId, int rankType, long humanId, int page) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDRANK_LONG_INT_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDRANK_LONG_INT_LONG_INT", new Object[] {guildId, rankType, humanId, page});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildRankMyInfo(long guildId, long humanId)}*/
	public void guildRankMyInfo(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDRANKMYINFO_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDRANKMYINFO_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildRollDice(long guildId, long humanId)}*/
	public void guildRollDice(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDROLLDICE_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDROLLDICE_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildSchedule(long guildId)}*/
	public void guildSchedule(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDSCHEDULE_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDSCHEDULE_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guildSetting(Param param)}*/
	public void guildSetting(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDSETTING_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILDSETTING_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#guild_area_move(long guildId, long humanId, Param param)}*/
	public void guild_area_move(long guildId, long humanId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILD_AREA_MOVE_LONG_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GUILD_AREA_MOVE_LONG_LONG_PARAM", new Object[] {guildId, humanId, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#gveEnd()}*/
	public void gveEnd() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GVEEND,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GVEEND", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#gveSign(long guildId, long humanId)}*/
	public void gveSign(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GVESIGN_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_GVESIGN_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#hallGuild(long guildId, int score)}*/
	public void hallGuild(long guildId, int score) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_HALLGUILD_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_HALLGUILD_LONG_INT", new Object[] {guildId, score});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#humanLogin(long guildId, long humanId)}*/
	public void humanLogin(long guildId, long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_HUMANLOGIN_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_HUMANLOGIN_LONG_LONG", new Object[] {guildId, humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#humanLogout(long guildId, long humanId, byte... brief)}*/
	public void humanLogout(long guildId, long humanId, byte... brief) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_HUMANLOGOUT_LONG_LONG_BYTES,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_HUMANLOGOUT_LONG_LONG_BYTES", new Object[] {guildId, humanId, brief});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildService#loadServer(List serverIdList)}*/
	public void loadServer(List serverIdList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_LOADSERVER_LIST,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_LOADSERVER_LIST", new Object[] {serverIdList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#nameChange(long guildId, long humanId, String name)}*/
	public void nameChange(long guildId, long humanId, String name) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_NAMECHANGE_LONG_LONG_STRING,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_NAMECHANGE_LONG_LONG_STRING", new Object[] {guildId, humanId, name});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#removeGuild(long guildId)}*/
	public void removeGuild(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_REMOVEGUILD_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_REMOVEGUILD_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#rollBackCreateGuild(long guildId)}*/
	public void rollBackCreateGuild(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_ROLLBACKCREATEGUILD_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_ROLLBACKCREATEGUILD_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#searchGuild(int serverId, int type, String searchKey, int page)}*/
	public void searchGuild(int serverId, int type, String searchKey, int page) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SEARCHGUILD_INT_INT_STRING_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SEARCHGUILD_INT_INT_STRING_INT", new Object[] {serverId, type, searchKey, page});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#sendMailToGuild(long receiverId, boolean skipLeader, long senderId, int mailSn, String title, String content, String itemJSON, Param params, Object... contentParam)}*/
	public void sendMailToGuild(long receiverId, boolean skipLeader, long senderId, int mailSn, String title, String content, String itemJSON, Param params, Object... contentParam) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMAILTOGUILD_LONG_BOOLEAN_LONG_INT_STRING_STRING_STRING_PARAM_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMAILTOGUILD_LONG_BOOLEAN_LONG_INT_STRING_STRING_STRING_PARAM_OBJECTS", new Object[] {receiverId, skipLeader, senderId, mailSn, title, content, itemJSON, params, contentParam});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#sendMailToGuildLeader(long receiverId, long senderId, int mailSn, String title, String content, String itemJSON, Param params, Object... contentParam)}*/
	public void sendMailToGuildLeader(long receiverId, long senderId, int mailSn, String title, String content, String itemJSON, Param params, Object... contentParam) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMAILTOGUILDLEADER_LONG_LONG_INT_STRING_STRING_STRING_PARAM_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMAILTOGUILDLEADER_LONG_LONG_INT_STRING_STRING_STRING_PARAM_OBJECTS", new Object[] {receiverId, senderId, mailSn, title, content, itemJSON, params, contentParam});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildService#sendMsg_guild_schedule_s2c(List guildIdList, int state)}*/
	public void sendMsg_guild_schedule_s2c(List guildIdList, int state) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMSG_GUILD_SCHEDULE_S2C_LIST_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMSG_GUILD_SCHEDULE_S2C_LIST_INT", new Object[] {guildIdList, state});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildService#sendMsg_guild_schedule_s2cWarm(List guildIdList, int state)}*/
	public void sendMsg_guild_schedule_s2cWarm(List guildIdList, int state) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMSG_GUILD_SCHEDULE_S2CWARM_LIST_INT,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDMSG_GUILD_SCHEDULE_S2CWARM_LIST_INT", new Object[] {guildIdList, state});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildService#sendSeasonResetMailSn(List leagueVOList)}*/
	public void sendSeasonResetMailSn(List leagueVOList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDSEASONRESETMAILSN_LIST,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDSEASONRESETMAILSN_LIST", new Object[] {leagueVOList});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link GuildService#sendSettleGuildReward(Map leagueVOMap)}*/
	public void sendSettleGuildReward(Map leagueVOMap) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDSETTLEGUILDREWARD_MAP,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_SENDSETTLEGUILDREWARD_MAP", new Object[] {leagueVOMap});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#treasureBox(long guildId)}*/
	public void treasureBox(long guildId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_TREASUREBOX_LONG,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_TREASUREBOX_LONG", new Object[] {guildId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#update(String json)}*/
	public void update(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE_STRING,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#update1(Object... objs)}*/
	public void update1(Object... objs) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE1_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE1_OBJECTS", new Object[] {objs});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#update2(Param param)}*/
	public void update2(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE2_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE2_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#update3(String json)}*/
	public void update3(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE3_STRING,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATE3_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#updateGuildMemberHead(long guildId, long humanId, String head)}*/
	public void updateGuildMemberHead(long guildId, long humanId, String head) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATEGUILDMEMBERHEAD_LONG_LONG_STRING,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATEGUILDMEMBERHEAD_LONG_LONG_STRING", new Object[] {guildId, humanId, head});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#updateGuildMemberName(long guildId, long humanId, String name)}*/
	public void updateGuildMemberName(long guildId, long humanId, String name) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATEGUILDMEMBERNAME_LONG_LONG_STRING,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATEGUILDMEMBERNAME_LONG_LONG_STRING", new Object[] {guildId, humanId, name});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link GuildService#updateMemberCombat(long guildId, long humanId, Param param)}*/
	public void updateMemberCombat(long guildId, long humanId, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATEMEMBERCOMBAT_LONG_LONG_PARAM,"ORG_GOF_DEMO_WORLDSRV_GUILD_GUILDSERVICE_UPDATEMEMBERCOMBAT_LONG_LONG_PARAM", new Object[] {guildId, humanId, param});
		if(immutableOnce) immutableOnce = false;
	}
}
