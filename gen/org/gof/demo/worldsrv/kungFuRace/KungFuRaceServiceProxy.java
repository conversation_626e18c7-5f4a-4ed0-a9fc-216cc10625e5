package org.gof.demo.worldsrv.kungFuRace;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import java.util.List;
import org.gof.core.support.Param;

@GofGenFile
public final class KungFuRaceServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_ADJUSTBATTLEPOS_LONG_LIST = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_APPLYJOINTEAM_LONG_STRING_LONG = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_BETCOMPETITION_LONG_INT_LONG_INT = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_COMBATRESULT_LONG_INT_LIST_INT = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_CREATETEAM_LONG_STRING = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_ENTERCOMBAT_LONG_LONG_INT = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_EXITTEAM_LONG = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETBETHISTORY_LONG = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETCHAMPIONHISTORY_LONG_INT = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETCHAMPIONINFO_LONG_INT = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETKNOCKOUTINFO_LONG = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETLOOPBETINFO_LONG_INT_INT_INT = 12;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETLOOPINFO_LONG_INT_INT_INT = 13;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETLOOPRANKINFO_LONG_INT_INT = 14;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETMAINTEAMLIST_LONG = 15;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETOVERVIEW_LONG_INT = 16;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETQUALIFYINGBATTLEREPORT_LONG = 17;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETQUALIFYINGENEMYLIST_LONG_INT_INT = 18;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETQUALIFYINGRANKINFO_LONG_INT = 19;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETTEAMAPPLYLIST_LONG = 20;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETTEAMINFO_LONG_LONG = 21;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GMCOMMAND_STRING_PARAM = 22;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_INITAFTERCONNECTADMIN = 23;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_LOOKUPROLE_LONG_STRING = 24;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_LOOKUPTEAM_LONG_LONG = 25;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_PLAYBATTLEVIDEO_LONG_LONG = 26;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_PLAYBATTLEVIDEORESULT_LONG_LONG_INT = 27;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_RESPJOINTEAMAPPLY_LONG_LONG_INT = 28;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATE_STRING = 29;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATE1_OBJECTS = 30;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATE2_STRING = 31;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATEHUMANBRIEF_LONG = 32;
		public static final int ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_WORSHIPCHAMPION_LONG = 33;
	}

	private static final String SERV_ID = "kungFuRace";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private KungFuRaceServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		KungFuRaceService serv = (KungFuRaceService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_ADJUSTBATTLEPOS_LONG_LIST: {
				return (GofFunction2<Long, List>)serv::adjustBattlePos;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_APPLYJOINTEAM_LONG_STRING_LONG: {
				return (GofFunction3<Long, String, Long>)serv::applyJoinTeam;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_BETCOMPETITION_LONG_INT_LONG_INT: {
				return (GofFunction4<Long, Integer, Long, Integer>)serv::betCompetition;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_COMBATRESULT_LONG_INT_LIST_INT: {
				return (GofFunction4<Long, Integer, List, Integer>)serv::combatResult;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_CREATETEAM_LONG_STRING: {
				return (GofFunction2<Long, String>)serv::createTeam;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_ENTERCOMBAT_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::enterCombat;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_EXITTEAM_LONG: {
				return (GofFunction1<Long>)serv::exitTeam;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETBETHISTORY_LONG: {
				return (GofFunction1<Long>)serv::getBetHistory;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETCHAMPIONHISTORY_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::getChampionHistory;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETCHAMPIONINFO_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::getChampionInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETKNOCKOUTINFO_LONG: {
				return (GofFunction1<Long>)serv::getKnockoutInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETLOOPBETINFO_LONG_INT_INT_INT: {
				return (GofFunction4<Long, Integer, Integer, Integer>)serv::getLoopBetInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETLOOPINFO_LONG_INT_INT_INT: {
				return (GofFunction4<Long, Integer, Integer, Integer>)serv::getLoopInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETLOOPRANKINFO_LONG_INT_INT: {
				return (GofFunction3<Long, Integer, Integer>)serv::getLoopRankInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETMAINTEAMLIST_LONG: {
				return (GofFunction1<Long>)serv::getMainTeamList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETOVERVIEW_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::getOverView;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETQUALIFYINGBATTLEREPORT_LONG: {
				return (GofFunction1<Long>)serv::getQualifyingBattleReport;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETQUALIFYINGENEMYLIST_LONG_INT_INT: {
				return (GofFunction3<Long, Integer, Integer>)serv::getQualifyingEnemyList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETQUALIFYINGRANKINFO_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::getQualifyingRankInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETTEAMAPPLYLIST_LONG: {
				return (GofFunction1<Long>)serv::getTeamApplyList;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETTEAMINFO_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::getTeamInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GMCOMMAND_STRING_PARAM: {
				return (GofFunction2<String, Param>)serv::gmCommand;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_INITAFTERCONNECTADMIN: {
				return (GofFunction0)serv::initAfterConnectAdmin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_LOOKUPROLE_LONG_STRING: {
				return (GofFunction2<Long, String>)serv::lookupRole;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_LOOKUPTEAM_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::lookupTeam;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_PLAYBATTLEVIDEO_LONG_LONG: {
				return (GofFunction2<Long, Long>)serv::playBattleVideo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_PLAYBATTLEVIDEORESULT_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::playBattleVideoResult;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_RESPJOINTEAMAPPLY_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::respJoinTeamApply;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATE_STRING: {
				return (GofFunction1<String>)serv::update;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATE1_OBJECTS: {
				return (GofFunction1<Object[]>)serv::update1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATE2_STRING: {
				return (GofFunction1<String>)serv::update2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATEHUMANBRIEF_LONG: {
				return (GofFunction1<Long>)serv::updateHumanBrief;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_WORSHIPCHAMPION_LONG: {
				return (GofFunction1<Long>)serv::worshipChampion;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static KungFuRaceServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static KungFuRaceServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static KungFuRaceServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static KungFuRaceServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static KungFuRaceServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static KungFuRaceServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static KungFuRaceServiceProxy createInstance(String node, String port, Object serviceId) {
		KungFuRaceServiceProxy inst = new KungFuRaceServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	@SuppressWarnings("rawtypes")
	/** {@link KungFuRaceService#adjustBattlePos(long humanId, List posList)}*/
	public void adjustBattlePos(long humanId, List posList) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_ADJUSTBATTLEPOS_LONG_LIST,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_ADJUSTBATTLEPOS_LONG_LIST", new Object[] {humanId, posList});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#applyJoinTeam(long humanId, String playerName, long teamId)}*/
	public void applyJoinTeam(long humanId, String playerName, long teamId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_APPLYJOINTEAM_LONG_STRING_LONG,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_APPLYJOINTEAM_LONG_STRING_LONG", new Object[] {humanId, playerName, teamId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#betCompetition(long humanId, int stage, long teamId, int betNum)}*/
	public void betCompetition(long humanId, int stage, long teamId, int betNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_BETCOMPETITION_LONG_INT_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_BETCOMPETITION_LONG_INT_LONG_INT", new Object[] {humanId, stage, teamId, betNum});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link KungFuRaceService#combatResult(long humanId, int result, List extList, int combatNum)}*/
	public void combatResult(long humanId, int result, List extList, int combatNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_COMBATRESULT_LONG_INT_LIST_INT,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_COMBATRESULT_LONG_INT_LIST_INT", new Object[] {humanId, result, extList, combatNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#createTeam(long humanId, String teamName)}*/
	public void createTeam(long humanId, String teamName) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_CREATETEAM_LONG_STRING,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_CREATETEAM_LONG_STRING", new Object[] {humanId, teamName});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#enterCombat(long humanId, long enemyTeamId, int combatNum)}*/
	public void enterCombat(long humanId, long enemyTeamId, int combatNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_ENTERCOMBAT_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_ENTERCOMBAT_LONG_LONG_INT", new Object[] {humanId, enemyTeamId, combatNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#exitTeam(long humanId)}*/
	public void exitTeam(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_EXITTEAM_LONG,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_EXITTEAM_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getBetHistory(long humanId)}*/
	public void getBetHistory(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETBETHISTORY_LONG,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETBETHISTORY_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getChampionHistory(long humanId, int season)}*/
	public void getChampionHistory(long humanId, int season) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETCHAMPIONHISTORY_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETCHAMPIONHISTORY_LONG_INT", new Object[] {humanId, season});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getChampionInfo(long humanId, int worshipNum)}*/
	public void getChampionInfo(long humanId, int worshipNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETCHAMPIONINFO_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETCHAMPIONINFO_LONG_INT", new Object[] {humanId, worshipNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getKnockoutInfo(long humanId)}*/
	public void getKnockoutInfo(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETKNOCKOUTINFO_LONG,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETKNOCKOUTINFO_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getLoopBetInfo(long humanId, int group, int action, int stage)}*/
	public void getLoopBetInfo(long humanId, int group, int action, int stage) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETLOOPBETINFO_LONG_INT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETLOOPBETINFO_LONG_INT_INT_INT", new Object[] {humanId, group, action, stage});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getLoopInfo(long humanId, int group, int round, int stage)}*/
	public void getLoopInfo(long humanId, int group, int round, int stage) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETLOOPINFO_LONG_INT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETLOOPINFO_LONG_INT_INT_INT", new Object[] {humanId, group, round, stage});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getLoopRankInfo(long humanId, int group, int stage)}*/
	public void getLoopRankInfo(long humanId, int group, int stage) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETLOOPRANKINFO_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETLOOPRANKINFO_LONG_INT_INT", new Object[] {humanId, group, stage});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getMainTeamList(long humanId)}*/
	public void getMainTeamList(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETMAINTEAMLIST_LONG,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETMAINTEAMLIST_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getOverView(long humanId, int combatNum)}*/
	public void getOverView(long humanId, int combatNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETOVERVIEW_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETOVERVIEW_LONG_INT", new Object[] {humanId, combatNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getQualifyingBattleReport(long humanId)}*/
	public void getQualifyingBattleReport(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETQUALIFYINGBATTLEREPORT_LONG,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETQUALIFYINGBATTLEREPORT_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getQualifyingEnemyList(long humanId, int type, int combatNum)}*/
	public void getQualifyingEnemyList(long humanId, int type, int combatNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETQUALIFYINGENEMYLIST_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETQUALIFYINGENEMYLIST_LONG_INT_INT", new Object[] {humanId, type, combatNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getQualifyingRankInfo(long humanId, int page)}*/
	public void getQualifyingRankInfo(long humanId, int page) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETQUALIFYINGRANKINFO_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETQUALIFYINGRANKINFO_LONG_INT", new Object[] {humanId, page});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getTeamApplyList(long humanId)}*/
	public void getTeamApplyList(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETTEAMAPPLYLIST_LONG,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETTEAMAPPLYLIST_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#getTeamInfo(long humanId, long teamId)}*/
	public void getTeamInfo(long humanId, long teamId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETTEAMINFO_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GETTEAMINFO_LONG_LONG", new Object[] {humanId, teamId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#gmCommand(String type, Param param)}*/
	public void gmCommand(String type, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GMCOMMAND_STRING_PARAM,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_GMCOMMAND_STRING_PARAM", new Object[] {type, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#initAfterConnectAdmin()}*/
	public void initAfterConnectAdmin() {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_INITAFTERCONNECTADMIN,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_INITAFTERCONNECTADMIN", new Object[] {});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#lookupRole(long humanId, String playerName)}*/
	public void lookupRole(long humanId, String playerName) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_LOOKUPROLE_LONG_STRING,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_LOOKUPROLE_LONG_STRING", new Object[] {humanId, playerName});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#lookupTeam(long humanId, long teamId)}*/
	public void lookupTeam(long humanId, long teamId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_LOOKUPTEAM_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_LOOKUPTEAM_LONG_LONG", new Object[] {humanId, teamId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#playBattleVideo(long humanId, long videoId)}*/
	public void playBattleVideo(long humanId, long videoId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_PLAYBATTLEVIDEO_LONG_LONG,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_PLAYBATTLEVIDEO_LONG_LONG", new Object[] {humanId, videoId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#playBattleVideoResult(long humanId, long videoId, int result)}*/
	public void playBattleVideoResult(long humanId, long videoId, int result) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_PLAYBATTLEVIDEORESULT_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_PLAYBATTLEVIDEORESULT_LONG_LONG_INT", new Object[] {humanId, videoId, result});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#respJoinTeamApply(long humanId, long applyHumanId, int isAgree)}*/
	public void respJoinTeamApply(long humanId, long applyHumanId, int isAgree) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_RESPJOINTEAMAPPLY_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_RESPJOINTEAMAPPLY_LONG_LONG_INT", new Object[] {humanId, applyHumanId, isAgree});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#update(String jo)}*/
	public void update(String jo) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATE_STRING,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATE_STRING", new Object[] {jo});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#update1(Object... objs)}*/
	public void update1(Object... objs) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATE1_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATE1_OBJECTS", new Object[] {objs});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#update2(String str)}*/
	public void update2(String str) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATE2_STRING,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATE2_STRING", new Object[] {str});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#updateHumanBrief(long humanId)}*/
	public void updateHumanBrief(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATEHUMANBRIEF_LONG,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_UPDATEHUMANBRIEF_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link KungFuRaceService#worshipChampion(long humanId)}*/
	public void worshipChampion(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_WORSHIPCHAMPION_LONG,"ORG_GOF_DEMO_WORLDSRV_KUNGFURACE_KUNGFURACESERVICE_WORSHIPCHAMPION_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}
}
