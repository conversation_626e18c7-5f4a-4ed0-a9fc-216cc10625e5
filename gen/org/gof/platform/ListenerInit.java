package org.gof.platform;
import org.gof.core.support.observer.ObServer;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;

@GofGenFile
public final class ListenerInit{
	public static <K,P> void init(ObServer<K, P> ob){
		ob.reg("4097$/CSSC", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.platform.integration.CSSCManager.class))::onCSSC, 1);  
		ob.reg("4097$/checkServer", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.platform.integration.CheckPFManager.class))::onCheckServer, 1);  
		ob.reg("4097$/GMNeedResult", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.platform.integration.GmCmdManager.class))::_GM_CMD_NEED_RESULT, 1);  
		ob.reg("4097$countAll", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.platform.integration.GmCmdManager.class))::onCOUNT_BY, 1);  
		ob.reg("4097$/gameServer", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.platform.integration.GmCmdManager.class))::onGAME_SERVER, 1);  
		ob.reg("4097$/GM", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.platform.integration.GmCmdManager.class))::onGMCmd, 1);  
		ob.reg("4097$/GMParseType2", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.platform.integration.GmCmdManager.class))::onGMCmdBody, 1);  
		ob.reg("4097$/queryRole", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.platform.integration.GmCmdManager.class))::onQUERY_ROLE, 1);  
		ob.reg("4097$/loginCheck", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.platform.integration.LoginManager.class))::onLogin, 1);  
		ob.reg("4097$/payNotify", (GofFunction1<org.gof.core.support.Param>)(ob.getTargetBean(org.gof.platform.integration.PayManager.class))::onPay, 1);  
	}
}

