package org.gof.demo.worldsrv.charm;

import io.vertx.core.json.JsonArray;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.appearance.SkinVo;
import org.gof.demo.worldsrv.character.HumanDailyResetInfo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.human.EModule;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgCharm;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.DailyResetTypeKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.treasureSkin.TreasureSkinInfo;

import java.util.*;
import java.util.stream.Collectors;

public class CharmManager extends ManagerBase {
    /**
     * 获取实例
     */
    public static CharmManager inst() {
        return inst(CharmManager.class);
    }

    /**
     * 初始化
     */
    static public void initCharmData(HumanObject humanObj, Charm charm) {
        if (charm == null) {
            return;
        }
        // 创建CharmData并存储到humanObj.operation中
        humanObj.operation.charmData = new CharmData(charm);
    }

    /**
     * 发送美观值信息
     * @param humanObj
     */
    public void sendCharmInfo(HumanObject humanObj) {
        String totalNumKey = CharmUtils.getCharmLikeTotalNumKey(humanObj.id);
        RedisTools.get(EntityManager.redisClient, totalNumKey, res -> {
            if (res.failed()) {
                Log.charm.warn("获取失败，totalNumKey={}", totalNumKey);
                return;
            }
            int likeTotalNum = Utils.intValue(res.result());
            String todayLikeSetKey = CharmUtils.getCharmTodayLikeSetKey(humanObj.id);
            Utils.getRedisSet(todayLikeSetKey, res2 -> {
                if (res2.failed()) {
                    Log.charm.warn("===getRedisSet失败，todayLikeSetKey={}", todayLikeSetKey);
                    return;
                }
                List<String> todayLikeHumanIdList =  res2.result();
                Charm charm = humanObj.operation.charmData.charm;
                MsgCharm.charm_info_s2c.Builder msg = MsgCharm.charm_info_s2c.newBuilder();
                msg.setCharmValue(charm.getCharmValue());
                List<Integer> showMedalList = Utils.strToIntList(charm.getShowMedalList());
                msg.addAllShowMedalList(showMedalList);
                List<Integer> receivedLevelRewardList = Utils.strToIntList(charm.getReceivedLevelRewardList());
                msg.addAllReceivedLevelRewardList(receivedLevelRewardList);
                List<Integer> unlockMedalList = Utils.strToIntList(charm.getUnlockMedalList());
                msg.addAllUnlockMedalList(unlockMedalList);
                HumanDailyResetInfo info = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyClaimedLikeRewardNum.getType());
                msg.setClaimedLikeRewardNum(info.getValue());
                msg.setLikeTotalNum(likeTotalNum);
                msg.addAllTodayLikeSet(todayLikeHumanIdList.stream().map(Utils::longValue).collect(Collectors.toList()));
                humanObj.sendMsg(msg);
//                Log.charm.info("===美观值信息，msg={} humanId={}", msg, humanObj.id);
            });
        });
    }

    /**
     * 领取美观等级奖励
     * @param humanObj
     * @param claimSn
     */
    public void _msg_charm_claim_level_reward_c2s(HumanObject humanObj, int claimSn) {
        Charm charm = humanObj.operation.charmData.charm;
        int curSn = CharmUtils.getCharmLevelSn(charm.getCharmValue());
        if(claimSn > curSn){
            Log.charm.warn("===领取美观等级奖励失败，claimSn={} > curSn={} humanId={}", claimSn, curSn, humanObj.id);
            return;
        }
        List<Integer> receivedLevelRewardList = Utils.strToIntList(charm.getReceivedLevelRewardList());
        if(receivedLevelRewardList.contains(claimSn)){
            Log.charm.warn("===领取美观等级奖励失败，已领取过sn={}的奖励 humanId={}", claimSn, humanObj.id);
            return;
        }
        ConfCharmLevel conf = ConfCharmLevel.get(claimSn);
        if(conf == null || conf.reward == null){
            Log.charm.warn("===领取美观等级奖励失败，sn={}的奖励配置不存在 humanId={}", claimSn, humanObj.id);
            return;
        }
        // 给奖励
        int[][] rewards = conf.reward;
        ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.美观值等级奖励);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewards);
        // 添加已领取的sn
        receivedLevelRewardList.add(claimSn);
        charm.setReceivedLevelRewardList(Utils.arrayIntToStr(receivedLevelRewardList));
        MsgCharm.charm_claim_level_reward_s2c.Builder msg = MsgCharm.charm_claim_level_reward_s2c.newBuilder();
        msg.setCharmSn(claimSn);
        humanObj.sendMsg(msg);
//        Log.charm.info("===领取美观等级奖励成功，claimSn={} curSn={} humanId={}", claimSn, curSn, humanObj.id);
    }

    /**
     * 保存展示勋章列表
     * @param humanObj
     * @param showMedalList
     */
    public void _msg_charm_save_show_medal_list_c2s(HumanObject humanObj, List<Integer> showMedalList) {
        Charm charm = humanObj.operation.charmData.charm;
        int medalUnlockNum = CharmUtils.getMedalUnlockNum(charm.getCharmValue());
        if (showMedalList.size() > medalUnlockNum) {
            Log.charm.warn("===保存展示勋章列表失败，勋章列表长度={}大于已解锁长度={} humanId={}", showMedalList.size(), medalUnlockNum, humanObj.id);
            return;
        }
        List<Integer> unlockMedalList = Utils.strToIntList(charm.getUnlockMedalList());
        // 检查要展示的勋章是否都已解锁
        for (int medalId : showMedalList) {
            if (!unlockMedalList.contains(medalId)) {
                Log.charm.warn("===保存展示勋章列表失败，尝试展示未解锁的勋章={} humanId={}", medalId, humanObj.id);
                return;
            }
        }
        charm.setShowMedalList(Utils.arrayIntToStr(showMedalList));
        MsgCharm.charm_save_show_medal_list_s2c.Builder msg = MsgCharm.charm_save_show_medal_list_s2c.newBuilder();
        msg.addAllShowMedalList(showMedalList);
        humanObj.sendMsg(msg);
//        Log.charm.info("===保存展示勋章列表成功，showMedalList={} humanId={}", showMedalList, humanObj.id);
    }

    /**
     * 获取收藏室信息
     * @param humanObj
     * @param targetId
     */
    public void _msg_charm_collection_room_info_c2s(HumanObject humanObj, long targetId) {
        HumanData.getHumanDataAsync(targetId, HumanManager.humanClasses2, res -> {
            if (res.failed()) {
                Log.charm.warn("获取玩家简要信息2取HumanData时出错, id={}, e={}", targetId, res.cause());
                return;
            }
            HumanData humanData = res.result();
            if (humanData == null || humanData.human == null) {
                Log.charm.warn("获取玩家简要信息2取HumanData时出错, id={}", targetId);
                return;
            }
            String totalNumKey = CharmUtils.getCharmLikeTotalNumKey(targetId);
            RedisTools.get(EntityManager.redisClient, totalNumKey, res2 -> {
                if (res2.failed()) {
                    Log.charm.warn("获取失败，totalNumKey={}", totalNumKey);
                    return;
                }
                int likeTotalNum = Utils.intValue(res2.result());
                Human human = humanData.human;
                Charm charm = humanData.charm;
                MsgCharm.charm_collection_room_info_s2c.Builder msg = MsgCharm.charm_collection_room_info_s2c.newBuilder();
                msg.setTargetId(targetId);
                msg.setName(human.getName());
                msg.setLevel(human.getLevel());
                Define.p_head.Builder head = Define.p_head.newBuilder();
                head.setId(human.getHeadSn());
                head.setFrameId(human.getCurrentHeadFrameSn());
                msg.setHead(head);
                msg.setLikeTotalNum(likeTotalNum);
                if(charm != null){
                    int charmSn = CharmUtils.getCharmLevelSn(charm.getCharmValue());
                    msg.setCharmSn(charmSn);
                    List<Integer> showMedalList = Utils.strToIntList(charm.getShowMedalList());
                    msg.addAllShowMedalList(showMedalList);
                    try{
                        Define.p_charm_collection_room collectionRoom = Define.p_charm_collection_room.parseFrom(charm.getCollectionRoomData());
                        if(collectionRoom == null){
                            Log.charm.warn("===获取收藏室信息失败，collectionRoomInfo解析错误 humanId={}", humanObj.id);
                            return;
                        }
                        msg.addAllRoleAreaList(collectionRoom.getRoleAreaListList());
                        msg.addAllOtherArea(collectionRoom.getOtherAreaList());
                    }catch (Exception e){
                        Log.charm.error("charmData收藏室数据解析错误 humanId={}", charm.getId());
                    }
                }
                humanObj.sendMsg(msg);
//                Log.charm.info("===获取收藏室信息成功，msg={} humanId={}", msg, humanObj.id);
            });
        });
    }


    /**
     * 保存展示区
     * @param humanObj
     * @param roleAreaList
     * @param otherArea
     */
    public void _msg_charm_save_display_area_c2s(HumanObject humanObj, List<Define.p_charm_role_area> roleAreaList, List<Define.p_charm_other_area_item> otherArea) {
        CharmData charmData = humanObj.operation.charmData;
        if(charmData == null){
            return;
        }
        charmData.saveDisplayArea(humanObj, roleAreaList, otherArea);
    }

    /**
     * 点赞收藏室
     * @param humanObj
     * @param targetId
     */
    public void _msg_charm_like_collection_room_c2s(HumanObject humanObj, long targetId) {
        long humanId = humanObj.id;
        if(targetId == humanId){
            Log.charm.warn("点赞收藏室失败，无法点赞自己");
            return;
        }
        String todayLikeSetKey = CharmUtils.getCharmTodayLikeSetKey(humanId);
        RedisTools.isMember(EntityManager.redisClient, todayLikeSetKey, String.valueOf(targetId), res -> {
            if (res.failed()) {
                Log.charm.warn("===isMember失败，todayLikeSetKey={}", todayLikeSetKey);
                return;
            }
            boolean result = res.result();
            if(result){
                Log.charm.warn("===点赞收藏室失败，已给玩家{}的收藏室点过赞 humanId={}", targetId, humanId);
                return;
            }
            RedisTools.addToSet(EntityManager.redisClient, todayLikeSetKey, String.valueOf(targetId), res2->{
                if (res2.failed()) {
                    Log.charm.warn("===addToSet失败，todayLikeSetKey={}", todayLikeSetKey);
                    return;
                }
                RedisTools.expire(EntityManager.redisClient, todayLikeSetKey, CharmConst.CHARM_LIKE_KEY_EXPIRE_TIME);
                String totalNumKey = CharmUtils.getCharmLikeTotalNumKey(targetId);
                RedisTools.add(EntityManager.redisClient, totalNumKey, 1, res3->{
                    if(res3.failed()){
                        Log.charm.warn("===增加收藏室总点赞数失败，totalNumKey={}", totalNumKey);
                        return;
                    }
                    int curLikeTotalNum = Utils.intValue(res3.result());
                    String member = CharmUtils.buildCharmLikeRecordMember(humanId, targetId);
                    // 给对方的列表，增加点赞记录
                    addLikeRecord(targetId, member);
                    // 给自己的列表，增加点赞记录
                    addLikeRecord(humanId, member);
                    ConfGlobal confCharmLikeTimes = ConfGlobal.get(ConfGlobalKey.charm_likes_times);
                    ConfGlobal confCharmLikeReward = ConfGlobal.get(ConfGlobalKey.charm_likes_reward);
                    int maxLikeNum = confCharmLikeTimes != null ? confCharmLikeTimes.value : 20;
                    int outputId = confCharmLikeReward != null ? confCharmLikeReward.value : 1204001;
                    HumanDailyResetInfo claimedInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyClaimedLikeRewardNum.getType());
                    if(claimedInfo.getValue() < maxLikeNum){
                        // 给奖励
                        Map<Integer, Integer> rewards = ProduceManager.inst().getDropMap(outputId);
                        ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.美观值点赞奖励);
                        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewards);
                        claimedInfo.setValue(claimedInfo.getValue() + 1);
                        humanObj.saveDailyResetRecord();
                    }
                    MsgCharm.charm_like_collection_room_s2c.Builder msg = MsgCharm.charm_like_collection_room_s2c.newBuilder();
                    msg.setTargetId(targetId);
                    msg.setLikeTotalNum(curLikeTotalNum);
                    msg.setClaimedLikeRewardNum(claimedInfo.getValue());
                    humanObj.sendMsg(msg);
//                    Log.charm.info("===点赞收藏室成功，targetId={} msg={} humanId={}", targetId, msg, humanId);
                });
            });
        });
    }

    /**
     * 增加点赞记录
     * @param targeId
     * @param member
     */
    public void addLikeRecord(long targeId, String member) {
        String likeRecordListKey = CharmUtils.getCharmLikeRecordListKey(targeId);
        RedisTools.addRank(EntityManager.redisClient, likeRecordListKey, member, Port.getTime(), res4->{
            if(res4.failed()){
                Log.charm.warn("===添加点赞记录列表失败，likeRecordListKey={}", likeRecordListKey);
                return;
            }
            // 保留score最大的30个元素
            int maxNum = 30;
            EntityManager.redisClient.zremrangebyrank(likeRecordListKey, "0", String.valueOf(-(maxNum+1)));
        });
    }

    /**
     * 查看收藏室点赞记录
     * @param humanObj
     */
    public void _msg_charm_collection_room_like_list_c2s(HumanObject humanObj) {
        String likeRecordListKey = CharmUtils.getCharmLikeRecordListKey(humanObj.id);
        RedisTools.getRankListByIndex(EntityManager.redisClient, likeRecordListKey, 0, -1, false, res->{
            if(res.failed()){
                Log.charm.warn("===获取点赞记录列表失败，likeRecordListKey={}", likeRecordListKey);
                return;
            }
            JsonArray json = res.result();
            Set<Long> searchIdSet = new HashSet<>();
            List<String[]> memberInfoList = new ArrayList<>();
            for(int i = 0; i < json.getList().size(); i++) {
                String member = String.valueOf(json.getList().get(i));
                String[] memberArr = member.split("_");
                if(memberArr.length != 3){
                    continue;
                }
                long likerId = Utils.longValue(memberArr[0]);
                if(likerId != humanObj.id){
                    // 自己的信息不用查
                    searchIdSet.add(likerId);
                }
                long targetId = Utils.longValue(memberArr[1]);
                if(targetId != humanObj.id){
                    // 自己的信息不用查
                    searchIdSet.add(likerId);
                }
                memberInfoList.add(memberArr);
            }
            if(searchIdSet.isEmpty()){
                sendLikeList(humanObj, new HashMap<>(), memberInfoList);
                return;
            }
            EntityManager.batchGetEntity(Human.class, new ArrayList<>(searchIdSet), res2 -> {
                if (res2.failed()) {
                    Log.charm.warn("===batchGetEntity failed, humanIdList={}", searchIdSet);
                    return;
                }
                List<Human> searchHumanList = res2.result();
                Map<Long, Human> searchHumanMap = searchHumanList.stream().collect(Collectors.toMap(
                        (Human::getId),
                        (v->v),
                        (existing, replacement) -> existing  // 处理重复key的情况，保留已存在的
                ));
                sendLikeList(humanObj, searchHumanMap, memberInfoList);
            });
        });
    }

    /**
     * 发送点赞列表
     * @param humanObj
     * @param searchHumanMap
     * @param memberInfoList
     */
    public void sendLikeList(HumanObject humanObj, Map<Long, Human> searchHumanMap, List<String[]> memberInfoList){
        MsgCharm.charm_collection_room_like_list_s2c.Builder msg = MsgCharm.charm_collection_room_like_list_s2c.newBuilder();
        List<Define.p_charm_like_info> likeRecordList = new ArrayList<>();
        for(String[] memberArr : memberInfoList) {
            long likerId = Utils.longValue(memberArr[0]);
            long targetId = Utils.longValue(memberArr[1]);
            int timestamp = Utils.intValue(Utils.longValue(memberArr[2]) / Time.SEC);
            Define.p_charm_like_info.Builder likeInfo = Define.p_charm_like_info.newBuilder();
            likeInfo.setLikerId(likerId);
            if(likerId != humanObj.id){
                Human likerHuman = searchHumanMap.get(likerId);
                if(likerHuman != null){
                    likeInfo.setName(likerHuman.getName());
                    likeInfo.setLevel(likerHuman.getLevel());
                    Define.p_head.Builder head = Define.p_head.newBuilder();
                    head.setId(likerHuman.getHeadSn());
                    head.setFrameId(likerHuman.getCurrentHeadFrameSn());
                    likeInfo.setHead(head);
                }
            }
            likeInfo.setTimestamp(timestamp);
            likeInfo.setTargetId(targetId);
            if(targetId != humanObj.id){
                Human targetHuman = searchHumanMap.get(targetId);
                if(targetHuman != null){
                    likeInfo.setTargetName(targetHuman.getName());
                }
            }
            msg.addLikeList(likeInfo);
        }
        msg.addAllLikeList(likeRecordList);
        humanObj.sendMsg(msg);
//        Log.charm.info("===查看收藏室点赞记录成功，msg={} humanId={}", msg, humanObj.id);
    }

    /**
     * 查看目标美观值信息
     * @param humanObj
     * @param targetId
     */
    public void _msg_charm_target_charm_info_c2s(HumanObject humanObj, long targetId) {
        String totalNumKey = CharmUtils.getCharmLikeTotalNumKey(targetId);
        RedisTools.get(EntityManager.redisClient, totalNumKey, res -> {
            if (res.failed()) {
                Log.charm.warn("获取失败，totalNumKey={}", totalNumKey);
                return;
            }
            int likeTotalNum = Utils.intValue(res.result());
            MsgCharm.charm_target_charm_info_s2c.Builder msg = MsgCharm.charm_target_charm_info_s2c.newBuilder();
            msg.setTargetId(targetId);
            msg.setLikeTotalNum(likeTotalNum);
            humanObj.sendMsg(msg);
//            Log.charm.info("===查看目标美观值信息成功，msg={} humanId={}", msg, humanObj.id);
        });
    }

    /**
     * 重新计算美观值
     * @param humanObj
     * @param notifyUpdate
     */
    static public void reCalcCharmValue(HumanObject humanObj, boolean notifyUpdate){
        Charm charm = humanObj.operation.charmData.charm;
        int oldCharmValue = charm.getCharmValue();
        int oldCharmLevel = CharmUtils.getCharmLevelSn(oldCharmValue);
        // 时装
        int skinCharm = 0;
        List<SkinVo> skinVoList = SkinVo.fromJSONArrayStrToList(humanObj.getHumanExtInfo().getSkinList());
        for (SkinVo vo : skinVoList) {
            ConfSkin confSkin = ConfSkin.get(vo.sn);
            if(confSkin != null){
                int levelOffset = Math.max(0, vo.level - 1);
                skinCharm += confSkin.charm_value + levelOffset * confSkin.charm_value_level;
            }
        }
        // 坐骑
        int mountCharm = 0;
        Mount mount = humanObj.operation.mount;
        if(mount != null){
            Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(mount.getSkinLvMap());
            for (Map.Entry<Integer, Integer> entry : skinLvMap.entrySet()) {
                ConfMount confMount = ConfMount.get(entry.getKey());
                if(confMount != null){
                    int levelOffset = Math.max(0, entry.getValue() - 1);
                    mountCharm += confMount.charm_value + levelOffset * confMount.charm_value_level;
                }
            }
        }
        // 神器
        int artifactCharm = 0;
        Artifact artifact = humanObj.artifact.atf;
        if(artifact != null){
            Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(artifact.getSkinLvMap());
            for (Map.Entry<Integer, Integer> entry : skinLvMap.entrySet()) {
                ConfArtifact confArtifact = ConfArtifact.get(entry.getKey());
                if(confArtifact != null){
                    int levelOffset = Math.max(0, entry.getValue() - 1);
                    artifactCharm += confArtifact.charm_value + levelOffset * confArtifact.charm_value_level;
                }
            }
        }
        // 背饰
        int wingCharm = 0;
        Wing wing = humanObj.operation.wing;
        if(wing != null){
            Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(wing.getSkinLvMap());
            for (Map.Entry<Integer, Integer> entry : skinLvMap.entrySet()) {
                ConfBackDecoration confBackDecoration = ConfBackDecoration.get(entry.getKey());
                if(confBackDecoration != null){
                    int levelOffset = Math.max(0, entry.getValue() - 1);
                    wingCharm += confBackDecoration.charm_value + levelOffset * confBackDecoration.charm_value_level;
                }
            }
        }
        // 伙伴皮肤
        int petSkinCharm = 0;
        Map<Integer, Integer> petSkinLvMap = humanObj.operation.petData.petSkinLvMap;
        for (Map.Entry<Integer, Integer> entry : petSkinLvMap.entrySet()) {
            ConfPetSkin confPetSkin = ConfPetSkin.get(entry.getKey());
            if(confPetSkin != null){
                int levelOffset = Math.max(0, entry.getValue() - 1);
                petSkinCharm += confPetSkin.charm_value + levelOffset * confPetSkin.charm_value_level;
            }
        }
        // 虫茧皮肤
        int treasureSkinCharm = 0;
        Map<Integer, TreasureSkinInfo> treasureSkinMap = humanObj.getTreasureSkinMap();
        for (int treasureSn : treasureSkinMap.keySet()) {
            ConfTreasureSkin confTreasureSkin = ConfTreasureSkin.get(treasureSn);
            if(confTreasureSkin != null){
                treasureSkinCharm += confTreasureSkin.charm_value;
            }
        }
        int totalCharm = skinCharm + mountCharm + artifactCharm + wingCharm + petSkinCharm + treasureSkinCharm;
        charm.setCharmValue(totalCharm);
        if(totalCharm > oldCharmValue){
            Log.charm.info("===美观值总计={} 原值={} 时装={} 坐骑={} 神器={} 背饰={} 伙伴皮肤={} 虫茧皮肤={} humanId={}",
                    totalCharm, oldCharmValue, skinCharm, mountCharm, artifactCharm, wingCharm, petSkinCharm, treasureSkinCharm, humanObj.id);
            // 计算新解锁的勋章
            List<Integer> newUnlockMedalList = new ArrayList<>();
            List<Integer> unlockMedalList = Utils.strToIntList(charm.getUnlockMedalList());
            for(ConfMedal confMedal : ConfMedal.findAll()){
                if(unlockMedalList.contains(confMedal.sn)){
                    continue;
                }
                if(checkMedalUnlock(humanObj, confMedal)){
                    newUnlockMedalList.add(confMedal.sn);
                }
            }
            // 保存新解锁的勋章
            unlockMedalList.addAll(newUnlockMedalList);
            charm.setUnlockMedalList(Utils.arrayIntToStr(unlockMedalList));
            if(notifyUpdate){
                MsgCharm.charm_update_info_s2c.Builder msg = MsgCharm.charm_update_info_s2c.newBuilder();
                msg.setCharmValue(totalCharm);
                msg.addAllNewUnlockMedalList(newUnlockMedalList);
                humanObj.sendMsg(msg);
                Log.charm.info("===美观值信息更新，msg={} humanId={}", msg, humanObj.id);
            }
            int newCharmLevel = CharmUtils.getCharmLevelSn(totalCharm);
            if(!newUnlockMedalList.isEmpty() || newCharmLevel > oldCharmLevel){
                // 计算美观值的属性加成
                updatePropCalcPower(humanObj, newCharmLevel);
            }
        }
    }

    /**
     * 检查勋章是否解锁
     * @param humanObj
     * @param confMedal
     * @return
     */
    static public boolean checkMedalUnlock(HumanObject humanObj, ConfMedal confMedal){
        int[] condition = confMedal.condition;
        int type = condition[0];
        int value = condition[1];
        switch(type){
            case CharmConst.CHARM_MEDAL_UNLOCK_CONDITION_119: {
                Artifact artifact = humanObj.artifact.atf;
                if (artifact == null) {
                    return false;
                }
                Map<Long, Long> atfSkinMap = Utils.jsonToMapLongLong(artifact.getSkinLvMap());
                return atfSkinMap.size() >= value;
            }
            case CharmConst.CHARM_MEDAL_UNLOCK_CONDITION_120:
                Mount mount = humanObj.operation.mount;
                if(mount == null){
                    return false;
                }
                Map<Long, Long> mountSkinMap = Utils.jsonToMapLongLong(mount.getSkinLvMap());
                return mountSkinMap.size() >= value;
            case CharmConst.CHARM_MEDAL_UNLOCK_CONDITION_121:
                Wing wing = humanObj.operation.wing;
                if(wing == null){
                    return false;
                }
                Map<Long, Long> wingSkinMap = Utils.jsonToMapLongLong(wing.getSkinLvMap());
                return wingSkinMap.size() >= value;
            case CharmConst.CHARM_MEDAL_UNLOCK_CONDITION_2015:
                Charm charm = humanObj.operation.charmData.charm;
                int charmSn = CharmUtils.getCharmLevelSn(charm.getCharmValue());
                return charmSn >= value;
            case CharmConst.CHARM_MEDAL_UNLOCK_CONDITION_2016:
                Map<Integer, Integer> petSnLvMap = humanObj.operation.petData.petSnLvMap;
                return petSnLvMap.size() >= value;
            case CharmConst.CHARM_MEDAL_UNLOCK_CONDITION_2017:
                int jobSn = humanObj.operation.profession.getJobSn();
                ConfJobs confJobs = ConfJobs.get(jobSn);
                if(confJobs == null){
                    return false;
                }
                return confJobs.change_times >= value;
            default:
                Log.charm.warn("===检查勋章是否解锁 type={} error", type);
                return false;
        }
    }

    /**
     * 重新计算美观值等级的属性加成
     * @param humanObj
     */
    static public void updatePropCalcPower(HumanObject humanObj, int charmSn){
        PropCalc propCalc = new PropCalc();
        int power = 0;
        ConfCharmLevel confCharmLevel = ConfCharmLevel.get(charmSn);
        if(confCharmLevel == null || confCharmLevel.attr == null || confCharmLevel.attr.length < 1){
            return;
        }
        propCalc.plus(confCharmLevel.attr);
        power += confCharmLevel.power;
        // 勋章属性
        Charm charm = humanObj.operation.charmData.charm;
        List<Integer> unlockMedalList = Utils.strToIntList(charm.getUnlockMedalList());
        for(int medalSn : unlockMedalList){
            ConfMedal confMedal = ConfMedal.get(medalSn);
            if(confMedal == null || confMedal.attr == null || confMedal.attr.length < 1){
                continue;
            }
            propCalc.plus(confMedal.attr);
            power += confMedal.power;
        }
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.charm, propCalc.toJSONStr());
        HumanManager.inst().updatePowerPar(humanObj, EModule.charm, power);
        PropManager.inst().propCalc(humanObj, CombatChangeLog.美观值);
        Log.charm.info("===更新美观值属性加成，propCalc={} power={} humanId={}", propCalc.toJSONStr(), power, humanObj.id);
    }

    @Listener(EventKey.HUMAN_LOGIN_FINISH)
    public void _on_HUMAN_LOGIN_FINISH(Param param) {
        HumanObject humanObj = param.get("humanObj");
        // 重新计算美观值
        reCalcCharmValue(humanObj, false);
        // 发送美观值信息
        sendCharmInfo(humanObj);
    }
}
