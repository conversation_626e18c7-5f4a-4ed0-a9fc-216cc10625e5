package org.gof.demo.worldsrv.item;

/**
 * 物品常量类
 * <AUTHOR>
 *
 */
public class ItemConstants {

	/* Type */
	public static final int 货币 = 0;
	public static final int 道具 = 1;
	public static final int 装备 = 2;
	public static final int 技能 = 3;
	public static final int 同伴 = 4;
	public static final int 种子 = 9;
	public static final int 肥料 = 10;
	public static final int 武魂 = 12;
	public static final int 礼包 = 13;
	public static final int 外观 = 15;
	public static final int 称号 = 17;
	public static final int 战令值 = 18;
	public static final int 头像框 = 19;
	public static final int 自选武魂 = 20;
	public static final int 战令 = 22;
	public static final int 气泡 = 25;
	public static final int 符石 = 26;
	public static final int 坐骑皮肤 = 27;
	public static final int 神器皮肤 = 28;
	public static final int 鲜花 = 30;
	public static final int 背饰皮肤 = 32;
	public static final int 附魔强化石 = 37;
	public static final int 头像 = 39;
	public static final int 飞宠材料 = 40;
	public static final int 飞宠蛋 = 41;
	public static final int 飞宠 = 42;
	public static final int 星将 = 46;
	public static final int 自选礼包 = 47;
	public static final int 随机礼包 = 48;	// 和type=13的礼包的区别是不自动开，要玩家手动开

	public static final int 表情包 = 50;
	public static final int 活动可用道具 = 51;// 道具表配置活动类型，具体道具效果在活动Control里面实现
	public static final int 活动道具 = 52;// 道具表配置活动类型，具体道具效果在活动Control里面实现
	public static final int 合成碎片 = 53;// 一次性使用该道具，如果数量足够，直接给新的道具
	public static final int 鱼饵 = 54;
	public static final int 鱼 = 55;


	/* subtype子类型 */

	/* 道具子类型 */
	public static final int IT_道具_钥匙 = 0;	// 需要消耗的道具的礼包

	/* 刷新道具的sn */
	public static final int AUTO_FARM_SEED = 101;
	public static final int AUTO_FARM_FERTILIZER = 112;
	public static final int AUTO_FARM_FERTILIZER_HELP = 113;
	public static final int AUTO_FARM_FAVORABILITY = 1106;
	public static final int AUTO_MINE = 4001;
	/* 道具的sn */
	public static final int goods_菇车币 = 201;
	public static final int goods_菇车经验 = 202;
	public static final int goods_挑战券 = 1006;
	public static final int goods_矿石 = 1007;
	public static final int goods_勇者试炼钥匙 = 1081;
	public static final int goods_邪眼改造 = 1020;
	public static final int goods_飞宠蛋糕 = 1163;
	public static final int goods_魔王军体力丹 = 1800;
	public static final int goods_史莱姆体力值 = 1814;

	/* 刷新道具的类型 */
	public static final int AUTO_TYPE_DAILY = 1;
	public static final int AUTO_TYPE_TIMER = 2;
	public static final int AUTO_TYPE_WEEK = 3;// 每周恢复



}