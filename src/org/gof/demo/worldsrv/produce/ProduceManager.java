package org.gof.demo.worldsrv.produce;

import org.apache.commons.collections.map.HashedMap;
import org.gof.core.support.*;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.angel.AngelManager;
import org.gof.demo.worldsrv.appearance.AppearanceManager;
import org.gof.demo.worldsrv.appearance.Personality;
import org.gof.demo.worldsrv.artifact.ArtifactGemVo;
import org.gof.demo.worldsrv.artifact.ArtifactManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.equip.EquipManager;
import org.gof.demo.worldsrv.fate.FateManager;
import org.gof.demo.worldsrv.fate.FateVo;
import org.gof.demo.worldsrv.flyPet.FlyPetManager;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.home.Fish.HomeFishManager;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.MoneyManager;
import org.gof.demo.worldsrv.human.RoleInfoKey;
import org.gof.demo.worldsrv.item.ItemConstants;
import org.gof.demo.worldsrv.item.ItemData;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.mall.MallManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.TokenItemType;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;

import java.math.BigDecimal;
import java.util.*;

public class ProduceManager extends ManagerBase {

	public static int RANDOMTOTAL = 10000; //随机使用的最大数量
	public static int probType1 = 1; //掉落类型1，权重类型
	public static int probType2 = 2; //掉落类型2，纯概率万分比
	// 这些变化要通知客户端role_resource_change_s2c
	public static List<Integer> snList = new ArrayList<>(Arrays.asList(1, 2, 7, 9, 10, 11,12,51,52,101,102,103,104,111,112,113,999,201,202,203));

	public static ProduceManager inst() {
		return inst(ProduceManager.class);
	}

	public void produceAdd(HumanObject humanObj, int itemSn, int itemNum, MoneyItemLogKey log, Object... obj) {
		rewardProduceMap(humanObj, itemSn, itemNum, log, obj);
	}


	public void produceAdd(HumanObject humanObj, Map<Integer, Integer> itemNumMap, MoneyItemLogKey log, Object... obj) {
		rewardProduceMap(humanObj, itemNumMap, log, obj);
	}

	public void produceAdd(HumanObject humanObj, int[] itemNumArr, MoneyItemLogKey log, Object... obj) {
		if(itemNumArr == null || itemNumArr.length % 2 != 0){
			Log.temp.error("===humanId={}, 奖励sn和数量个数不对等");
			return;
		}
		rewardProduceMap(humanObj, Utils.intArrToIntMap(itemNumArr), log, obj);
	}

	public void produceAdd(HumanObject humanObj, int[][] itemNumArr, MoneyItemLogKey log, Object... obj) {
		produceAdd(humanObj, itemNumArr, 1, log, obj);
	}

	public void produceAdd(HumanObject humanObj, int[][] itemNumArr, double multiple, MoneyItemLogKey log, Object... obj) {
		if(itemNumArr == null){
			return;
		}
		int ml = itemNumArr.length;
		Map<Integer, Integer> map = new HashMap<>();
		for(int m = 0; m < ml; m++) {
			int nl = itemNumArr[m].length;
			if (nl % 2 != 0) {
				Log.temp.error("===配表错误,humanId={}, log={}", humanObj.id, log);
				continue;
			}
			for (int n = 0; n < nl; n += 2) {
				int itemSn = itemNumArr[m][n];
				int itemNum = Utils.intValue(itemNumArr[m][n + 1] * multiple);
				int num = Utils.intValue(map.get(itemSn)) + itemNum;
				map.put(itemSn, num);
			}
		}
		rewardProduceMap(humanObj, map, log, obj);
	}

	public ReasonResult canCostProduce(HumanObject humanObj, int itemSn, long itemNum) {
		if(itemNum <= 0){
			return new ReasonResult();
		}
		if(TokenItemType.isMoney(itemSn)){
			return MoneyManager.inst().canProduceReduce(humanObj, itemSn, itemNum);
		}
		Map<Integer, Map<Integer, Integer>> classifySnNumMap = humanObj.operation.itemData.getTypeSnNumDataMap();
		ConfGoods confItem = ConfGoods.get(itemSn);
		if(confItem == null){
			Log.temp.error("===ConfItem配表错误， sn={}, num={}", itemSn, itemNum);
			return new ReasonResult();
		}
		Map<Integer, Integer> snNumMap = classifySnNumMap.get(confItem.classify);
		if(snNumMap == null || !snNumMap.containsKey(itemSn)){
			return new ReasonResult();
		}
		int sunNum = snNumMap.get(itemSn);
		if (itemNum > sunNum) {
			if (TokenItemType.isPushGiftItem(itemSn)) {
				// 推送礼包
				MallManager.inst().pushLimitGift26Shortage(humanObj, itemSn);
			}
			return new ReasonResult();
		}
		return new ReasonResult(true);
	}

	public long getCostProduce(HumanObject humanObj, int itemSn) {
		if(TokenItemType.isMoney(itemSn)){
			return MoneyManager.inst().getProduceReduce(humanObj, itemSn);
		}
		Map<Integer, Map<Integer, Integer>> classifySnNumMap = humanObj.operation.itemData.getTypeSnNumDataMap();
		ConfGoods confItem = ConfGoods.get(itemSn);
		if(confItem == null){
			Log.temp.error("===ConfItem配表错误， sn={}", itemSn);
			return 0;
		}
		Map<Integer, Integer> snNumMap = classifySnNumMap.get(confItem.classify);
		if(snNumMap == null || !snNumMap.containsKey(itemSn)){
			return 0;
		}
		return snNumMap.get(itemSn);
	}

	public void produceAll(HumanObject humanObj, Map<Integer, Integer> allMap, MoneyItemLogKey log, Object... obj) {
		if(allMap == null || allMap.isEmpty()){
			Log.temp.info("===humanId={}, allMap={}, log={}", humanObj.id, allMap, log);
			return;
		}
		rewardProduceMap(humanObj, allMap, log, obj);
	}

	public ReasonResult canCostIntArr(HumanObject humanObj, int[][] costs) {
		for (int i = 0; i < costs.length; i++) {
			ReasonResult rr = canCostIntArr(humanObj, costs[i]);
			if (!rr.success) {
				return rr;
			}
		}
		return new ReasonResult(true);
	}


	/**
	 * 判断能不能扣除
	 * <AUTHOR>
	 * @Date 2024/3/8
	 * @Param
	 */
	public ReasonResult canCostIntArr(HumanObject humanObj, int[] intArr){
		return canCostIntArr(humanObj, intArr, 1);
	}

	public ReasonResult canCostIntArr(HumanObject humanObj, int[] intArr, int multiple){
		if(intArr == null){
			return new ReasonResult(false);
		}
		if(intArr.length % 2 != 0){
			Log.temp.error("===数组不等，intArr={}", Utils.intArrToList(intArr));
			return new ReasonResult(false);
		}
		for(int i = 0; i < intArr.length; i+=2){
			int sn = intArr[i];
			int num = intArr[i+1] * multiple;
			if(TokenItemType.isMoney(sn)){
				ReasonResult result = MoneyManager.inst().canProduceReduce(humanObj, sn, num);
				if(!result.success){
					return result;
				}
			} else {
				ReasonResult result = humanObj.operation.itemData.canCostItemSnNum(sn, num);
				if(!result.success){
					return result;
				}
			}
		}
		return new ReasonResult(true);
	}

	public ReasonResult costIntArr(HumanObject humanObj, int[][] costs, MoneyItemLogKey log) {
		for (int i = 0; i < costs.length; i++) {
			ReasonResult rr = costIntArr(humanObj, costs[i], log);
			if (!rr.success) {
				return rr;
			}
		}
		return new ReasonResult(true);
	}

	public ReasonResult costIntArr(HumanObject humanObj, int[] intArr, MoneyItemLogKey log){
		return costIntArr(humanObj,intArr, 1, log);
	}

	public ReasonResult costIntArr(HumanObject humanObj, int[] intArr, int multiple, MoneyItemLogKey log){
		if(intArr == null){
			return new ReasonResult(false);
		}
		if(intArr.length % 2 != 0){
			Log.temp.error("===数组不等，intArr={}", Utils.intArrToList(intArr));
			return new ReasonResult(false);
		}
		Define.p_role_change.Builder dRole = Define.p_role_change.newBuilder();
		Define.p_role_change.Builder dRoleChange = Define.p_role_change.newBuilder();
		List<Define.p_goods> dInfoList = new ArrayList<>();
		for(int i = 0; i < intArr.length; i+=2){
			int sn = intArr[i];
			int num = intArr[i+1] * multiple;
			if(TokenItemType.isMoney(sn)){
				MoneyManager.inst().produceMoneyReduce(humanObj, sn, num, log);
				long value = MoneyManager.inst().getProduceReduce(humanObj, sn);
				if(TokenItemType.isLimit(sn)){
					Define.p_key_value.Builder dInfo = HumanManager.inst().to_p_key_value(sn, value);
					dRole.addKv(dInfo);
					if(!RoleInfoKey.resIdList.contains(RoleInfoKey.getByKey(sn))){
						dRoleChange.addKv(dInfo);
					}
				} else {
					dInfoList.add(HumanManager.inst().to_p_goods(sn, (int)value, 0));
				}
			} else {
				humanObj.operation.itemData.costItemSnNum(humanObj, sn, num, log);
				if(TokenItemType.isLimit(sn)){
					Define.p_key_value.Builder dInfo = HumanManager.inst().to_p_key_value(sn, humanObj.operation.itemData.getItemNum(sn));
					dRole.addKv(dInfo);
					if(!RoleInfoKey.resIdList.contains(RoleInfoKey.getByKey(sn))){
						dRoleChange.addKv(dInfo);
					}
					continue;
				}
				dInfoList.add(HumanManager.inst().to_p_goods(sn, humanObj.operation.itemData.getItemNum(sn), 0));
			}
		}
		if(dRoleChange.getKvCount() > 0 || dRoleChange.getKsCount() > 0){
			HumanManager.inst().sendMsg_role_info_change_s2c(humanObj, dRoleChange.build());
		}
		if(dRole.getKvCount() > 0 || dRole.getKsCount() > 0){
			HumanManager.inst().sendMsg_role_resource_change_s2c(humanObj, dRole);
		}
		if(!dInfoList.isEmpty()){
			HumanManager.inst().sendMsg_goods_change_s2c(humanObj, dInfoList);
		}
		return new ReasonResult(true);
	}

	public ReasonResult costItem(HumanObject humanObj, int costSn, long costNum, MoneyItemLogKey log){
		Define.p_role_change.Builder dRole = Define.p_role_change.newBuilder();
		Define.p_role_change.Builder dRoleChange = Define.p_role_change.newBuilder();
		List<Define.p_goods> dInfoList = new ArrayList<>();
		if(TokenItemType.isMoney(costSn)){
			MoneyManager.inst().produceMoneyReduce(humanObj, costSn, costNum, log);
			long value = MoneyManager.inst().getProduceReduce(humanObj, costSn);
			if(TokenItemType.isLimit(costSn)){
				Define.p_key_value.Builder dInfo = HumanManager.inst().to_p_key_value(costSn, value);
				dRole.addKv(dInfo);
				if(!RoleInfoKey.resIdList.contains(RoleInfoKey.getByKey(costSn))){
					dRoleChange.addKv(dInfo);
				}
			} else {
				dInfoList.add(HumanManager.inst().to_p_goods(costSn, (int)value, 0));
			}
		} else {
			if(costNum >= Integer.MAX_VALUE){
				return new ReasonResult(false);
			}
			humanObj.operation.itemData.costItemSnNum(humanObj, costSn, Utils.intValue(costNum), log);
			if(TokenItemType.isLimit(costSn)){
				Define.p_key_value.Builder dInfo = HumanManager.inst().to_p_key_value(costSn, humanObj.operation.itemData.getItemNum(costSn));
				dRole.addKv(dInfo);
				if(!RoleInfoKey.resIdList.contains(RoleInfoKey.getByKey(costSn))){
					dRoleChange.addKv(dInfo);
				}
			} else {
				dInfoList.add(HumanManager.inst().to_p_goods(costSn, humanObj.operation.itemData.getItemNum(costSn), 0));
			}
		}
		if(dRoleChange.getKvCount() > 0 || dRoleChange.getKsCount() > 0){
			HumanManager.inst().sendMsg_role_info_change_s2c(humanObj, dRoleChange.build());
		}
		if(dRole.getKvCount() > 0 || dRole.getKsCount() > 0){
			HumanManager.inst().sendMsg_role_resource_change_s2c(humanObj, dRole);
		}
		if(!dInfoList.isEmpty()){
			HumanManager.inst().sendMsg_goods_change_s2c(humanObj, dInfoList);
		}
		return new ReasonResult(true);
	}

	/**
	 * 检测物品能否通过消耗对应价格，并且直接消耗对应价格（例如直接用月钻抽卡）
	 */
	public ReasonResult checkAndCostItemPrice(HumanObject humanObj, int itemSn, int itemNum, MoneyItemLogKey log) {
		ConfGoods confGoods = ConfGoods.get(itemSn);
		if (confGoods == null) {
			return new ReasonResult(false);
		}
		int num = ItemManager.inst().getItemNum(humanObj, itemSn);
		int allNum = confGoods.price[1] * (itemNum - num);
		int[][] costs;
		if (num == 0) {
			costs = new int[][] {
					new int[] { confGoods.price[0], allNum }
			};
		} else {
			costs = new int[][] {
					new int[] { itemSn, num },
					new int[] { confGoods.price[0], allNum }
			};
		}
		return ProduceManager.inst().checkAndCostItem(humanObj, costs, log);
	}

	public ReasonResult checkAndCostItem(HumanObject humanObj, int costSn, int costNum, MoneyItemLogKey log){
		ReasonResult result = canCostProduce(humanObj, costSn, costNum);
		if(!result.success){
			return result;
		}
		return costItem(humanObj, costSn, costNum, log);
	}

	public ReasonResult checkAndCostItem(HumanObject humanObj, int[][] itemNumArr, int multiple, MoneyItemLogKey log){
		if (itemNumArr == null || itemNumArr.length == 0) {
			return new ReasonResult(false);
		}
		int[][] result = new int[itemNumArr.length][2];
		for (int i = 0; i < itemNumArr.length; i++) {
			// 复制物品ID
			result[i][0] = itemNumArr[i][0];
			// 数量乘以倍数
			result[i][1] = itemNumArr[i][1] * multiple;
		}
		return checkAndCostItem(humanObj, result, log);
	}

	public ReasonResult checkAndCostItem(HumanObject humanObj, int[][] itemNumArr, MoneyItemLogKey log){
		if(itemNumArr == null){
			return new ReasonResult(false);
		}
		ItemData itemData = humanObj.operation.itemData;
		int ml = itemNumArr.length;
		for(int m = 0; m < ml; m++) {
			int nl = itemNumArr[m].length;
			if (nl % 2 != 0) {
				Log.temp.error("===配表错误,humanId={}, log={}", humanObj.id, log);
				// TODO continue;
			}
			for (int n = 0; n < nl; n += 2) {
				int itemSn = itemNumArr[m][n];
				int itemNum = itemNumArr[m][n + 1];
				if (TokenItemType.isMoney(itemSn)) {
					ReasonResult result = MoneyManager.inst().canProduceReduce(humanObj, itemSn, itemNum);
					if (!result.success) {
						return result;
					}
				} else {
					ReasonResult result = itemData.canCostItemSnNum(itemSn, itemNum);
					if (!result.success) {
						return result;
					}
				}
			}
		}

		Define.p_role_change.Builder dRole = Define.p_role_change.newBuilder();
		Define.p_role_change.Builder dRoleChange = Define.p_role_change.newBuilder();
		List<Define.p_goods> dInfoList = new ArrayList<>();
		for(int m = 0; m < ml; m++) {
			int nl = itemNumArr[m].length;
			for (int n = 0; n < nl; n += 2) {
				int itemSn = itemNumArr[m][n];
				int itemNum = itemNumArr[m][n + 1];
				if (TokenItemType.isMoney(itemSn)) {
					MoneyManager.inst().produceMoneyReduce(humanObj, itemSn, itemNum, log);
					long value = MoneyManager.inst().getProduceReduce(humanObj, itemSn);
					if(TokenItemType.isLimit(itemSn)){
						Define.p_key_value.Builder dInfo =HumanManager.inst().to_p_key_value(itemSn, value);
						dRole.addKv(dInfo);
						if(!RoleInfoKey.resIdList.contains(RoleInfoKey.getByKey(itemSn))){
							dRoleChange.addKv(dInfo);
						}
					} else {
						dInfoList.add(HumanManager.inst().to_p_goods(itemSn, (int)value, 0));
					}
				} else {
					itemData.costItemSnNum(humanObj, itemSn, itemNum, log);
					if(TokenItemType.isLimit(itemSn)){
						Define.p_key_value.Builder dInfo =HumanManager.inst().to_p_key_value(itemSn, humanObj.operation.itemData.getItemNum(itemSn));
						dRole.addKv(dInfo);
						if(!RoleInfoKey.resIdList.contains(RoleInfoKey.getByKey(itemSn))){
							dRoleChange.addKv(dInfo);
						}
						continue;
					}
					dInfoList.add(HumanManager.inst().to_p_goods(itemSn, humanObj.operation.itemData.getItemNum(itemSn), 0));
				}
			}
		}
		if(dRoleChange.getKvCount() > 0 || dRoleChange.getKsCount() > 0){
			HumanManager.inst().sendMsg_role_info_change_s2c(humanObj, dRoleChange.build());
		}

		if(dRole.getKvCount() > 0 || dRole.getKsCount() > 0){
			HumanManager.inst().sendMsg_role_resource_change_s2c(humanObj, dRole);
		}
		if(!dInfoList.isEmpty()){
			HumanManager.inst().sendMsg_goods_change_s2c(humanObj, dInfoList);
		}
		return new ReasonResult(true);
	}

	public Map<Integer, Integer> getDropMap(int groupId) {
		return getDropMap(groupId, new ArrayList<>(), true);
	}

	public Map<Integer, Integer> getDropMap(int groupId, List<Define.p_reward> awardSnList, boolean isClear) {
		return getDropMap(groupId, awardSnList, isClear, 1);
	}
	/** 
	 * 根据掉落组id, 获取掉落map集合
	 * <AUTHOR>
	 * @Date 2024/3/13
	 * @Param 
	 */
	public Map<Integer, Integer> getDropMap(int groupId, List<Define.p_reward> awardSnList, boolean isClear, int loopNum){
		loopNum++;
		if(loopNum > 10){
			Log.temp.error("===循环次数过多，groupId={}, awardSnList={}, loopNum={}", groupId, awardSnList, loopNum);
			return new HashedMap();
		}
		Map<Integer, Integer> dropMap = new HashedMap();
		int maxNum = GlobalConfVal.getConfOutPutMaxNum(groupId);
		List<Integer> snList = new ArrayList<>();
		List<Integer> rageList = new ArrayList<>();
		for(int i = 1; i <= maxNum; i++){
			ConfOutput conf = ConfOutput.get(groupId * 1000 + i);
			if(conf == null){
				continue;
			}
			if(conf.probtype == probType1){
				snList.add(conf.sn);
				rageList.add(conf.prob);
			} else if(conf.probtype == probType2){
				if(Utils.isRandomInclude(conf.prob, RANDOMTOTAL)){
					if(conf.item != null){
						dropMap = Utils.intArrToIntMap(dropMap, conf.item);
						if(!isClear){
							awardSnList.addAll(to_p_rewardList(conf.item));
						}
					}
					// 额外掉落组
					if(conf.othergroup != null){
						for(int m = 0; m < conf.othergroup.length; m+=2){
							int otherGroupId = conf.othergroup[m];
							int otherGroupNum = conf.othergroup[m+1];
							for(int n = 0; n < otherGroupNum; n++){
								Map<Integer, Integer> otherDropMap = getDropMap(otherGroupId, awardSnList, isClear, loopNum);
								dropMap = Utils.mergeMap(dropMap, otherDropMap);
							}
						}
					}
				}
			} else {
				Log.temp.error("===未实现对应代码，ConfOutput配表错误，sn={}, probtype={}", conf.sn, conf.probtype);
			}
		}
		if(!rageList.isEmpty()){
			int index = Utils.getRandRange(rageList);
			if(index >= 0 && index < snList.size()){
				ConfOutput confOutput = ConfOutput.get(snList.get(index));
				if(confOutput == null){
					Log.temp.error("===ConfOutput配表错误, sn={}, index={}", snList.get(index), index);
					return dropMap;
				}
				if(confOutput.item != null){
					dropMap = Utils.intArrToIntMap(dropMap, confOutput.item);
					if(!isClear){
						awardSnList.addAll(to_p_rewardList(confOutput.item));
					}
				}
				if(confOutput.othergroup != null){
					for(int m = 0; m < confOutput.othergroup.length; m+=2){
						int otherGroupId = confOutput.othergroup[m];
						int otherGroupNum = confOutput.othergroup[m+1];
						for(int n = 0; n < otherGroupNum; n++){
							Map<Integer, Integer> otherDropMap = getDropMap(otherGroupId, awardSnList, isClear);
							dropMap = Utils.mergeMap(dropMap, otherDropMap);
						}
					}
				}
				return dropMap;
			}
		}
		return dropMap;
	}

	public List<Define.p_reward> to_p_rewardList(int[] item){
		List<Define.p_reward> rewardList = new ArrayList<>();
		if(item == null || item.length % 2 != 0){
			return rewardList;
		}
		for(int i = 0; i < item.length; i+=2){
			rewardList.add(HumanManager.inst().to_p_reward(item[i], item[i+1]));
		}
		return rewardList;
	}

	public List<Define.p_reward> to_p_rewardList(int[][] item){
		List<Define.p_reward> rewardList = new ArrayList<>();
		if(item == null){
			return rewardList;
		}
		for(int i = 0; i < item.length; i++){
			rewardList.add(HumanManager.inst().to_p_reward(item[i][0], item[i][1]));
		}
		return rewardList;
	}

	public List<Define.p_reward> to_p_rewardList(Map<Integer,Integer> rewardMap){
		List<Define.p_reward> rewardList = new ArrayList<>();
		for(Map.Entry<Integer, Integer> entry : rewardMap.entrySet()){
			rewardList.add(HumanManager.inst().to_p_reward(entry.getKey(), entry.getValue()));
		}
		return rewardList;
	}


	/**
	 * 给掉落包
	 * <AUTHOR>
	 * @Date 2024/4/11
	 * @Param 
	 */
	public Map<Integer, Integer> produceAddDrop(HumanObject humanObj, int[][] dropNumArr, MoneyItemLogKey log, Object... obj) {
		Map<Integer, Integer> dropAllMap = producePreDrop(dropNumArr);
		rewardProduceMap(humanObj, dropAllMap, log, obj);
		return dropAllMap;
	}

	/**
	 * 产出前掉落
	 * <AUTHOR>
	 * @Date 2024/5/30
	 * @Param
	 */
	public Map<Integer, Integer> producePreDrop(int[][] dropNumArr) {
		Map<Integer, Integer> dropAllMap = new HashMap<>();
		if (dropNumArr == null) {
			return dropAllMap;
		}
		int ml = dropNumArr.length;
		for (int m = 0; m < ml; m++) {
			int nl = dropNumArr[m].length;
			if (nl % 2 != 0) {
				Log.temp.error("producePreDrop===配表错误, dropNumArr={}", Utils.arrayIntToStr(dropNumArr[m]));
				continue;
			}
			for (int n = 0; n < nl; n+=2)  {
				int dropSn = dropNumArr[m][n];
				int dropNum = dropNumArr[m][n+1];
				for (int i = 0; i < dropNum; i++) {
					dropAllMap = Utils.mergeMap(dropAllMap, ProduceManager.inst().getDropMap(dropSn));
				}
			}
		}
		return dropAllMap;
	}

	/**
	 * 掉落包转具体物品sn和数量
	 * @param dropNumMap
	 * @return
	 */
	public Map<Integer, Integer> producePreDrop(Map<Integer, Integer> dropNumMap) {
		Map<Integer, Integer> dropAllMap = new HashMap<>();
		for(Map.Entry<Integer, Integer> entry : dropNumMap.entrySet()){
			int dropSn = entry.getKey();
			int dropNum = entry.getValue();
			for (int i = 0; i < dropNum; i++) {
				dropAllMap = Utils.mergeMap(dropAllMap, ProduceManager.inst().getDropMap(dropSn));
			}
		}
		return dropAllMap;
	}

	public Map<Integer, List<ProduceVo>> getProduceVoAll(HumanObject humanObj, int[][] dropNumArr, MoneyItemLogKey log, Object... obj){
		Map<Integer, Integer> dropAllMap = producePreDrop(dropNumArr);
		return getProduceVoAll(humanObj, dropAllMap, log, obj);
	}

	/** 
	 * 掉落获取最终具体事物
	 * <AUTHOR>
	 * @Date 2024/6/3
	 * @Param return 出现报错就把整个produceVoAllMap=null
	 */
	public Map<Integer, List<ProduceVo>> getProduceVoAll(HumanObject humanObj, Map<Integer, Integer> itemNumMap, MoneyItemLogKey log, Object... obj) {
		Map<Integer, List<ProduceVo>> produceVoAllMap = new HashMap<>();
		for (Map.Entry<Integer, Integer> entry : itemNumMap.entrySet()) {
			int itemSn = entry.getKey();
			int itemNum = entry.getValue();
			Map<Integer, List<ProduceVo>> voMapNew = getProduceVoAll(humanObj, itemSn, itemNum, log, obj);
			if (voMapNew == null) {
				return null;// 先有问题就直接全null;
			}
			for (Map.Entry<Integer, List<ProduceVo>> entryVo : voMapNew.entrySet()) {
				int type = entryVo.getKey();
				List<ProduceVo> voListAll = produceVoAllMap.computeIfAbsent(type, k -> new ArrayList<>());
				voListAll.addAll(entryVo.getValue());
			}
		}
		return produceVoAllMap;
	}

	private Map<Integer, List<ProduceVo>> getProduceVoAll(HumanObject humanObj, int itemSn, int itemNum, MoneyItemLogKey log, Object... obj){
		Map<Integer, List<ProduceVo>> produceVoAllMap = new HashMap<>();
		if (itemSn <= 0 || itemNum <= 0) {
			Log.temp.error("===ConfItem配表错误， sn={}, num={}, log={}, obj={}", itemSn, itemNum, log, obj);
			return null;// 没有直接报错。
		}
		ConfGoods confItem = ConfGoods.get(itemSn);
		if (confItem == null) {
			Log.temp.error("===ConfItem配表错误， sn={}, num={}, log={}, obj={}", itemSn, itemNum, log, obj);
			return null;// 没有直接报错。
		}
		if (TokenItemType.isMoney(itemSn)) {
			ProduceVo vo = new ProduceVo(itemSn, itemNum);
			List<ProduceVo> voList = produceVoAllMap.computeIfAbsent(ItemConstants.货币, k -> new ArrayList<>());
			voList.add(vo);
			return produceVoAllMap;
		}

		switch (confItem.type) {
			case ItemConstants.武魂:
				if (!itemType_12(humanObj, produceVoAllMap, itemSn, itemNum)) {
					return null;
				}
				break;
			case ItemConstants.称号:
			case ItemConstants.头像框:
			case ItemConstants.气泡:
			case ItemConstants.头像:
				if (!itemType_17_19_25(produceVoAllMap, itemSn, confItem.effect, confItem.type)) {
					return null;
				}
				break;
			case ItemConstants.外观:
				if (!itemType_15(produceVoAllMap, itemSn, itemNum, log)) {
					return null;
				}
				break;
			case ItemConstants.符石:
				if (!itemType_26(humanObj, produceVoAllMap, confItem.quality, confItem.effect[0][0], itemNum)) {
					return null;
				}
				break;
			case ItemConstants.礼包:
				useBagType13(humanObj, produceVoAllMap, confItem, itemNum, log);
				break;
			case ItemConstants.道具:
			case ItemConstants.种子:
			case ItemConstants.肥料:
			case ItemConstants.战令:
			case ItemConstants.鲜花:
			case ItemConstants.飞宠材料:
			case ItemConstants.活动可用道具:
			case ItemConstants.鱼饵:
				itemType_1(humanObj, produceVoAllMap, itemSn, itemNum, log);
				break;
			case ItemConstants.装备:
				itemType_2(humanObj, produceVoAllMap, itemSn, itemNum, log);
				break;
			case ItemConstants.技能:
				itemType_3(humanObj, produceVoAllMap, itemSn, itemNum, log);
				break;
			case ItemConstants.同伴:
				itemType_4(humanObj, produceVoAllMap, itemSn, itemNum, log);
				break;
			case ItemConstants.战令值:
				itemType_18(humanObj, produceVoAllMap, itemSn, itemNum, log);
				break;
			case ItemConstants.飞宠蛋:
				itemType_41(humanObj, produceVoAllMap, itemSn, itemNum, log);
				break;
			case ItemConstants.飞宠:
				itemType_42(humanObj, produceVoAllMap, itemSn, itemNum, log);
				break;
			case ItemConstants.星将:
				itemType_46(humanObj, produceVoAllMap, itemSn, itemNum, log);
				break;
			case ItemConstants.活动道具:
				itemType_52(humanObj, produceVoAllMap, itemSn, itemNum, log);
				break;
			case ItemConstants.鱼:
				itemType_55(humanObj, produceVoAllMap, itemSn, itemNum, log);
				break;
			default:
				Log.temp.info("====代码未实现， 默认是道具 type={}, sn={}, num={}, log={}, produceVoAllMap={}", confItem.type, itemSn, itemNum, log, produceVoAllMap);
				itemType_1(humanObj, produceVoAllMap, itemSn, itemNum, log );
				break;
		}
		return produceVoAllMap;
	}

	private boolean itemType_1(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap,
							   int itemSn, int itemNum,  MoneyItemLogKey log){
		ProduceVo vo = new ProduceVo(itemSn, itemNum);
		vo.type = ItemConstants.道具;
		List<ProduceVo> voList = produceVoAllMap.get(ItemConstants.道具);
		if(voList == null){
			voList = new ArrayList<>();
			produceVoAllMap.put(ItemConstants.道具, voList);
		}
				voList.add(vo);
		return true;
	}

	private boolean itemType_2(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap,
							   int itemSn, int itemNum,  MoneyItemLogKey log){
		ProduceVo vo = new ProduceVo(itemSn, itemNum);
		vo.type = ItemConstants.装备;
		List<ProduceVo> voList = produceVoAllMap.get(ItemConstants.道具);

		if(voList == null){
			voList = new ArrayList<>();
			produceVoAllMap.put(ItemConstants.装备, voList);
		}
		voList.add(vo);
		return true;
	}

	private boolean itemType_3(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap,
							   int itemSn, int itemNum,  MoneyItemLogKey log){
		ProduceVo vo = new ProduceVo(itemSn, itemNum);
		vo.type = ItemConstants.技能;
		List<ProduceVo> voList = produceVoAllMap.get(ItemConstants.技能);
		if(voList == null){
			voList = new ArrayList<>();
			produceVoAllMap.put(ItemConstants.技能, voList);
		}
		voList.add(vo);
		return true;
	}

	private boolean itemType_4(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap,
							   int itemSn, int itemNum,  MoneyItemLogKey log){
		ProduceVo vo = new ProduceVo(itemSn, itemNum);
		vo.type = ItemConstants.同伴;
		List<ProduceVo> voList = produceVoAllMap.get(ItemConstants.同伴);
		if(voList == null){
			voList = new ArrayList<>();
			produceVoAllMap.put(ItemConstants.同伴, voList);
		}
		voList.add(vo);
		return true;
	}

	private boolean itemType_12(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap, int itemSn, int num) {
		ConfGoods confGoods = ConfGoods.get(itemSn);
		int[][] effect = confGoods.effect;
		if (effect == null) {
			return false;
		}
		int sn = effect[0][0];
		int lv = effect[0][1];
		ConfFate confFate = ConfFate.get(sn);
		if (confFate == null) {
			Log.temp.error("===ConfFate配表错误， not find sn={}", sn);
			return false;
		}
		List<ProduceVo> voList = produceVoAllMap.computeIfAbsent(ItemConstants.武魂, k -> new ArrayList<>());
		for (int i = 0; i < num; i++) {
			long id = humanObj.fate.genId();
			FateVo vo = new FateVo(id, sn, lv);
			ProduceVo produceVo = new ProduceVo(itemSn, 1);
			produceVo.type = ItemConstants.武魂;
			produceVo.objList.add(vo);
			voList.add(produceVo);
		}
		return true;
	}

	private void useBagType13(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap, ConfGoods confGoods,
							  int itemNum, MoneyItemLogKey log){
		int itemSn = confGoods.sn;
		if(confGoods.effect == null || confGoods.effect.length < 1){
			Log.temp.error("===ConfGoods配表错误， itemSn={}, effect={}", itemSn, confGoods.effect);
			return;
		}
		int groupId = confGoods.effect[0][0];
		if(groupId <= 0){
			Log.temp.error("===ConfGoods配表错误， itemSn={}, groupId={}", itemSn, groupId);
			return;
		}
		// 单次处理最大数量
		int maxNum = 100;
		if(itemNum > maxNum){
			Log.temp.error("===自动打开礼包数量超出{}, humanId={}, sn={},itemNum={}", maxNum, humanObj.id, itemSn, itemNum);
		}
		Map<Integer, Integer> dropMap = new HashedMap();
		for(int i = 0; i < itemNum; i++){
			dropMap = Utils.mergeMap(dropMap, ProduceManager.inst().getDropMap(groupId));
		}
		Map<Integer, List<ProduceVo>> produceVoAllMapNew = getProduceVoAll(humanObj, dropMap, log);
		for(Map.Entry<Integer, List<ProduceVo>> entry : produceVoAllMapNew.entrySet()){
			List<ProduceVo> voList = produceVoAllMap.computeIfAbsent(entry.getKey(), k -> new ArrayList<>());
			voList.addAll(entry.getValue());
		}
	}
	private boolean itemType_15(Map<Integer, List<ProduceVo>> produceVoAllMap, int itemSn, int itemNum,  MoneyItemLogKey log) {
		ProduceVo vo = new ProduceVo(itemSn, itemNum);
		vo.type = ItemConstants.外观;
		List<ProduceVo> voList = produceVoAllMap.computeIfAbsent(ItemConstants.外观, k -> new ArrayList<>());
		voList.add(vo);
		return true;
	}

	private boolean itemType_17_19_25(Map<Integer, List<ProduceVo>> produceVoAllMap, int itemSn, int[][] effect, int type){
		int sn = effect[0][0];
		int endTime = effect[1][0];
		List<ProduceVo> voList = produceVoAllMap.computeIfAbsent(type, k -> new ArrayList<>());
		Personality personality = new Personality(sn, endTime,1);
		ProduceVo vo = new ProduceVo(sn, 1, type);
		vo.goodsSn = itemSn;
		vo.objList.add(personality);
		voList.add(vo);
		return true;
	}

	private boolean itemType_18(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap,int itemSn, int itemNum,  MoneyItemLogKey log){
		ProduceVo vo = new ProduceVo(itemSn, itemNum);
		vo.type = ItemConstants.战令值;
		List<ProduceVo> voList = produceVoAllMap.get(ItemConstants.道具);

		if(voList == null){
			voList = new ArrayList<>();
			produceVoAllMap.put(ItemConstants.战令值, voList);
		}
		voList.add(vo);
		return true;
	}

	private boolean itemType_26(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap, int quality, int effect, int num) {
		if(!humanObj.isModUnlock(47)){
			return false;
		}
		List<ProduceVo> voList = produceVoAllMap.get(ItemConstants.符石);
		if(voList == null){
			voList = new ArrayList<>();
			produceVoAllMap.put(ItemConstants.符石, voList);
		}
		for(int j = 0; j < num; ++j){
			ArtifactGemVo gem = new ArtifactGemVo();
			gem.id = humanObj.artifact.genId();
			gem.quality = quality;
			gem.lv = 1;
			gem.isRed = 1;

			//随机部位,套装
			ConfArtifactGemGenerate confGen = ConfArtifactGemGenerate.get(effect);
			if(confGen == null){
				Log.game.error("神器宝石生成配置错误，effect={}", effect);
				return false;
			}
			int index = Utils.randomByWeight2D(confGen.slot_possible,1);
			gem.pos = confGen.slot_possible[index][0];
			index = Utils.randomByWeight2D(confGen.set_possible,1);
			gem.suit = confGen.set_possible[index][0];

			//属性
			ConfArtifactGemQuality confGemQuality = ConfArtifactGemQuality.get(gem.quality);
			int baseGroup = confGemQuality.mainattr_groups[gem.pos-1];
			ConfArtifactGemattr confGemattr = ArtifactManager.inst().genAttrConf(baseGroup, new ArrayList<>());
			int randAttrValue = confGemattr.initial_value.length>1?Utils.random(confGemattr.initial_value[0],confGemattr.initial_value[1]+1):confGemattr.initial_value[0];
			gem.baseAttr.plus(confGemattr.attr_id, BigDecimal.valueOf(randAttrValue));

			//随机属性
			List<Integer> excludeIds = new ArrayList<>();
			int randAttrNum = Utils.random(confGemQuality.viceattr_num[0],confGemQuality.viceattr_num[1]+1);
			for (int i = 0; i < randAttrNum; i++) {
				confGemattr = ArtifactManager.inst().genAttrConf(confGemQuality.viceattr_group, excludeIds);
				excludeIds.add(confGemattr.sn);
				randAttrValue = confGemattr.initial_value.length>1?Utils.random(confGemattr.initial_value[0],confGemattr.initial_value[1]+1):confGemattr.initial_value[0];
				gem.randAttr.plus(confGemattr.attr_id, BigDecimal.valueOf(randAttrValue));
			}
			gem.isRed = 1;
			ProduceVo vo = new ProduceVo();
			vo.num = 1;
			vo.type = ItemConstants.符石;
			vo.objList.add(gem);
			voList.add(vo);
		}
		return true;
	}

	private boolean itemType_41(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap, int itemSn, int itemNum, MoneyItemLogKey log) {
		ProduceVo vo = new ProduceVo(itemSn, itemNum);
		vo.type = ItemConstants.飞宠蛋;
		produceVoAllMap.computeIfAbsent(ItemConstants.飞宠蛋, k -> new ArrayList<>()).add(vo);
		return true;
	}

	private boolean itemType_42(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap, int itemSn, int itemNum, MoneyItemLogKey log) {
		ProduceVo vo = new ProduceVo(itemSn, itemNum);
		vo.type = ItemConstants.飞宠;
		produceVoAllMap.computeIfAbsent(ItemConstants.飞宠, k -> new ArrayList<>()).add(vo);
		return true;
	}

	private boolean itemType_46(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap, int itemSn, int itemNum, MoneyItemLogKey log) {
		ProduceVo vo = new ProduceVo(itemSn, itemNum);
		vo.type = ItemConstants.星将;
		List<ProduceVo> voList = produceVoAllMap.computeIfAbsent(ItemConstants.星将, k -> new ArrayList<>());
		voList.add(vo);
		return true;
	}

	private boolean itemType_52(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap, int itemSn, int itemNum, MoneyItemLogKey log) {
		ProduceVo vo = new ProduceVo(itemSn, itemNum);
		vo.type = ItemConstants.活动道具;
		List<ProduceVo> voList = produceVoAllMap.computeIfAbsent(ItemConstants.活动道具, k -> new ArrayList<>());
		voList.add(vo);
		return true;
	}

	private boolean itemType_55(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap, int itemSn, int itemNum, MoneyItemLogKey log) {
		ProduceVo vo = new ProduceVo(itemSn, itemNum);
		vo.type = ItemConstants.鱼;
		List<ProduceVo> voList = produceVoAllMap.computeIfAbsent(ItemConstants.鱼, k -> new ArrayList<>());
		voList.add(vo);
		return true;
	}

	public void rewardProduceVoAll(HumanObject humanObj, Map<Integer, List<ProduceVo>> produceVoAllMap, MoneyItemLogKey log, Object... obj){
		if(produceVoAllMap == null){
			return;
		}
		List<Define.p_goods> dInfoList = new ArrayList<>();
		Define.p_role_change.Builder dRole = Define.p_role_change.newBuilder();

		List<Integer> skillSnList = new ArrayList<>();
		Map<Integer, Integer> skillSnLvMap = humanObj.operation.skillData.skillSnLvMap;
		for(Map.Entry<Integer, List<ProduceVo>> entry : produceVoAllMap.entrySet()){
			int type = entry.getKey();
			List<ProduceVo> voList = entry.getValue();

			switch (type){
				case ItemConstants.货币:
					giveMoney(humanObj, voList, dInfoList, dRole, log, obj);
					break;
				case ItemConstants.装备:
					EquipManager.inst().giveEquip(humanObj, voList, log);
					break;
				case ItemConstants.道具:
					giveItem(humanObj, voList, dInfoList, dRole, log, obj);
					break;
				case ItemConstants.武魂:
					FateManager.inst().produceFate(humanObj, voList, log);
					break;
				case ItemConstants.符石:
					ArtifactManager.inst().produceGem(humanObj, voList, log);
					break;
				case ItemConstants.外观:
					EquipManager.inst().addAppearance(humanObj, voList);
					break;
				case ItemConstants.称号:
				case ItemConstants.头像框:
				case ItemConstants.气泡:
				case ItemConstants.头像:
					AppearanceManager.inst().addPersonality(humanObj, voList, log);
					break;
				case ItemConstants.技能:
					for(ProduceVo vo : voList){
						ConfGoods conf = ConfGoods.get(vo.itemSn);
						if(conf == null){
							continue;
						}
						int skillSn = conf.effect[0][0];
						if(skillSnLvMap.containsKey(skillSn) || skillSnList.contains(skillSn)){
							continue;
						}
						skillSnList.add(skillSn);
					}
					giveItem(humanObj, voList, dInfoList, dRole, log, obj);
					break;
				case ItemConstants.同伴:
					giveItem(humanObj, voList, dInfoList, dRole, log, obj);
					break;
				case ItemConstants.战令值:
					giveItem(humanObj, voList, dInfoList, dRole, log, obj);
					ActivityManager.inst().giveWartokenExp(humanObj, voList.get(0).itemSn,voList.get(0).num);
					break;
				case ItemConstants.飞宠蛋:
					FlyPetManager.inst().giveFlyEggItem(humanObj, voList);
					break;
				case ItemConstants.飞宠:
					FlyPetManager.inst().giveFlyPet(humanObj, voList);
					break;
				case ItemConstants.星将:
					AngelManager.inst().activeAngel(humanObj, voList);
					giveItem(humanObj, voList, dInfoList, dRole, log, obj);
					break;
				case ItemConstants.活动道具:
					ActivityManager.inst().itemAdd(humanObj, voList);
					break;
				case ItemConstants.鱼:
					HomeFishManager.inst().giveFish(humanObj, voList);
					break;
				default:
					Log.temp.error("====未实现代码， type={}, voList={}, log={}", type, voList, log);
					giveItem(humanObj, voList, dInfoList, dRole, log, obj);
					break;
			}
		}

		if(!dInfoList.isEmpty()){
			HumanManager.inst().sendMsg_goods_change_s2c(humanObj, dInfoList);
		}

		if(dRole.getKvCount() > 0 || dRole.getKsCount() > 0){
			// 闪钻付费打点过滤
			Define.p_role_change.Builder dRoleChange = Define.p_role_change.newBuilder();
			for(Define.p_key_value kv : dRole.getKvList()){
				if(!RoleInfoKey.resIdList.contains(RoleInfoKey.getByKey((int)kv.getK()))){
					dRoleChange.addKv(kv);
				}
			}
			if(dRoleChange.getKvCount() > 0 || dRoleChange.getKsCount() > 0){
				HumanManager.inst().sendMsg_role_info_change_s2c(humanObj, dRoleChange.build());
			}
			HumanManager.inst().sendMsg_role_resource_change_s2c(humanObj, dRole);
		}

		if(!skillSnList.isEmpty()){
			SkillManager.inst().sendMsg_skill_system_update_s2c(humanObj, SkillManager.skill_system_update_type_0, skillSnList);
		}
	}

	private void giveMoney(HumanObject humanObj, List<ProduceVo> voList, List<Define.p_goods> dInfoList,
						   Define.p_role_change.Builder dRole, MoneyItemLogKey log, Object... obj){

		for(ProduceVo vo : voList) {
			int itemSn = vo.itemSn;
			int itemNum = vo.num;
			if (TokenItemType.isMoney(itemSn)) {
				MoneyManager.inst().produceMoneyAdd(humanObj, itemSn, itemNum, log);
				long value = MoneyManager.inst().getProduceReduce(humanObj, itemSn);
				if (TokenItemType.isLimit(itemSn)) {
					dRole.addKv(HumanManager.inst().to_p_key_value(itemSn, value));
				} else {
					dInfoList.add(HumanManager.inst().to_p_goods(itemSn, (int) value, 0));
				}
			} else {
				Log.temp.error("===出问题了，给货币中夹杂其他类型的， voList={}", voList);
			}
		}
	}

	private void giveItem(HumanObject humanObj, List<ProduceVo> voList, List<Define.p_goods> dInfoList,
						  Define.p_role_change.Builder dRole, MoneyItemLogKey log, Object... obj){
		ItemData itemData = humanObj.operation.itemData;
		boolean isUpdateFunc = false;
		for(ProduceVo vo : voList){
			int itemSn = vo.itemSn;
			int itemNum = vo.num;
			ItemManager.inst().produceAdd(humanObj, itemSn, itemNum, log, obj);
			if(TokenItemType.isLimit(itemSn)){
				dRole.addKv(HumanManager.inst().to_p_key_value(itemSn, itemData.getItemNum(itemSn)));
				continue;
			}
			dInfoList.add(HumanManager.inst().to_p_goods(itemSn, itemData.getItemNum(itemSn), 0));
			if (!isUpdateFunc) {
				ConfGoods confGoods = ConfGoods.get(itemSn);
				if (confGoods.type == ItemConstants.坐骑皮肤 || confGoods.type == ItemConstants.神器皮肤
						|| confGoods.type == ItemConstants.背饰皮肤) {
					isUpdateFunc = true;
				}
			}
		}
		if (isUpdateFunc) {
			HumanManager.inst().checkFuncOpen(humanObj);
		}
	}

	public Map<Integer, List<ProduceVo>> rewardProduceMap(HumanObject humanObj, Map<Integer, Integer> itemMap, MoneyItemLogKey log, Object... obj){
		Map<Integer, List<ProduceVo>> voMap = getProduceVoAll(humanObj, itemMap, log, obj);
		rewardProduceVoAll(humanObj, voMap, log, obj);
		return voMap;
	}

	public Map<Integer, List<ProduceVo>> rewardProduceMap(HumanObject humanObj, int itemSn, int itemNum, MoneyItemLogKey log, Object... obj){
		Map<Integer, List<ProduceVo>> voMap = getProduceVoAll(humanObj, itemSn, itemNum, log, obj);
		rewardProduceVoAll(humanObj, voMap, log, obj);
		return voMap;
	}

	public List<Define.p_reward> rewardProduceToMsg(HumanObject humanObj, Map<Integer, Integer> itemMap, MoneyItemLogKey log, Object... objs) {
		Map<Integer, List<ProduceVo>> produceMap = rewardProduceMap(humanObj, itemMap, log, objs);
		List<Define.p_reward> msgList = new ArrayList<>(0);
		if (produceMap == null) {
			return msgList;
		}
		for (Map.Entry<Integer, List<ProduceVo>> entry : produceMap.entrySet()) {
			for (ProduceVo vo : entry.getValue()) {
				Define.p_reward.Builder pReward = Define.p_reward.newBuilder();
				pReward.setGtid(vo.goodsSn);
				pReward.setNum(vo.num);
				msgList.add(pReward.build());
			}
		}
		return msgList;
	}

	public List<Define.p_reward> rewardProduceToMsg(HumanObject humanObj, int[][] items, MoneyItemLogKey log, Object... objs) {
		Map<Integer, Integer> itemMap = new HashMap<>();
		for (int[] item : items) {
			itemMap.put(item[0], itemMap.getOrDefault(item[0], 0) + item[1]);
		}
		return rewardProduceToMsg(humanObj, itemMap, log, objs);
	}
}

