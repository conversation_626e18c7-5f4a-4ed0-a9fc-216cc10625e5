# 钓鱼系统优化修改说明

## 修改概述

根据需求，对钓鱼系统进行了以下优化：
1. 优化saveTotalAlbumFishes数据存储格式，减少冗余
2. 正确使用isRecord字段区分破纪录的鱼
3. 修正消息发送逻辑，只发送破纪录的鱼到album_fishes
4. 完善giveFish方法，实现道具获得鱼类的完整逻辑
5. 添加fisher_lv和fisher_exp到相关消息中

## 详细修改内容

### 1. 数据存储优化

#### FishData.java
- **saveTotalAlbumFishes()**: 
  - 原来：存储完整的Map<Integer, FishDetail>对象
  - 现在：只存储破纪录鱼的fishBaseSn列表 List<Integer>
  - 优势：大幅减少JSON数据大小，避免重复存储可通过配置表获取的数据

- **loadTotalAlbumFishes()**:
  - 支持新的数组格式加载
  - 兼容旧的Map格式（向后兼容）
  - 通过fishBaseSn从配置表重建FishDetail对象

### 2. isRecord字段正确使用

#### FishDetail.java
- 添加 `@JSONField(serialize = false)` 确保isRecord不被持久化
- isRecord只作为临时标记使用，用于区分当次操作中是否破纪录

#### HomeFishManager.java - updateAlbumData()
- 正确设置isRecord标记：
  - 第一次获得某种鱼：isRecord = true
  - 破纪录（长度更长）：isRecord = true  
  - 其他情况：isRecord = false

### 3. 消息发送逻辑修正

#### sendFishReelResponse() - 手动钓鱼
- **修改前**：发送所有albumFishesUpdate的鱼到album_fishes
- **修改后**：只发送isRecord=true的鱼到album_fishes
- **添加**：fisher_lv和fisher_exp字段

#### sendAutoFishFinResponse() - 自动钓鱼  
- **修改前**：发送所有totalAlbumMap的鱼到album_fishes
- **修改后**：只发送isRecord=true的鱼到album_fishes
- **添加**：fisher_lv和fisher_exp字段

#### 新增sendFishDetailUpdateResponse() - 道具获得
- 实现home_fish_detail_update_s2c消息发送
- 只发送破纪录的鱼

### 4. sellFishes统计逻辑修正

#### 手动钓鱼
- **修改前**：从sellFishes中减去所有albumFishesUpdate的鱼
- **修改后**：只从sellFishes中减去isRecord=true的鱼

#### 自动钓鱼
- **修改前**：从totalSellMap中减去所有totalAlbumMap的鱼
- **修改后**：由于totalAlbumMap现在只包含破纪录的鱼，逻辑正确，但添加了双重检查

### 5. totalAlbumFishes更新逻辑

#### 手动钓鱼
- 在updateAlbumData后，如果isRecord=true，将鱼添加到totalAlbumFishes

#### 自动钓鱼  
- 在updateAlbumData后，如果isRecord=true，将鱼添加到totalAlbumFishes

#### giveFish方法
- 不直接操作totalAlbumFishes，通过消息通知客户端

### 6. giveFish方法完善

#### 原来的问题
- 只调用updateAlbumData，没有后续处理
- 没有发送消息给客户端
- 没有保存数据和更新属性

#### 修改后
- 收集所有破纪录的鱼
- 发送home_fish_detail_update_s2c消息
- 调用saveAlbumDetailMap()保存数据
- 调用updatePorpCalcPower()更新属性

## 消息协议对应关系

### home_fish_reel_s2c (手动钓鱼)
- `album_fishes`: 只包含破纪录的鱼（isRecord=true）
- `sell_fishes`: 包含所有钓到的鱼，但排除破纪录的鱼
- `fisher_lv`: 钓鱼佬等级
- `fisher_exp`: 钓鱼佬经验

### home_fish_fin_auto_s2c (自动钓鱼结束)
- `album_fishes`: 只包含破纪录的鱼（isRecord=true）
- `sell_fishes`: 包含所有钓到的鱼，但排除破纪录的鱼  
- `fisher_lv`: 钓鱼佬等级
- `fisher_exp`: 钓鱼佬经验

### home_fish_detail_update_s2c (道具获得)
- `album_fishes`: 只包含破纪录的鱼（isRecord=true）

## 数据流程说明

### 破纪录判定流程
1. 调用updateAlbumData()更新图鉴数据
2. 在updateAlbumData()中设置isRecord标记
3. 根据isRecord标记决定是否：
   - 添加到totalAlbumFishes
   - 发送到客户端album_fishes
   - 从sell_fishes中排除

### 数据保存流程
1. 图鉴数据保存到albumDetailMap（完整数据）
2. 破纪录数据保存到totalAlbumFishes（会话期间）
3. saveTotalAlbumFishes()只保存破纪录鱼的fishBaseSn列表

## 优化效果

1. **存储空间减少**：totalAlbumFishes从存储完整对象改为只存储ID列表
2. **逻辑清晰**：isRecord字段明确区分破纪录和普通鱼类
3. **消息精确**：客户端只收到真正需要的破纪录鱼类信息
4. **功能完整**：giveFish方法现在能正确处理道具获得鱼类的所有逻辑

## 兼容性

- 新的loadTotalAlbumFishes()方法兼容旧的Map格式数据
- 不影响现有的图鉴数据结构
- 消息协议保持不变，只是内容更精确
