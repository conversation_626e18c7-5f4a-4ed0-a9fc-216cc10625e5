package org.gof.demo.worldsrv.home.Fish;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.config.ConfFishBase;
import org.gof.demo.worldsrv.config.ConfFishTask;
import org.gof.demo.worldsrv.entity.HomeFish;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.task.type.TaskVO;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 钓鱼数据 - 会话缓存(非持久化)
 * 整合了原FishDataStructure的功能，让代码更简洁
 */
public class FishData {
    private HomeFish homeFish;

    // ===== 持久化数据结构 =====
    /** 图鉴详情数据 */
    private Map<Integer, FishDetail> albumDetailMap = new HashMap<>();

    /** 渔具等级数据 */
    private Map<Integer, Integer> fishTools = new HashMap<>();

    /** 鱼塘槽位数据 */
    private Map<Integer, Integer> houseList = new HashMap<>();

    /** 每日任务数据 */
    private Map<Integer, TaskVO> dailyTasks = new HashMap<>();

    /** 渔场解锁任务数据 */
    private Map<Integer, TaskVO> fishGroundTasks = new HashMap<>();

    /** 鱼塘装扮数据 */
    private Map<Integer, Integer> houseDesign = new HashMap<>();

    /** 自动钓鱼鱼饵数据 */
    private List<List<Integer>> autoFishBaitsSnNum;

    /** 会话中破纪录的鱼 */
    private Map<Integer, FishDetail> totalAlbumFishes = new HashMap<>();

    /** 会话中卖出的鱼 */
    private Map<Integer, Integer> totalSellFishes = new HashMap<>();

    /** 会话中获得的总奖励 */
    private Map<Integer, Integer> totalRewards = new HashMap<Integer, Integer>();

    // ===== 会话缓存数据 (非持久化) =====
    /** 手动钓鱼待结算的渔场SN */
    private int pendingGroundSn = 0;

    /** 手动钓鱼待结算的鱼SN列表 */
    private List<Integer> pendingFishSns = new ArrayList<>();

    /** 手动钓鱼待消耗的鱼饵SN列表 */
    private int pendingBaitSn = 0;

    /** 手动钓鱼的次数 (1或10) */
    private int pendingCastNum = 0;

    /** 上次自动钓鱼结算的时间戳 */
    private long autoFishLastSettleTime = 0;

    // ===== Getter/Setter =====
    public HomeFish getHomeFish() {
        return homeFish;
    }

    public void setHomeFish(HomeFish homeFish) {
        this.homeFish = homeFish;
        // 重新加载数据结构
        loadFromHomeFish();
    }

    /**
     * 从HomeFish实体加载数据
     */
    private void loadFromHomeFish() {
        if (homeFish == null) {
            return;
        }

        // 加载图鉴详情
        loadAlbumDetailMap(homeFish.getAlbumDetailMap());

        // 加载渔具等级
        loadFishTools(homeFish.getFishTools());

        // 加载鱼塘槽位
        loadHouseList(homeFish.getHouseList());

        // 加载鱼塘装备
        loadHouseDesign(homeFish.getHouseDesign());

        // 加载每日任务
        loadDailyTasks(homeFish.getDailyTasks());

        // 加载渔场解锁任务
        loadFishGroundTasks(homeFish.getFishGroundTasks());

        // 加载自动钓鱼鱼饵
        loadAutoFishBaitsSnNum(homeFish.getAutoFishBaitsSnNum());

        // 加载会话数据
        loadTotalAlbumFishes(homeFish.getTotalAlbumFishes());
        loadTotalSellFishes(homeFish.getTotalSellFishes());
        loadTotalRewards(homeFish.getTotalRewards());
    }

    public int getPendingGroundSn() {
        return pendingGroundSn;
    }

    public void setPendingGroundSn(int pendingGroundSn) {
        this.pendingGroundSn = pendingGroundSn;
    }

    public List<Integer> getPendingFishSns() {
        return pendingFishSns;
    }

    public void setPendingFishSns(List<Integer> pendingFishSns) {
        this.pendingFishSns = pendingFishSns;
    }

    public int getPendingBaitSn() {
        return pendingBaitSn;
    }

    public void setPendingBaitSn(int pendingBaitSn) {
        this.pendingBaitSn = pendingBaitSn;
    }

    public int getPendingCastNum() {
        return pendingCastNum;
    }

    public void setPendingCastNum(int pendingCastNum) {
        this.pendingCastNum = pendingCastNum;
    }

    public long getAutoFishLastSettleTime() {
        return autoFishLastSettleTime;
    }

    public void setAutoFishLastSettleTime(long autoFishLastSettleTime) {
        this.autoFishLastSettleTime = autoFishLastSettleTime;
    }

    /**
     * 清空待结算缓存
     */
    public void clearPendingCache() {
        pendingGroundSn = 0;
        pendingFishSns.clear();
        pendingBaitSn = 0;
        pendingCastNum = 0;
    }

    // ===== 数据加载方法 =====

    private void loadAlbumDetailMap(String json) {
        albumDetailMap.clear();
        if (Utils.isEmptyJSONString(json)) return;
        JSONArray ja = Utils.toJSONArray(json);
        for(int i = 0; i < ja.size(); i++){
            JSONObject jo = ja.getJSONObject(i);
            FishDetail detail = new FishDetail();
            detail.fromJSON(jo);
            ConfFishBase conf = ConfFishBase.get(detail.getFishSn());
            if(conf == null){
                Log.fish.error("===ConfFishBase 配表错误， not find sn={}", detail.getFishSn());
                continue;
            }
            int fishGroupSn = conf.fish_group_id;
            albumDetailMap.put(fishGroupSn, detail);
        }
    }

    private void loadFishTools(String json) {
        fishTools.clear();
        if (Utils.isEmptyJSONString(json)) return;

        Map<String, Integer> map = Utils.jsonToMapStringInt(json);
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            int toolType = Utils.intValue(entry.getKey());
            fishTools.put(toolType, entry.getValue());
        }
    }

    private void loadHouseList(String json) {
        houseList.clear();
        if (Utils.isEmptyJSONString(json)) return;

        Map<String, Object> map = Utils.jsonToMap(json);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            int locationId = Utils.intValue(entry.getKey());
            int fishGroupSn = Utils.intValue(entry.getValue());
            houseList.put(locationId, fishGroupSn);
        }
    }

    private void loadDailyTasks(String json) {
        dailyTasks.clear();
        if (Utils.isEmptyJSONString(json)) return;

        JSONArray ja = Utils.toJSONArray(json);
        for(int i = 0; i < ja.size(); i++){
            TaskVO vo = new TaskVO(ja.getString(i));
            ConfFishTask conf = ConfFishTask.get(vo.taskSn);
            if(conf == null){
                Log.task.error("===ConfFishTask 配表错误， not find sn={}", vo.taskSn);
                continue;
            }
            vo.setConditionType(conf.condition[0]);
            vo.setParam1(conf.condition[1]);
            vo.setSumPlan(conf.condition[2]);
            dailyTasks.put(vo.taskSn, vo);
        }
    }

    private void loadFishGroundTasks(String json) {
        fishGroundTasks.clear();
        if (Utils.isEmptyJSONString(json)) return;

        JSONArray ja = Utils.toJSONArray(json);
        for(int i = 0; i < ja.size(); i++){
            TaskVO vo = new TaskVO(ja.getString(i));
            ConfFishTask conf = ConfFishTask.get(vo.taskSn);
            if(conf == null){
                Log.task.error("===ConfFishTask 配表错误， not find sn={}", vo.taskSn);
                continue;
            }
            vo.setConditionType(conf.condition[0]);
            vo.setParam1(conf.condition[1]);
            vo.setSumPlan(conf.condition[2]);
            fishGroundTasks.put(vo.taskSn, vo);
        }
    }

    private void loadHouseDesign(String json) {
        houseDesign.clear();
        if (Utils.isEmptyJSONString(json)) return;

        Map<String, Object> map = Utils.jsonToMap(json);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            int designSn = Utils.intValue(entry.getKey());
            int level = Utils.intValue(entry.getValue());
            houseDesign.put(designSn, level);
        }
    }

    private void loadAutoFishBaitsSnNum(String json) {
        autoFishBaitsSnNum = null;
        if (Utils.isEmptyJSONString(json)) return;

        autoFishBaitsSnNum = JSON.parseObject(
                json,
                new TypeReference<List<List<Integer>>>() {}
        );
    }

    private void loadTotalAlbumFishes(String json) {
        totalAlbumFishes.clear();
        if (Utils.isEmptyJSONString(json)) return;

        JSONArray ja = Utils.toJSONArray(json);
        for(int i = 0; i < ja.size(); i++){
            JSONObject jo = ja.getJSONObject(i);
            FishDetail detail = new FishDetail();
            detail.fromJSON(jo);
            totalAlbumFishes.put(detail.getFishSn(), detail);
        }
    }

    private void loadTotalSellFishes(String json) {
        totalSellFishes.clear();
        if (Utils.isEmptyJSONString(json)) return;

        Map<String, Object> map = Utils.jsonToMap(json);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            int fishGroupSn = Utils.intValue(entry.getKey());
            int count = Utils.intValue(entry.getValue());
            totalSellFishes.put(fishGroupSn, count);
        }
    }

    private void loadTotalRewards(String json) {
        totalRewards.clear();
        if (Utils.isEmptyJSONString(json)) return;

        Map<String, Object> map = Utils.jsonToMap(json);
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            int currencyId = Utils.intValue(entry.getKey());
            int amount = Utils.intValue(entry.getValue());
            totalRewards.put(currencyId, amount);
        }
    }

    // ===== 数据保存方法 =====

    public void saveAlbumDetailMap() {
        if (albumDetailMap.isEmpty()) {
            homeFish.setAlbumDetailMap("[]");
            return;
        }
        JSONArray ja = new JSONArray();
        for (FishDetail detail : albumDetailMap.values()) {
            ja.add(detail.toJSON());
        }
        homeFish.setAlbumDetailMap(ja.toJSONString());
    }

    public void saveFishTools() {
        if (fishTools.isEmpty()){
            homeFish.setFishTools("{}");
            return;
        }

        Map<String, Integer> map = new HashMap<>();
        for (Map.Entry<Integer, Integer> entry : fishTools.entrySet()) {
            map.put(String.valueOf(entry.getKey()), entry.getValue());
        }
        homeFish.setFishTools(Utils.mapStringIntToJSON(map));
    }

    public void saveHouseList() {
        if (houseList.isEmpty()){
            homeFish.setHouseList("{}");
            return;
        }

        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<Integer, Integer> entry : houseList.entrySet()) {
            map.put(String.valueOf(entry.getKey()), entry.getValue());
        }
        homeFish.setHouseList(Utils.mapToJSON(map));
    }

    public void saveDailyTasks() {
        if (dailyTasks.isEmpty()){
            homeFish.setDailyTasks("{}");
            return;
        }
        JSONArray ja = new JSONArray();
        for(TaskVO vo : dailyTasks.values()){
            ja.add(vo.toString());
        }
        homeFish.setDailyTasks(ja.toJSONString());
    }

    public void saveFishGroundTasks() {
        if (fishGroundTasks.isEmpty()){
            homeFish.setFishGroundTasks("{}");
        }
        JSONArray ja = new JSONArray();
        for(TaskVO vo : fishGroundTasks.values()){
            ja.add(vo.toString());
        }
        homeFish.setFishGroundTasks(ja.toJSONString());
    }

    public void saveHouseDesign() {
        if (houseDesign.isEmpty()){
            homeFish.setHouseDesign("{}");
            return;
        }

        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<Integer, Integer> entry : houseDesign.entrySet()) {
            map.put(String.valueOf(entry.getKey()), entry.getValue());
        }
        homeFish.setHouseDesign(Utils.mapToJSON(map));
    }

    public void saveAutoFishBaitsSnNum() {
        if (autoFishBaitsSnNum == null || autoFishBaitsSnNum.isEmpty()){
            homeFish.setAutoFishBaitsSnNum("[]");
            return;
        }
        homeFish.setAutoFishBaitsSnNum(JSON.toJSONString(autoFishBaitsSnNum));
    }

    public void saveTotalAlbumFishes() {
        if (totalAlbumFishes.isEmpty()){
            homeFish.setTotalAlbumFishes("[]");
            return;
        }
        JSONArray ja = new JSONArray();
        for(FishDetail detail : totalAlbumFishes.values()){
            ja.add(detail.toJSON());
        }
        homeFish.setTotalAlbumFishes(ja.toJSONString());
    }


    public Map<Integer, FishDetail> getAlbumDetailMap() {
        return albumDetailMap;
    }

    public Map<Integer, Integer> getFishTools() {
        return fishTools;
    }

    public Map<Integer, Integer> getHouseList() {
        return houseList;
    }

    public Map<Integer, TaskVO> getDailyTasks() {
        return dailyTasks;
    }

    public Map<Integer, TaskVO> getFishGroundTasks() {
        return fishGroundTasks;
    }

    public Map<Integer, Integer> getHouseDesign() {
        return houseDesign;
    }

    public List<List<Integer>> getAutoFishBaitsSnNum() {
        return autoFishBaitsSnNum;
    }

    public void setAutoFishBaitsSnNum(List<List<Integer>> autoFishBaitsSnNum) {
        this.autoFishBaitsSnNum = autoFishBaitsSnNum;
    }

    public Map<Integer, FishDetail> getTotalAlbumFishes() {
        return totalAlbumFishes;
    }

    public Map<Integer, Integer> getTotalSellFishes() {
        return totalSellFishes;
    }

    public Map<Integer, Integer> getTotalRewards() {
        return totalRewards;
    }
}
