# 顺序鱼饵消耗修复说明

## 修复的核心问题

### 原有问题
**鱼饵分配逻辑错误**：
- 我之前的实现预先收集所有轮次的鱼饵需求，然后批量扣除
- 如果某一轮的鱼饵分配失败，整个批量扣除就会失败，导致自动钓鱼中断
- 没有正确实现"每轮按顺序凑够10个鱼饵"的逻辑

### 用户的正确需求
**每轮按顺序消耗鱼饵，凑够10个**：
- 鱼饵1有15个，鱼饵2有3个，鱼饵3有6个（总共24个）
- 第1轮：10个鱼饵1
- 第2轮：5个鱼饵1 + 3个鱼饵2 + 2个鱼饵3 = 10个
- 第3轮：4个鱼饵3（余数）

## 核心修复内容

### 1. 重写鱼饵分配算法
```java
/**
 * 获取指定轮次的鱼饵消耗信息（支持多种鱼饵混合，正确处理顺序消耗）
 */
private List<BaitConsumptionInfo> getCurrentBaitInfosForRound(HomeFish homeFish, int roundOffset) {
    // 计算到当前轮次为止，总共需要消耗的鱼饵数量
    int totalNeededUpToThisRound = (alreadySettledRounds + roundOffset + 1) * multiple;
    
    // 计算到上一轮次为止，已经消耗的鱼饵数量
    int totalConsumedUpToPrevRound = (alreadySettledRounds + roundOffset) * multiple;
    
    // 本轮需要的鱼饵数量
    int needThisRound = totalNeededUpToThisRound - totalConsumedUpToPrevRound;
    
    // 计算本轮实际能消耗的鱼饵数量（可能少于needThisRound，这就是余数情况）
    int actualNeedThisRound = Math.min(needThisRound, totalAvailable - totalConsumedUpToPrevRound);
    
    // 按顺序分配鱼饵
    // ...
}
```

### 2. 正确的轮次计算逻辑
```java
// 原来的错误逻辑：基于当前轮次计算总消耗
int totalConsumed = currentRound * multiple;

// 修复后的正确逻辑：基于累积消耗计算每轮分配
int totalConsumedUpToPrevRound = (alreadySettledRounds + roundOffset) * multiple;
int totalNeededUpToThisRound = (alreadySettledRounds + roundOffset + 1) * multiple;
```

### 3. 精确的鱼饵分配算法
```java
// 按顺序分配鱼饵
int allocated = 0;
int consumedSoFar = 0;

for (List<Integer> baitInfo : baitSnNumList) {
    if (allocated >= actualNeedThisRound) break;

    int baitSn = baitInfo.get(0);
    int baitCount = baitInfo.get(1);
    
    // 检查这个鱼饵是否在当前轮次的消耗范围内
    if (consumedSoFar + baitCount > totalConsumedUpToPrevRound) {
        // 计算这个鱼饵在当前轮次可以提供多少
        int availableFromThisBait = Math.min(
            baitCount - Math.max(0, totalConsumedUpToPrevRound - consumedSoFar), // 这个鱼饵的剩余数量
            actualNeedThisRound - allocated // 当前轮次还需要的数量
        );
        
        if (availableFromThisBait > 0) {
            roundBaits.add(new BaitConsumptionInfo(baitSn, availableFromThisBait));
            allocated += availableFromThisBait;
        }
    }
    
    consumedSoFar += baitCount;
}
```

## 修复前后对比

### 用户场景：鱼饵1有15个，鱼饵2有3个，鱼饵3有6个

#### 修复前（错误）
```
预先收集所有轮次需求 → 批量扣除失败 → 自动钓鱼中断 ❌
```

#### 修复后（正确）
```
第1轮: 10个鱼饵1
第2轮: 5个鱼饵1 + 3个鱼饵2 + 2个鱼饵3 = 10个
第3轮: 4个鱼饵3（余数）
总计: 3轮，24次钓鱼 ✅
```

## 算法详解

### 关键概念
1. **totalConsumedUpToPrevRound**：到上一轮为止已经消耗的鱼饵总数
2. **totalNeededUpToThisRound**：到当前轮为止需要消耗的鱼饵总数
3. **actualNeedThisRound**：当前轮实际需要的鱼饵数量（考虑余数）

### 计算示例
**场景**：鱼饵1有15个，鱼饵2有3个，鱼饵3有6个，10连钓

#### 第1轮（roundOffset=0）
```
totalConsumedUpToPrevRound = 0 * 10 = 0
totalNeededUpToThisRound = 1 * 10 = 10
needThisRound = 10 - 0 = 10
actualNeedThisRound = min(10, 24 - 0) = 10

分配：
- 鱼饵1：consumedSoFar=0, baitCount=15
  - 0 + 15 > 0 ✓
  - availableFromThisBait = min(15 - max(0, 0-0), 10-0) = min(15, 10) = 10
  - 分配：10个鱼饵1
```

#### 第2轮（roundOffset=1）
```
totalConsumedUpToPrevRound = 1 * 10 = 10
totalNeededUpToThisRound = 2 * 10 = 20
needThisRound = 20 - 10 = 10
actualNeedThisRound = min(10, 24 - 10) = 10

分配：
- 鱼饵1：consumedSoFar=0, baitCount=15
  - 0 + 15 > 10 ✓
  - availableFromThisBait = min(15 - max(0, 10-0), 10-0) = min(5, 10) = 5
  - 分配：5个鱼饵1，allocated=5

- 鱼饵2：consumedSoFar=15, baitCount=3
  - 15 + 3 > 10 ✓
  - availableFromThisBait = min(3 - max(0, 10-15), 10-5) = min(3, 5) = 3
  - 分配：3个鱼饵2，allocated=8

- 鱼饵3：consumedSoFar=18, baitCount=6
  - 18 + 6 > 10 ✓
  - availableFromThisBait = min(6 - max(0, 10-18), 10-8) = min(6, 2) = 2
  - 分配：2个鱼饵3，allocated=10
```

#### 第3轮（roundOffset=2）
```
totalConsumedUpToPrevRound = 2 * 10 = 20
totalNeededUpToThisRound = 3 * 10 = 30
needThisRound = 30 - 20 = 10
actualNeedThisRound = min(10, 24 - 20) = 4

分配：
- 鱼饵3：consumedSoFar=18, baitCount=6
  - 18 + 6 > 20 ✓
  - availableFromThisBait = min(6 - max(0, 20-18), 4-0) = min(4, 4) = 4
  - 分配：4个鱼饵3
```

## 测试覆盖

### SequentialBaitConsumptionTest.java 测试类
1. **testSpecificScenario**：测试用户提到的具体场景
2. **testAnotherComplexScenario**：测试另一个复杂场景
3. **testExactRounds**：测试边界情况（鱼饵刚好够整数轮）

### 测试验证点
- ✅ 每轮鱼饵分配的准确性
- ✅ 多种鱼饵混合使用
- ✅ 余数轮次的正确处理
- ✅ 总投竿次数的准确性
- ✅ 结算轮次的正确性

## 性能影响

### 计算复杂度
- **时间复杂度**：O(轮次数 × 鱼饵种类数) - 与原来相同
- **空间复杂度**：O(轮次数 × 鱼饵种类数) - 与原来相同

### 实际性能
- **算法优化**：更精确的计算，减少了错误分配
- **批量处理保持**：仍然使用批量扣除鱼饵道具
- **错误率降低**：避免了因分配错误导致的自动钓鱼中断

## 关键优势

### 1. 精确性
- 每轮鱼饵分配完全按照用户期望的顺序进行
- 正确处理跨鱼饵类型的分配

### 2. 健壮性
- 不会因为某轮分配失败而导致整个自动钓鱼中断
- 正确处理各种边界情况

### 3. 用户体验
- 鱼饵消耗符合直觉：按顺序，每轮凑够10个
- 最大化利用所有鱼饵，不浪费资源

## 日志输出示例

```
具体场景测试开始，鱼饵配置: 鱼饵1=15个, 鱼饵2=3个, 鱼饵3=6个，总计24个
预期分配:
  第1轮: 10个鱼饵1
  第2轮: 5个鱼饵1 + 3个鱼饵2 + 2个鱼饵3 = 10个
  第3轮: 4个鱼饵3

第1轮实际分配:
  鱼饵SN=1, 数量=10
✅ 第1轮分配正确: 10个鱼饵1

第2轮实际分配:
  鱼饵SN=1, 数量=5
  鱼饵SN=2, 数量=3
  鱼饵SN=3, 数量=2
✅ 第2轮分配正确: 5个鱼饵1 + 3个鱼饵2 + 2个鱼饵3

第3轮实际分配:
  鱼饵SN=3, 数量=4
✅ 第3轮分配正确: 4个鱼饵3（余数）

钓鱼统计: 投竿=24, 结算轮次=3
✅ 投竿次数正确，所有24个鱼饵都被使用
✅ 结算轮次正确，共3轮
```

这个修复完全解决了用户提到的问题，实现了正确的顺序鱼饵消耗逻辑，确保每轮都能按顺序凑够指定数量的鱼饵。
