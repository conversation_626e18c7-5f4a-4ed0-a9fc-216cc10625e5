package org.gof.demo.worldsrv.home.Fish;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.pwrd.op.LogOp;
import org.gof.core.Port;
import org.gof.core.support.ManagerBase;
import org.gof.core.support.Param;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.ActivityProgressSourceModule;
import org.gof.demo.worldsrv.activity.calculator.ActivityControlWarToken;
import org.gof.demo.worldsrv.activity.calculator.IActivityControl;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.HomeFish;
import org.gof.demo.worldsrv.entity.UnitPropPlus;
import org.gof.demo.worldsrv.human.EModule;
import org.gof.demo.worldsrv.human.FuncOpenType;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgHome;
import org.gof.demo.worldsrv.msg.MsgTask;
import org.gof.demo.worldsrv.privilege.PrivilegeManager;
import org.gof.demo.worldsrv.privilege.PrivilegeType;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.produce.ProduceVo;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.task.type.ITaskTypeData;
import org.gof.demo.worldsrv.task.type.TaskTypeDataFactory;
import org.gof.demo.worldsrv.task.type.TaskVO;

import java.util.*;
import java.util.ArrayList;
import java.util.Collections;


public class HomeFishManager extends ManagerBase {

    /**
     * 获取实例
     *
     * @return
     */
    public static HomeFishManager inst() {
        return inst(HomeFishManager.class);
    }

    /**
     * 功能解锁与初始化
     * 监听全局功能开启事件
     */
    @Listener(EventKey.FUNCTION_OPEN)
    public void onFunctionOpen(Param param) {
        HumanObject humanObj = param.get("humanObj");
        if(humanObj.operation.fishData != null && humanObj.operation.fishData.getHomeFish() != null){
            return;
        }
        if(!humanObj.isModUnlock(FuncOpenType.FUNC_HOME_FISH)){
            return;
        }
        createFishData(humanObj);
    }

    /**
     * 初始化钓鱼数据
     */
    public void createFishData(HumanObject humanObj) {
        // 创建FishData实例
        if (humanObj.operation.fishData == null) {
            humanObj.operation.fishData = new FishData();
        }
        FishData fishData = humanObj.operation.fishData;
        // 创建HomeFish实体
        HomeFish homeFish = new HomeFish();
        homeFish.setId(humanObj.id);

        // 设置初始数据
        homeFish.setFisherLv(1);
        homeFish.setFisherExp(0);
        homeFish.setAlbumLv(0);
        homeFish.setAlbumExp(0);
        homeFish.setTotalScore(0);
        homeFish.setAlbumDetailMap("{}");
        homeFish.setFishTools("{}");
        homeFish.setHouseList("{}");
        homeFish.setAlbumScoreRewards("");
        homeFish.setDailyTaskGroupSn(0);
        homeFish.setDailyTasks("{}");
        homeFish.setDailyTaskRecv(false);
        homeFish.setUnlockedMaxGround(1); // 解锁默认渔场

        humanObj.operation.fishData.setHomeFish(homeFish);

        //解锁默认鱼塘装扮
        unlockDefaultHouseDesign(fishData);

        // 解锁默认鱼塘槽位
        unlockDefaultHouseSlots(fishData);

        //解锁渔具
        unlockDefaultTools(fishData);

        // 初始化下一渔场任务
        initNextGroundTasks(humanObj, fishData, ConfFishGround.get(ConfFishGround.get(1).unlock_ground));

        //初始化每日任务
        initDailyTasks(humanObj);
        
        // 保存数据
        homeFish.persist();

        Log.fish.debug("初始化钓鱼数据完成, humanId={}", humanObj.id);
    }

    private void unlockDefaultHouseDesign(FishData fishData) {
        Map<Integer, Integer> designMap = fishData.getHouseDesign();
        ConfFishConfig conf = ConfFishConfig.get(EFishType.CONF_INIT_DESIGN);
        designMap.put(conf.parameter[0][0], 1);
        fishData.saveHouseDesign();
        HomeFish homeFish = fishData.getHomeFish();
        homeFish.setCurrentHouseDesign(conf.parameter[0][0]);
    }

    /**
     * 解锁默认鱼塘槽位
     */
    private void unlockDefaultHouseSlots(FishData fishData) {
        Map<Integer, Integer> houseMap = fishData.getHouseList();

        // 获取所有鱼塘配置
        Collection<ConfFishHouse> houseConfigs = ConfFishHouse.findAll();
        for (ConfFishHouse config : houseConfigs) {
            // 如果解锁不消耗道具，就默认解锁这个槽位
            if (config.cost == null || config.cost.length == 0 ||
                (config.cost.length == 2 && config.cost[1] == 0)) {
                houseMap.put(config.sn, 0); // 0表示已解锁但未装备
            }
        }

        fishData.saveHouseList();
    }

    private void unlockDefaultTools(FishData fishData) {
        Map<Integer, Integer> toolMap = fishData.getFishTools();
        toolMap.put(EFishType.FISHING_ROD, 1);
        toolMap.put(EFishType.FISHING_LINE, 1);
        toolMap.put(EFishType.FISHING_HOOK, 1);
        fishData.saveFishTools();
    }

    private void initDailyTasks(HumanObject humanObj) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        ConfFishTaskGroup selectedGroup = selectTaskGroup(humanObj, homeFish);

        if (selectedGroup == null) {
            return;
        }

        homeFish.setDailyTaskGroupSn(selectedGroup.sn);
        homeFish.setDailyTaskRecv(false);

        Map<Integer, TaskVO> dailyTasks = humanObj.operation.fishData.getDailyTasks();
        MsgTask.task_update_s2c.Builder taskBuilder = createAndUpdateTasks(humanObj, selectedGroup, dailyTasks);

        humanObj.sendMsg(taskBuilder.build());
        humanObj.operation.fishData.saveDailyTasks();
    }

    public void resetDaily(HumanObject humanObj) {

        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            return;
        }

        FishData fishData = humanObj.operation.fishData;
        HomeFish homeFish = fishData.getHomeFish();
        ConfFishTaskGroup selectedGroup = selectTaskGroup(humanObj, homeFish);

        if (selectedGroup == null) {
            return;
        }
        humanObj.operation.fishData.getHomeFish().setDailyReelSuccCount(0);
        homeFish.setDailyTaskGroupSn(selectedGroup.sn);
        homeFish.setDailyTaskRecv(false);

        // 发送消息通知客户端任务组变化
        MsgHome.home_fish_daily_change_s2c.Builder builder = MsgHome.home_fish_daily_change_s2c.newBuilder();
        builder.setTaskGroup(selectedGroup.sn);
        builder.setDailyFishNum(0);
        humanObj.sendMsg(builder.build());

        // 更新每日任务
        Map<Integer, TaskVO> dailyTasks = fishData.getDailyTasks();
        dailyTasks.clear();
        createAndUpdateTasks(humanObj, selectedGroup, dailyTasks);

        fishData.saveDailyTasks();
    }

    /**
     * 根据解锁的地面等级选择任务组
     */
    private ConfFishTaskGroup selectTaskGroup(HumanObject humanObj, HomeFish homeFish) {
        Collection<ConfFishTaskGroup> taskGroups = ConfFishTaskGroup.findAll();
        int maxUnlockedGround = homeFish.getUnlockedMaxGround();

        List<Integer> weights = new ArrayList<>();
        List<ConfFishTaskGroup> taskGroupList = new ArrayList<>();

        for (ConfFishTaskGroup conf : taskGroups) {
            if (conf.ground_id <= maxUnlockedGround) {
                weights.add(conf.weight);
                taskGroupList.add(conf);
            }
        }

        if (taskGroupList.isEmpty()) {
            return null;
        }

        int selectedIndex = Utils.getRandRange(weights);
        ConfFishTaskGroup selectedGroup = taskGroupList.get(selectedIndex);

        if (selectedGroup == null) {
            Log.fish.error("===ConfFishTaskGroup 配表错误， not find groupId={},humanId={}",
                    selectedGroup != null ? selectedGroup.sn : "null", humanObj.id);
            return null;
        }

        return selectedGroup;
    }

    /**
     * 创建并更新任务列表
     */
    private MsgTask.task_update_s2c.Builder createAndUpdateTasks(HumanObject humanObj,
                                                                 ConfFishTaskGroup taskGroup,
                                                                 Map<Integer, TaskVO> dailyTasks) {
        MsgTask.task_update_s2c.Builder taskBuilder = MsgTask.task_update_s2c.newBuilder();

        if (taskGroup.task_list == null) {
            Log.fish.error("===ConfFishTaskGroup 配表错误， task_list为空,groupId={},humanId={}",
                    taskGroup.sn, humanObj.id);
            return taskBuilder;
        }

        for (int taskSn : taskGroup.task_list) {
            ConfFishTask confTask = ConfFishTask.get(taskSn);
            if (confTask == null) {
                Log.fish.error("===ConfFishTask 配表错误， not find sn={},humanId={}", taskSn, humanObj.id);
                continue;
            }

            TaskVO taskVO = new TaskVO(taskSn, TaskConditionTypeKey.TASK_钓鱼每日任务, confTask.condition, null, 0);
            ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(taskVO.getConditionType());
            if (idata != null) {
                idata.checkPlan(humanObj, taskVO, 0);
            }

            dailyTasks.put(taskSn, taskVO);

            // 构建协议消息
            Define.p_task.Builder pTask = Define.p_task.newBuilder();
            pTask.setTaskId(taskVO.getTaskSn());
            pTask.setState(taskVO.getStatus());
            pTask.setCount(taskVO.getPlan());
            pTask.setType(taskVO.getType());
            taskBuilder.addTaskList(pTask.build());
        }

        return taskBuilder;
    }




    /**
     * 处理钓鱼数据查询请求
     */
    public void handleFishDataC2S(HumanObject humanObj) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;

        // 构建响应消息
        MsgHome.home_fish_data_s2c.Builder builder = MsgHome.home_fish_data_s2c.newBuilder();

        // 基础信息
        builder.setFisherLv(homeFish.getFisherLv());
        builder.setFisherExp(homeFish.getFisherExp());
        builder.setTotalScore((int)homeFish.getTotalScore());
        builder.setAlbumLv(homeFish.getAlbumLv());
        builder.setAlbumExp(homeFish.getAlbumExp());
        builder.setMaxUnlockFishGround(homeFish.getUnlockedMaxGround());

        // 图鉴详情
        Map<Integer, FishDetail> albumDetailMap = fishData.getAlbumDetailMap();
        for (Map.Entry<Integer, FishDetail> entry : albumDetailMap.entrySet()) {
            FishDetail detail = entry.getValue();
            Define.p_home_fish_detail.Builder detailBuilder = Define.p_home_fish_detail.newBuilder();
            detailBuilder.setFishTypeSn(entry.getKey());
            detailBuilder.setFishSn(detail.getFishSn());
            detailBuilder.setLen(detail.getMaxLen());
            detailBuilder.setLv(detail.getLevel());
            detailBuilder.setExp(detail.getExp());
            builder.addAlbumDetail(detailBuilder.build());
        }

        // 渔具等级
        Map<Integer, Integer> fishTools = fishData.getFishTools();
        for (Map.Entry<Integer, Integer> entry : fishTools.entrySet()) {
            if (entry.getValue() > 0) {
                Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
                kvBuilder.setK(entry.getKey());
                kvBuilder.setV(entry.getValue());
                builder.addFishTool(kvBuilder.build());
            }
        }

        // 每日任务信息
        builder.setDailyTaskGroupSn(homeFish.getDailyTaskGroupSn());
        builder.setIsDailyTaskRecv(homeFish.isDailyTaskRecv());

        // 鱼塘信息
        Map<Integer, Integer> houseList = fishData.getHouseList();
        for (Map.Entry<Integer, Integer> entry : houseList.entrySet()) {
            Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
            kvBuilder.setK(entry.getKey());
            kvBuilder.setV(entry.getValue());
            builder.addHouseList(kvBuilder.build());
        }

        // 鱼塘装扮信息
        Map<Integer, Integer> houseDesign = fishData.getHouseDesign();
        for (Map.Entry<Integer, Integer> entry : houseDesign.entrySet()) {
            if (entry.getValue() > 0) { // 只发送已激活的装扮
                Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
                kvBuilder.setK(entry.getKey());
                kvBuilder.setV(entry.getValue());
                builder.addHouseDesign(kvBuilder.build());
            }
        }

        // 当前使用的装扮
        builder.setUseDesignSn(homeFish.getCurrentHouseDesign());

        // 自动钓鱼状态
        if (homeFish.getAutoFishGroundSn() > 0) {
            builder.setAutoStartTime((int)(homeFish.getAutoFishStartTime() / 1000));
            builder.setAutoGroundSn(homeFish.getAutoFishGroundSn());
            builder.setAutoMultiple(homeFish.getAutoFishCastNum());

            // 自动钓鱼鱼饵列表
            List<List<Integer>> autoFishBaitsSnNum = fishData.getAutoFishBaitsSnNum();
            if (autoFishBaitsSnNum != null) {
                for (List<Integer> baitInfo : autoFishBaitsSnNum) {
                    if (baitInfo.size() >= 2) {
                        Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
                        kvBuilder.setK(baitInfo.get(0)); // 鱼饵SN
                        kvBuilder.setV(baitInfo.get(1)); // 数量
                        builder.addBaitSnNum(kvBuilder.build());
                    }
                }
            }
        }

        // 已领取的图鉴评分奖励
        String albumScoreRewards = homeFish.getAlbumScoreRewards();
        if (!Utils.isEmptyJSONString(albumScoreRewards)) {
            String[] rewardIds = albumScoreRewards.split(",");
            for (String rewardId : rewardIds) {
                builder.addAlbumRewardedList(Utils.intValue(rewardId));
            }
        }

        builder.setDailyFishNum(homeFish.getDailyReelSuccCount());
        humanObj.sendMsg(builder.build());
    }

    /**
     * 处理领取总评分奖励请求
     */
    public void handleGetAlbumRewardC2S(HumanObject humanObj, int scoreTaskSn) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        // 检查是否已领取
        String albumScoreRewards = homeFish.getAlbumScoreRewards();
        if (albumScoreRewards.contains(String.valueOf(scoreTaskSn))) {
            Log.fish.warn("总评分奖励已领取, humanId={}, scoreTaskSn={}", humanObj.id, scoreTaskSn);
            return;
        }

        ConfFishTask conf = ConfFishTask.get(scoreTaskSn);
        if(conf.condition[2] > homeFish.getTotalScore()){
            Log.fish.error("===ConfFishTask 配表错误， not find sn={},humanId={}", scoreTaskSn, humanObj.id);
            return;
        }
        ProduceManager.inst().produceAdd(humanObj, conf.reward, MoneyItemLogKey.钓鱼图鉴奖励);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, conf.reward);

        // 记录已领取
        if (Utils.isEmptyJSONString(albumScoreRewards)) {
            albumScoreRewards = String.valueOf(scoreTaskSn);
        } else {
            albumScoreRewards += "," + scoreTaskSn;
        }
        homeFish.setAlbumScoreRewards(albumScoreRewards);

        // 响应客户端
        MsgHome.home_fish_get_album_reward_s2c.Builder builder = MsgHome.home_fish_get_album_reward_s2c.newBuilder();
        String[] rewardIds = albumScoreRewards.split(",");
        for (String rewardId : rewardIds) {
            builder.addAlbumRewardedList(Utils.intValue(rewardId));
        }
        humanObj.sendMsg(builder.build());

        Log.fish.debug("领取总评分奖励成功, humanId={}, scoreTaskSn={}", humanObj.id, scoreTaskSn);
    }

    /**
     * 处理领取每日任务奖励请求
     */
    public void handleDailyTaskRewardC2S(HumanObject humanObj) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        // 检查是否已领取
        if (homeFish.isDailyTaskRecv()) {
            Log.fish.warn("每日任务奖励已领取, humanId={}", humanObj.id);
            return;
        }

        FishData fishData = humanObj.operation.fishData;
        Map<Integer, TaskVO> dailyTasks = fishData.getDailyTasks();
        for (TaskVO task : dailyTasks.values()) {
            if (task.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成) {
                Log.fish.warn("每日任务未完成, humanId={}, taskSn={}", humanObj.id, task.getTaskSn());
                return;
            }
        }
        ConfFishTaskGroup conf = ConfFishTaskGroup.get(homeFish.getDailyTaskGroupSn());
        if(conf == null){
            Log.fish.error("===ConfFishTaskGroup 配表错误， not find groupId={},humanId={}", homeFish.getDailyTaskGroupSn(), humanObj.id);
            return;
        }
        if(conf.reward == null){
            Log.fish.error("===ConfFishTaskGroup 配表错误， reward为空,groupId={},humanId={}", homeFish.getDailyTaskGroupSn(), humanObj.id);
            return;
        }
        ProduceManager.inst().produceAdd(humanObj, conf.reward, MoneyItemLogKey.钓鱼每日任务奖励);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, conf.reward);

        // 标记已领取
        homeFish.setDailyTaskRecv(true);

        // 响应客户端
        MsgHome.home_fish_daily_task_reward_s2c.Builder builder = MsgHome.home_fish_daily_task_reward_s2c.newBuilder();
        builder.setResult(0); // 0:成功
        humanObj.sendMsg(builder.build());

        Log.fish.debug("领取每日任务奖励成功, humanId={}", humanObj.id);
    }

    /**
     * 处理渔场解锁请求
     */
    public void handleGroundUnlockC2S(HumanObject humanObj, int fishGroundSn) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;

        ConfFishGround groundConfig = ConfFishGround.get(fishGroundSn);
        if (groundConfig == null) {
            Log.fish.warn("渔场配置不存在, humanId={}, fishGroundSn={}", humanObj.id, fishGroundSn);
            return;
        }

        // 检查任务进度是否完成
        Map<Integer, TaskVO> fishGroundTasks = fishData.getFishGroundTasks();
        for (int taskSn : groundConfig.unlock) {
            ConfFishTask confTask = ConfFishTask.get(taskSn);
            if(confTask == null || confTask.condition == null || confTask.condition.length < 3){
                Log.fish.error("===ConfFishTask 配表错误， not find sn={},humanId={}", taskSn, humanObj.id);
                return;
            }
            TaskVO taskVO = fishGroundTasks.get(taskSn);
            if(taskVO == null){
                Log.fish.error("===ConfFishTask 配表错误， not find sn={},humanId={}", taskSn, humanObj.id);
                return;
            }
            if(taskVO.getPlan() < confTask.condition[2]){
                Log.fish.error("解锁渔场失败，任务未完成, humanId={}, fishGroundSn={}, taskSn={}", humanObj.id, fishGroundSn, taskSn);
                return;
            }
        }

        if(!ProduceManager.inst().checkAndCostItem(humanObj, groundConfig.unlock_cost[0], groundConfig.unlock_cost[1], MoneyItemLogKey.钓鱼渔场解锁).success){
            Log.fish.error("解锁渔场失败道具不足, humanId={}, fishGroundSn={}", humanObj.id, fishGroundSn);
            return;
        }

        // 解锁渔场
        homeFish.setUnlockedMaxGround(fishGroundSn);

        ConfFishGround nextGroundConfig = ConfFishGround.get(groundConfig.unlock_ground);
        if (nextGroundConfig != null && nextGroundConfig.unlock != null && nextGroundConfig.unlock.length > 0) {
            // 生成下一个渔场的解锁任务 - 只更新任务ID，保留任务进度
            initNextGroundTasks(humanObj, fishData, nextGroundConfig);
            MsgTask.task_update_s2c.Builder taskBuilder = MsgTask.task_update_s2c.newBuilder();

            // 添加新的解锁任务
            for (TaskVO task : fishData.getFishGroundTasks().values()) {
                Define.p_task.Builder pTask = Define.p_task.newBuilder();
                pTask.setTaskId(task.getTaskSn());
                pTask.setState(task.getStatus());
                pTask.setCount(task.getPlan());
                pTask.setType(task.getType());
                taskBuilder.addTaskList(pTask.build());
            }
            humanObj.sendMsg(taskBuilder.build());
        }

        // 响应客户端
        MsgHome.home_fish_ground_unlock_s2c.Builder builder = MsgHome.home_fish_ground_unlock_s2c.newBuilder();
        builder.setFishGroundSn(fishGroundSn);
        humanObj.sendMsg(builder.build());

        Log.fish.debug("渔场解锁成功, humanId={}, fishGroundSn={}", humanObj.id, fishGroundSn);
    }

    /**
     * 初始化下一个渔场的解锁任务 - 只切换任务ID，保留任务进度
     */
    private void initNextGroundTasks(HumanObject humanObj, FishData fishData, ConfFishGround nextGroundConfig) {
        Map<Integer, TaskVO> fishGroundTasks = fishData.getFishGroundTasks();
        fishGroundTasks.clear();
        // 遍历新渔场的解锁任务配置
        for (int newTaskSn : nextGroundConfig.unlock) {
            ConfFishTask newTaskConf = ConfFishTask.get(newTaskSn);
            if (newTaskConf == null || newTaskConf.condition == null) {
                Log.fish.error("新任务配置错误, taskSn={}, humanId={}", newTaskSn, humanObj.id);
                continue;
            }
            TaskVO taskVO = new TaskVO(newTaskConf.sn, TaskConditionTypeKey.TASK_渔场解锁, newTaskConf.condition, null, 0);
            ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(taskVO.getConditionType());
            if(idata != null){
                if(newTaskConf.condition[0] == TaskConditionTypeKey.TASK_TYPE_2020){
                    taskVO.setPlan(fishData.getHomeFish().getTotalFishCount()-1);
                    idata.checkPlan(humanObj, taskVO, 1);
                }else {
                    idata.checkPlan(humanObj, taskVO, 0);
                }

            }
            fishGroundTasks.put(newTaskSn, taskVO);
        }

        // 更新渔场任务列表
        fishData.saveFishGroundTasks();
    }

    /**
     * 处理一键装备请求
     */
    public void handleFishHouseEquipOneClickC2S(HumanObject humanObj, List<Define.p_key_value> equipList) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        FishData fishData = humanObj.operation.fishData;
        Map<Integer, Integer> houseList = fishData.getHouseList();
        Map<Integer, FishDetail> albumDetailMap = fishData.getAlbumDetailMap();

        // 验证所有装备请求
        for (Define.p_key_value equip : equipList) {
            int locationId = (int)equip.getK();
            int fishGroupSn = (int)equip.getV();

            // 检查槽位是否已解锁
            if (!houseList.containsKey(locationId)) {
                Log.fish.warn("槽位未解锁, humanId={}, locationId={}", humanObj.id, locationId);
                return;
            }

            // 检查鱼类是否拥有
            if (fishGroupSn > 0 && !albumDetailMap.containsKey(fishGroupSn)) {
                Log.fish.warn("玩家未拥有该鱼类, humanId={}, fishGroupSn={}", humanObj.id, fishGroupSn);
                return;
            }
        }

        // 执行装备
        for (Define.p_key_value equip : equipList) {
            houseList.put((int)equip.getK(), (int)equip.getV());
        }

        // 保存数据
        fishData.saveHouseList();

        // 响应客户端
        MsgHome.home_fish_house_equip_one_click_s2c.Builder builder = MsgHome.home_fish_house_equip_one_click_s2c.newBuilder();
        builder.addAllEquipList(equipList);
        builder.setResult(0); // 0:成功
        humanObj.sendMsg(builder.build());

        Log.fish.debug("一键装备成功, humanId={}, equipCount={}", humanObj.id, equipList.size());
    }

    /**
     * 处理抛竿钓鱼请求
     */
    public void handleFishCastC2S(HumanObject humanObj, int groundSn, List<Integer> baitSnList, int castNum) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        // 前置验证
        if (baitSnList == null || baitSnList.isEmpty()) {
            Log.fish.warn("鱼饵列表为空, humanId={}", humanObj.id);
            return;
        }
        int baitSn = baitSnList.get(0);
        if (!validateFishCast(humanObj, groundSn, baitSn, castNum)) {
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;

        // 清空旧缓存
        fishData.clearPendingCache();

        // 设置新的缓存数据
        fishData.setPendingGroundSn(groundSn);
        fishData.setPendingCastNum(castNum);

        // 准备鱼饵列表
        fishData.setPendingBaitSn(baitSn);

        // 执行第一次钓鱼逻辑，获取第一条鱼
        int firstFishSn = executeFishing(humanObj, groundSn, baitSn);
        if (firstFishSn <= 0) {
            Log.fish.error("钓鱼失败，无法获取鱼类, humanId={}, groundSn={}, baitSn={}",
                humanObj.id, groundSn, baitSn);
            return;
        }

        // 缓存第一条鱼
        fishData.getPendingFishSns().add(firstFishSn);

        // 如果是多次抛竿，继续生成其他鱼
        for (int i = 1; i < castNum; i++) {
            int fishSn = executeFishing(humanObj, groundSn, baitSn);
            if (fishSn > 0) {
                fishData.getPendingFishSns().add(fishSn);
            }
        }

        // 更新抛竿统计
        homeFish.setTotalCastCnt(homeFish.getTotalCastCnt() + castNum);

        // 响应客户端
        MsgHome.home_fish_cast_s2c.Builder builder = MsgHome.home_fish_cast_s2c.newBuilder();
        builder.setFishId(firstFishSn); // 用于客户端表现
        humanObj.sendMsg(builder.build());

        Log.fish.debug("抛竿成功, humanId={}, groundSn={}, baitSn={}, castNum={}, firstFishSn={}",
            humanObj.id, groundSn, baitSn, castNum, firstFishSn);
    }

    /**
     * 验证抛竿请求
     */
    private boolean validateFishCast(HumanObject humanObj, int groundSn, int baitSn, int castNum) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        // 检查是否正在自动钓鱼
        if (homeFish.getAutoFishGroundSn() > 0) {
            Log.fish.warn("玩家正在自动钓鱼，无法手动抛竿, humanId={}", humanObj.id);
            return false;
        }

        // 检查抛竿次数
        if (castNum != 1 && castNum != 10) {
            Log.fish.warn("无效的抛竿次数, humanId={}, castNum={}", humanObj.id, castNum);
            return false;
        }

        // 检查10连抛竿权限
        if (castNum == 10) {
            ConfFishConfig config = ConfFishConfig.get(EFishType.CONF_FISH_10_CAST_LV);
            if (config != null && config.parameter != null && config.parameter.length > 0) {
                int requiredLv = config.parameter[0][0];
                if (homeFish.getFisherLv() < requiredLv) {
                    int privilegeValue = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.Fishing_TenTimesUnlock,0);
                    if(privilegeValue < 1){
                        Log.fish.warn("钓鱼等级不足，不是vip，无法10连抛竿, humanId={}, currentLv={}, requiredLv={}",
                                humanObj.id, homeFish.getFisherLv(), requiredLv);
                        return false;
                    }
                }
            }
        }

        // 检查鱼饵数量
        ConfFishBait baitConfig = ConfFishBait.get(baitSn);
        int itemSn = baitConfig.item_id;
        int baitNum = ItemManager.inst().getItemNum(humanObj, itemSn);
        if (baitNum < castNum) {
            Log.fish.warn("鱼饵数量不足, humanId={}, baitSn={}, baitNum={}, realBaitNum={}",
                    humanObj.id, baitSn, baitNum, baitNum);
            return false;
        }
        // 检查渔场是否可用该鱼饵
        ConfFishGround groundConfig = ConfFishGround.get(groundSn);
        if (groundConfig == null) {
            Log.fish.warn("渔场配置不存在, humanId={}, groundSn={}", humanObj.id, groundSn);
            return false;
        }

        if (groundConfig.usable_bait != null) {
            boolean baitAllowed = false;
            for (int allowedBait : groundConfig.usable_bait) {
                if (allowedBait == baitSn) {
                    baitAllowed = true;
                    break;
                }
            }
            if (!baitAllowed) {
                Log.fish.warn("该渔场不允许使用此鱼饵, humanId={}, groundSn={}, baitSn={}",
                        humanObj.id, groundSn, baitSn);
                return false;
            }
        }
        return true;
    }

    /**
     * 执行钓鱼逻辑 - 随机生成鱼类
     */
    private int executeFishing(HumanObject humanObj, int groundSn, int baitSn) {
        // A. 随机鱼类组 (Fish Group)
        ConfFishBait baitConfig = ConfFishBait.get(baitSn);
        if (baitConfig == null) {
            Log.fish.error("鱼饵配置不存在, humanId={}, baitSn={}", humanObj.id, baitSn);
            return 0;
        }
        int fishGroupId = randomFishGroup(humanObj, groundSn, baitConfig);
        if (fishGroupId <= 0) {
            return 0;
        }

        // B. 随机长度级别 (grade)
        int fishSn = randomFishGrade(humanObj, fishGroupId, baitConfig);

        return fishSn;
    }

    /**
     * 随机鱼类组
     */
    private int randomFishGroup(HumanObject humanObj, int groundSn, ConfFishBait baitConfig) {
        ConfFishGround groundConfig = ConfFishGround.get(groundSn);
        if (groundConfig == null || groundConfig.fish_show == null) {
            Log.fish.error("渔场配置错误, humanId={}, groundSn={}", humanObj.id, groundSn);
            return 0;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;

        // 获取渔具属性
        int toolLucky = getToolAttributes(fishData, ConfFishTool.K.lucky);
        int toolLiveFactor = getToolAttributes(fishData, ConfFishTool.K.live_factor);

        int monthCardBonus = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.Fishing_LuckBonus,0);

        // 计算各鱼类组的最终权重
        List<Integer> weights = new ArrayList<>();
        List<Integer> groupIds = new ArrayList<>();

        for (int[] fishShow : groundConfig.fish_show) {
            if (fishShow.length < 2) continue;

            int groupId = fishShow[0];
            int baseWeight = fishShow[1];

            ConfFishGroup groupConfig = ConfFishGroup.get(groupId);
            if (groupConfig == null) continue;

            int finalWeight = calculateFishGroupWeight(groupConfig, baseWeight,
                toolLucky, toolLiveFactor, baitConfig, monthCardBonus);

            weights.add(finalWeight);
            groupIds.add(groupId);
        }

        // 根据权重随机选择
        int selectedIndex = Utils.getRandRange(weights);
        if (selectedIndex >= 0 && selectedIndex < groupIds.size()) {
            return groupIds.get(selectedIndex);
        }
        Log.fish.error("随机鱼类组失败, humanId={}, groundSn={}", humanObj.id, groundSn);
        return 0;
    }

    /**
     * 计算鱼类组权重
     */
    private int calculateFishGroupWeight(ConfFishGroup groupConfig, int baseWeight,
                                       int toolLucky, int toolLiveFactor,
                                       ConfFishBait baitConfig, int monthCardBonus) {

        int finalWeight = baseWeight;

        if (groupConfig.type == EFishType.FISH_TYPE_PRECIOUS) {
            // 珍贵鱼最终权重 = 基础权重 * [1 + 渔具幸运 * (1 + 月卡加成*0.01) * 0.01] + 鱼饵加成权重
            double multiplier = 1.0 + toolLucky * (1.0 + monthCardBonus * 0.01) * 0.01;
            finalWeight = (int)(baseWeight * multiplier);

            // 添加鱼饵加成
            if (baitConfig != null && baitConfig.parameter != null && baitConfig.parameter.length > 0) {
                finalWeight += baitConfig.parameter[0]; // 珍贵鱼饵加成
            }
        } else {
            // 其他鱼类组最终权重 = 基础权重 * (1 + 渔具权重加成*0.01) + 鱼饵权重加成
            double multiplier = 1.0 + toolLiveFactor * 0.01;
            finalWeight = (int)(baseWeight * multiplier);

            // 添加鱼饵加成 - 根据鱼类型匹配
            if (baitConfig != null && baitConfig.parameter != null && baitConfig.parameter.length > 0 && isUseful(baitConfig.type, groupConfig.type)) {
                finalWeight += baitConfig.parameter[0]; // 普通鱼饵加成
            }
        }

        return Math.max(finalWeight, 1); // 确保权重至少为1
    }

    //传入鱼饵类型返回对鱼类型加成
    private boolean isUseful(int baitType, int fishType) {
        switch (baitType) {
            case EFishType.BAIT_SMALL:
                return fishType == 1;
            case EFishType.BAIT_MEDIUM:
                return fishType == 2;
            case EFishType.BAIT_LARGE:
                return fishType == 3;
            case EFishType.BAIT_AQUATIC:
                return fishType == 4;
            case EFishType.BAIT_PRECIOUS:
                return fishType == 5;
            case EFishType.BAIT_RARE:
                return fishType == 6;
            case EFishType.BAIT_FISH_KING:
                return fishType == 10;
            default:
                return false;
        }
    }

    /**
     * 随机长度级别
     */
    private int randomFishGrade(HumanObject humanObj, int fishGroupId, ConfFishBait baitConfig) {
        FishData fishData = humanObj.operation.fishData;

        // 获取玩家渔具总等级
        int totalToolLevel = getTotalToolLevel(fishData);

        // 筛选符合条件的鱼类基础配置
        Collection<ConfFishBase> allFishBase = ConfFishBase.findAll();
        List<ConfFishBase> validFish = new ArrayList<>();

        for (ConfFishBase fishBase : allFishBase) {
            if (fishBase.fish_group_id == fishGroupId && fishBase.need_tool <= totalToolLevel) {
                validFish.add(fishBase);
            }
        }

        if (validFish.isEmpty()) {
            Log.fish.error("===ConfFishBase 配表错误， validFish为空, humanId={}, fishGroupId={}", humanObj.id, fishGroupId);
            return 0;
        }

        // 获取渔具丰饵属性
        int sizeFactor = 0;
        if(baitConfig != null) {
            sizeFactor = getToolAttributes(fishData, ConfFishTool.K.size_factor);
        }

        // 计算权重
        List<Integer> weights = new ArrayList<>();
        List<Integer> fishSns = new ArrayList<>();

        for (ConfFishBase fishBase : validFish) {
            int finalWeight = fishBase.weight + sizeFactor;
            if(baitConfig != null && baitConfig.sn == EFishType.BAIT_LENGTH) {
                finalWeight += baitConfig.parameter[0]; // 长鱼饵加成
            }
            weights.add(finalWeight);
            fishSns.add(fishBase.sn);
        }

        // 根据权重随机选择
        int selectedIndex = Utils.getRandRange(weights);
        if (selectedIndex >= 0 && selectedIndex < fishSns.size()) {
            return fishSns.get(selectedIndex);
        }
        Log.fish.error("随机鱼类失败, humanId={}", humanObj.id);
        return 0;
    }

    /**
     * 获取渔具属性总和
     */
    private int getToolAttributes(FishData fishData, String attrName) {;
        int total = 0;
        Map<Integer, Integer> toolMap = fishData.getFishTools();
        for (Map.Entry<Integer, Integer> entry : toolMap.entrySet()) {
            int toolType = entry.getKey();
            int toolLevel = entry.getValue();

            ConfFishTool toolConfig = ConfFishTool.get(toolType, toolLevel);
            if (toolConfig == null) {
                continue;
            }
            total += (int)toolConfig.getFieldValue(attrName);
        }
        return total;
    }

    /**
     * 获取渔具总等级
     */
    private int getTotalToolLevel(FishData fishData) {
        Map<Integer, Integer> toolMap = fishData.getFishTools();
        int totalLevel = 0;

        for (Integer level : toolMap.values()) {
            totalLevel += level;
        }

        return totalLevel;
    }

    /**
     * 处理收竿结算请求
     */
    public void handleFishReelC2S(HumanObject humanObj, int state) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        // 只处理成功的情况，失败直接返回
        if (state != 0) {
            humanObj.operation.fishData.clearPendingCache();
            Log.fish.debug("收竿失败，直接返回, humanId={}", humanObj.id);
            return;
        }

        FishData fishData = humanObj.operation.fishData;
        HomeFish homeFish = fishData.getHomeFish();

        // 获取待结算的鱼
        List<Integer> pendingFishSns = new ArrayList<>(fishData.getPendingFishSns());
        int baitSn = fishData.getPendingBaitSn();
        int castNum = fishData.getPendingCastNum();
        Log.fish.debug("待结算的鱼: {}, baitSn={}, castNum={}", pendingFishSns, baitSn, castNum);
        // 清空缓存
        fishData.clearPendingCache();

        if (pendingFishSns.isEmpty()) {
            Log.fish.warn("没有待结算的鱼, humanId={}", humanObj.id);
            return;
        }

        // 初始化结算数据
        int reelSuccCnt = 0;
        int slippedCnt = 0;
        Map<Integer, Integer> rewardMap = new HashMap<>();
        Set<FishDetail> albumFishesUpdate = new HashSet<>();
        Map<Integer, Integer> sellFishes = new HashMap<>();
        int firstFishId = 0;
        int firstFishLen = 0;

        // 消耗鱼饵
        if(!consumeBaits(humanObj, baitSn, castNum)){
            return;
        }

        Map<Integer, Integer> quitNumMap = new HashMap<>();
        Map<Integer, Integer> gradeNumMap = new HashMap<>();
        Map<Integer, Integer> typeNumMap = new HashMap<>();


        // 循环结算每一条鱼
        for (int i = 0; i < pendingFishSns.size(); i++) {
            int fishSn = pendingFishSns.get(i);
            ConfFishBase fishBase = ConfFishBase.get(fishSn);
            if (fishBase == null) {
                Log.fish.error("鱼类基础配置不存在, humanId={}, fishSn={}", humanObj.id, fishSn);
                continue;
            }

            // 权威逃跑判定
            if (calculateEscapeRate(humanObj, fishBase)) {
                slippedCnt++;
                continue;
            }

            // 成功钓到
            reelSuccCnt++;
            int randomLen = 0;
            if(fishBase.len_range != null && fishBase.len_range.length == 2) {
                randomLen = Utils.random(fishBase.len_range[0], fishBase.len_range[1]);
            }

            // 记录第一条鱼的信息
            if (firstFishId == 0 && castNum == 1) {
                firstFishId = fishSn;
                firstFishLen = randomLen;
            }

            // A. 奖励发放
            processRewards(humanObj, fishBase, rewardMap);

            // B. 图鉴数据更新
            FishDetail updatedFish = updateAlbumData(humanObj, fishBase, randomLen);
            // 如果是破纪录的鱼，也添加到totalAlbumFishes用于统计
            if (updatedFish.isRecord()) {
                albumFishesUpdate.add(updatedFish);
            }

            // C. 统计卖出的鱼
            ConfFishGroup groupConfig = ConfFishGroup.get(fishBase.fish_group_id);
            if (groupConfig != null) {
                sellFishes.put(fishBase.fish_group_id,
                    sellFishes.getOrDefault(fishBase.fish_group_id, 0) + 1);

                typeNumMap.put(groupConfig.type, typeNumMap.getOrDefault(groupConfig.type, 0) + 1);
                gradeNumMap.put(fishBase.grade, gradeNumMap.getOrDefault(fishBase.grade, 0) + 1);
                quitNumMap.put(groupConfig.quality, quitNumMap.getOrDefault(groupConfig.quality, 0) + 1);
            }
        }

        // 统计卖出的鱼排查掉破纪录的鱼（只有破纪录的鱼才从sellFishes中减去）
        for (FishDetail detail : albumFishesUpdate) {
            ConfFishBase baseConfig = ConfFishBase.get(detail.getFishSn());
            sellFishes.put(baseConfig.fish_group_id, sellFishes.getOrDefault(baseConfig.fish_group_id, 0) - 1);
        }
        //如果是vip鱼币加成
        int privilegeValue = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.Fishing_RewardBonus,0);
        if(privilegeValue > 0){
            for (Map.Entry<Integer, Integer> entry : rewardMap.entrySet()) {
                if(entry.getKey() == EFishType.FISHING_COIN){
                    entry.setValue(entry.getValue() * (100 + privilegeValue) / 100);
                }
            }
        }

        ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.钓鱼收竿);
        // 更新统计数据
        homeFish.setTotalReelSuccCnt(homeFish.getTotalReelSuccCnt() + reelSuccCnt);
        homeFish.setTotalSlippedCnt(homeFish.getTotalSlippedCnt() + slippedCnt);

        fishData.saveAlbumDetailMap();
        if(!albumFishesUpdate.isEmpty()){
            updatePorpCalcPower(humanObj);
        }
        // 响应客户端
        sendFishReelResponse(humanObj, castNum, reelSuccCnt, slippedCnt,
            rewardMap, albumFishesUpdate, sellFishes, firstFishId, firstFishLen);

        Event.fire(EventKey.HOME_FISHING, "humanObj", humanObj, "castNum", castNum, "reelSuccCnt", reelSuccCnt, "quitNumMap", quitNumMap, "gradeNumMap", gradeNumMap, "typeNumMap", typeNumMap);

        Log.fish.debug("收竿结算完成, humanId={}, 成功={}, 逃跑={}",
            humanObj.id, reelSuccCnt, slippedCnt);
    }

    @Listener(EventKey.HOME_FISHING)
    public void onHomeFishing(Param param) {
        HumanObject humanObj = param.get("humanObj");
        int castNum = param.getInt("castNum");
        int reelSuccCnt = param.getInt("reelSuccCnt");
        Map<Integer, Integer> quitNumMap = param.get("quitNumMap");
        Map<Integer, Integer> gradeNumMap = param.get("gradeNumMap");
        Map<Integer, Integer> typeNumMap = param.get("typeNumMap");

        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_钓鱼每日任务, TaskConditionTypeKey.TASK_TYPE_2020, castNum);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_渔场解锁, TaskConditionTypeKey.TASK_TYPE_2020, castNum);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_活动, TaskConditionTypeKey.TASK_TYPE_2020, castNum);
        Event.fire(EventKey.ACTIVITY_ADD_PROGRESS, "humanObj", humanObj, "module", ActivityProgressSourceModule.Fish.value, "num", castNum);// 这个本身会更新活动任务
        if (reelSuccCnt > 0) {
            humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_钓鱼每日任务, TaskConditionTypeKey.TASK_TYPE_2021, reelSuccCnt);
            for (Map.Entry<Integer, Integer> entry : quitNumMap.entrySet()) {
                humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_钓鱼每日任务, TaskConditionTypeKey.TASK_TYPE_2019, entry.getKey(), entry.getValue());
            }
            for (Map.Entry<Integer, Integer> entry : gradeNumMap.entrySet()) {
                humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_钓鱼每日任务, TaskConditionTypeKey.TASK_TYPE_2016, entry.getKey(), entry.getValue());
            }
            for (Map.Entry<Integer, Integer> entry : typeNumMap.entrySet()) {
                humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_钓鱼每日任务, TaskConditionTypeKey.TASK_TYPE_2018, entry.getKey(), entry.getValue());
            }

            //加战令经验
            ConfFishConfig conf = ConfFishConfig.get(EFishType.CONF_ACT_TYPE_WAR_TOKEN);
            if(conf == null || conf.parameter == null || conf.parameter.length == 0){
                Log.fish.error("===ConfFishConfig=null, sn={}", EFishType.CONF_ACT_TYPE_WAR_TOKEN);
                return;
            }
            IActivityControl control = ActivityControlTypeFactory.getTypeData(conf.parameter[0][0]);
            if(control == null || !(control instanceof ActivityControlWarToken)) {
                Log.fish.error("===ActivityControlWarToken=null, sn={}", conf.parameter[0][0]);
            }else {
                int expLimit = ConfFishConfig.get(EFishType.CONF_DAILY_EXP_LIMIT).parameter[0][0];
                int expReward = ConfFishConfig.get(EFishType.CONF_FISH_EXP_REWARD).parameter[0][0];
                HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
                int alreadyAddedExp = homeFish.getDailyReelSuccCount() * expReward;
                int canAddExp = expLimit - alreadyAddedExp;
                int addExp = reelSuccCnt * expReward;
                int actualAddExp = Math.min(addExp, canAddExp);
                homeFish.setDailyReelSuccCount(homeFish.getDailyReelSuccCount() + reelSuccCnt);
                if(actualAddExp <= 0){
                    return;
                }
                ActivityControlWarToken activityControlWarToken = (ActivityControlWarToken) control;
                activityControlWarToken.addWartokenExp(humanObj, actualAddExp);
            }
        }
    }

    /**
     * 消耗鱼饵
     */
    private boolean consumeBaits(HumanObject humanObj, int baitSn, int baitNum) {
        ConfFishBait baitConfig = ConfFishBait.get(baitSn);
        if (baitConfig == null) {
            Log.fish.error("鱼饵配置不存在, humanId={}, baitSn={}", humanObj.id, baitSn);
            return false;
        }

        int actualConsumeNum = baitNum;
        if (baitConfig.sn == EFishType.BAIT_LOSSLESS &&
                baitConfig.parameter != null && baitConfig.parameter.length > 0) {

            int probability = baitConfig.parameter[0];
            for (int i = 0; i < baitNum; i++) {
                if (Utils.random(10000) <= probability) {
                    actualConsumeNum--; // 概率不消耗
                }
            }
            actualConsumeNum = Math.max(0, actualConsumeNum); // 确保不小于0
        }
        Log.fish.debug("消耗鱼饵, humanId={}, baitSn={}, 使用数量={}, 实际消耗={}",
                humanObj.id, baitSn, baitNum, actualConsumeNum);

        // 鱼饵转道具消耗
        return ProduceManager.inst().checkAndCostItem(humanObj, baitConfig.item_id, actualConsumeNum, MoneyItemLogKey.钓鱼自动收竿).success;
    }

    /**
     * 计算逃跑率
     */
    private boolean calculateEscapeRate(HumanObject humanObj, ConfFishBase fishBase) {
        FishData fishData = humanObj.operation.fishData;

        // 获取基础逃跑率配置
        ConfFishConfig escapeConfig = ConfFishConfig.get(EFishType.CONF_BASE_ESCAPE_RATE);
        if (escapeConfig == null || escapeConfig.parameter == null || escapeConfig.parameter.length == 0) {
            return false;
        }

        int baseEscapeRate = escapeConfig.parameter[0][0]; // 万分比

        // 获取渔具稳固属性
        int toolStable = getToolAttributes(fishData, ConfFishTool.K.stable);

        // 获取小鱼稳固属性
        int fishStable = fishBase.restable;

        // 计算最终逃跑率: 基础逃跑率*0.0001/[(1+渔具稳固*0.01)*(1-小鱼稳固*0.01)]
        double escapeRate = (baseEscapeRate * 0.0001) /
            ((1.0 + toolStable * 0.01) * (1.0 - fishStable * 0.01));

        // 转换为万分比
        int finalEscapeRate = (int)(escapeRate * 10000);

        return Utils.random(10000) < finalEscapeRate;
    }

    /**
     * 处理奖励发放
     */
    private void processRewards(HumanObject humanObj, ConfFishBase fishBase, Map<Integer, Integer> rewardMap) {
        ConfFishGroup groupConfig = ConfFishGroup.get(fishBase.fish_group_id);
        if (groupConfig == null) return;

        // 发放货币奖励
        if (groupConfig.sell != null && groupConfig.sell.length >= 2) {
            int currencyId = groupConfig.sell[0];
            int amount = groupConfig.sell[1];

            rewardMap.put(currencyId, rewardMap.getOrDefault(currencyId, 0) + amount);
        }

        // 发放钓鱼经验
        if (fishBase.exp > 0) {
            addFisherExp(humanObj, fishBase.exp);
        }
    }

    /**
     * 更新图鉴数据
     */
    private FishDetail updateAlbumData(HumanObject humanObj, ConfFishBase fishBase, int randomLen) {
        FishData fishData = humanObj.operation.fishData;

        // 获取当前图鉴数据
        Map<Integer, FishDetail> albumMap = fishData.getAlbumDetailMap();
        int fishGroupId = fishBase.fish_group_id;

        FishDetail currentDetail = albumMap.get(fishGroupId);

        boolean isNewRecord = false;

        if (currentDetail == null) {
            // 第一次钓到这种鱼
            currentDetail = new FishDetail(fishBase.sn, randomLen, 0, 1);
            isNewRecord = true; // 第一次获得也算破纪录
        } else {
            // 检查是否破纪录
            if (randomLen > currentDetail.getMaxLen()) {
                currentDetail.setMaxLen(randomLen);
                currentDetail.setFishSn(fishBase.sn); // 更新为新的鱼类基础表sn
                isNewRecord = true;
            }

            // 累积经验
            currentDetail.setExp(currentDetail.getExp() + 1);
        }

        // 设置是否破纪录标记
        currentDetail.setRecord(isNewRecord);

        // 更新图鉴
        albumMap.put(fishGroupId, currentDetail);

        LogOp.log("addFlyEgg",
                humanObj.getHuman().getId(),
                Port.getTime(),
                Utils.formatTime(Port.getTime(), "yyyy-MM-dd"),
                humanObj.getHuman().getAccount(),
                humanObj.getHuman().getName(),
                humanObj.getHuman().getLevel(),
                humanObj.getHuman().getServerId(),
                currentDetail.getFishSn(),
                1,
                currentDetail.toJSONString()
        );

        // 保存数据结构到实体
        return currentDetail;
    }

    /**
     * 添加钓鱼者经验
     */
    public void addFisherExp(HumanObject humanObj, int exp) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        int currentExp = homeFish.getFisherExp() + exp;
        if(currentExp < 0){
            Log.fish.error("钓鱼经验超过int最大值, humanId={}, currentExp={}", humanObj.id, currentExp);
            return;
        }
        homeFish.setFisherExp(currentExp);

        // 检查是否可以升级
        checkFisherLevelUp(humanObj);
    }

    /**
     * 检查钓鱼者等级提升
     */
    private void checkFisherLevelUp(HumanObject humanObj) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        while (true) {
            ConfFisher fisherConfig = ConfFisher.get(homeFish.getFisherLv());
            // 这里需要配置表支持，暂时使用简单的升级逻辑
            int nextLevel = homeFish.getFisherLv() + 1;
            int needExp = fisherConfig.need; // 简单的升级公式
            ConfFisher nextFisherConfig = ConfFisher.get(nextLevel);
            if (nextFisherConfig == null) {
                homeFish.setFisherExp(homeFish.getFisherExp() - needExp);
                break;
            }
            if (homeFish.getFisherExp() >= needExp) {
                homeFish.setFisherLv(nextLevel);
                homeFish.setFisherExp(homeFish.getFisherExp() - needExp);

                // 检查钓鱼等级相关任务
                humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_渔场解锁, TaskConditionTypeKey.TASK_TYPE_2014);

                Log.fish.debug("钓鱼者升级, humanId={}, newLevel={}", humanObj.id, nextLevel);
            } else {
                break;
            }
        }
    }

    /**
     * 发送收竿响应
     */
    private void sendFishReelResponse(HumanObject humanObj, int castNum, int reelSuccCnt, int slippedCnt,
                                    Map<Integer, Integer> rewardMap, Set<FishDetail> albumFishesUpdate,
                                    Map<Integer, Integer> sellFishes, int firstFishId, int firstFishLen) {

        MsgHome.home_fish_reel_s2c.Builder builder = MsgHome.home_fish_reel_s2c.newBuilder();

        // 只发送破纪录的鱼到album_fishes
        for (FishDetail detail : albumFishesUpdate) {
            if (detail.isRecord()) { // 只有破纪录的鱼才发送
                ConfFishBase baseConfig = ConfFishBase.get(detail.getFishSn());
                Define.p_home_fish_detail.Builder detailBuilder = Define.p_home_fish_detail.newBuilder();
                detailBuilder.setFishTypeSn(baseConfig.fish_group_id); // 这里需要鱼类组SN
                detailBuilder.setFishSn(detail.getFishSn());
                detailBuilder.setLen(detail.getMaxLen());
                detailBuilder.setLv(detail.getLevel());
                detailBuilder.setExp(detail.getExp());
                builder.addAlbumFishes(detailBuilder.build());
            }
        }

        // 卖出的鱼
        for (Map.Entry<Integer, Integer> entry : sellFishes.entrySet()) {
            if(entry.getValue() <= 0){
                continue;
            }
            Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
            kvBuilder.setK(entry.getKey());
            kvBuilder.setV(entry.getValue());
            builder.addSellFishes(kvBuilder.build());
        }

        builder.setCastNum(castNum);
        builder.setReelSuccCnt(reelSuccCnt);
        builder.setSlippedCnt(slippedCnt);

        // 出售获得的奖励
        for (Map.Entry<Integer, Integer> entry : rewardMap.entrySet()) {
            Define.p_reward.Builder rewardBuilder = Define.p_reward.newBuilder();
            rewardBuilder.setGtid(entry.getKey());
            rewardBuilder.setNum(entry.getValue());
            builder.addReward(rewardBuilder.build());
        }

        builder.setFishId(firstFishId);
        builder.setLen(firstFishLen);

        // 添加钓鱼佬等级和经验
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        builder.setFisherLv(homeFish.getFisherLv());
        builder.setFisherExp(homeFish.getFisherExp());

        humanObj.sendMsg(builder.build());
    }

    /**
     * 处理鱼类升级请求
     */
    public void handleFishGroupLevelUpC2S(HumanObject humanObj, int fishType) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        FishData fishData = humanObj.operation.fishData;
        Map<Integer, FishDetail> albumDetailMap = fishData.getAlbumDetailMap();

        List<FishUpgradeInfo> upgradeInfoList = new ArrayList<>();
        List<Integer> upgradedFishTypes = new ArrayList<>();

        if (fishType == 0) {
            // 一键升级所有鱼（按ID排序）
            List<Integer> sortedFishTypes = new ArrayList<>(albumDetailMap.keySet());
            Collections.sort(sortedFishTypes);

            Log.fish.info("开始一键升级, humanId={}, 鱼类数量={}, 排序后ID={}",
                humanObj.id, sortedFishTypes.size(), sortedFishTypes);

            for (Integer fishTypeSn : sortedFishTypes) {
                FishDetail detail = albumDetailMap.get(fishTypeSn);
                if (detail != null) {
                    Log.fish.debug("尝试升级鱼类, fishTypeSn={}, currentLevel={}, currentExp={}",
                        fishTypeSn, detail.getLevel(), detail.getExp());

                    FishUpgradeInfo upgradeInfo = upgradeToMaxLevel(humanObj, fishTypeSn, detail);
                    if (upgradeInfo != null) {
                        upgradeInfoList.add(upgradeInfo);
                        upgradedFishTypes.add(fishTypeSn);
                        Log.fish.info("鱼类升级成功, fishTypeSn={}, {}级→{}级, 消耗货币={}",
                            fishTypeSn, upgradeInfo.oldLevel, upgradeInfo.newLevel, upgradeInfo.costAmount);
                    } else {
                        Log.fish.debug("鱼类无法升级, fishTypeSn={}", fishTypeSn);
                    }
                }
            }
        } else {
            // 升级指定鱼类一级
            FishDetail detail = albumDetailMap.get(fishType);
            if (detail != null) {
                Log.fish.debug("尝试升级指定鱼类一级, fishTypeSn={}, currentLevel={}, currentExp={}",
                    fishType, detail.getLevel(), detail.getExp());

                FishUpgradeInfo upgradeInfo = upgradeOneLevel(humanObj, fishType, detail);
                if (upgradeInfo != null) {
                    upgradeInfoList.add(upgradeInfo);
                    upgradedFishTypes.add(fishType);
                    Log.fish.info("指定鱼类升级成功, fishTypeSn={}, {}级→{}级, 消耗货币={}",
                        fishType, upgradeInfo.oldLevel, upgradeInfo.newLevel, upgradeInfo.costAmount);
                } else {
                    Log.fish.debug("指定鱼类无法升级, fishTypeSn={}", fishType);
                }
            } else {
                Log.fish.warn("指定鱼类不存在, fishTypeSn={}", fishType);
            }
        }

        // 执行升级
        boolean hasUpgrade = false;
        for (FishUpgradeInfo upgradeInfo : upgradeInfoList) {
            FishDetail detail = albumDetailMap.get(upgradeInfo.fishTypeSn);
            if (detail != null) {
                detail.setLevel(upgradeInfo.newLevel);
                detail.setExp(upgradeInfo.remainExp);
                hasUpgrade = true;
            }
        }

        if (hasUpgrade) {
            // 只保存图鉴数据
           fishData.saveAlbumDetailMap();

            // 重新计算评分
            updateTotalScore(humanObj);

            // 检查图鉴升级
            checkAlbumLevelUp(humanObj);
        }

        // 响应客户端
        sendFishGroupLevelUpResponse(humanObj, upgradedFishTypes, upgradeInfoList);

        Log.fish.debug("鱼类升级完成, humanId={}, upgradeCount={}", humanObj.id, upgradeInfoList.size());
    }

    /**
     * 升级指定鱼类一级
     */
    private FishUpgradeInfo upgradeOneLevel(HumanObject humanObj, int fishTypeSn, FishDetail detail) {
        int currentLevel = detail.getLevel();
        int currentExp = detail.getExp();

        // 检查是否可以升级
        ConfFishLevel nextLevelConfig = ConfFishLevel.get(fishTypeSn, currentLevel + 1);
        if (nextLevelConfig == null) {
            Log.fish.debug("鱼类已达最大等级, fishTypeSn={}, currentLevel={}", fishTypeSn, currentLevel);
            return null;
        }
        ConfFishLevel currentLevelConfig = ConfFishLevel.get(fishTypeSn, currentLevel);
        // 检查经验是否足够
        if (currentExp < currentLevelConfig.need) {
            Log.fish.debug("经验不足，无法升级, fishTypeSn={}, currentExp={}, needExp={}",
                fishTypeSn, currentExp, nextLevelConfig.need);
            return null;
        }

        // 检查并消耗货币
        if (currentLevelConfig.cost != null && currentLevelConfig.cost.length >= 2) {
            int currencyId = currentLevelConfig.cost[0];
            int costAmount = currentLevelConfig.cost[1];

            ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, currencyId, costAmount,
                MoneyItemLogKey.钓鱼图鉴升级);

            if (!result.success) {
                Log.fish.warn("货币不足，无法升级鱼类, humanId={}, fishTypeSn={}, currencyId={}, needAmount={}",
                    humanObj.id, fishTypeSn, currencyId, costAmount);
                return null;
            }
        }

        // 创建升级信息
        int newLevel = currentLevel + 1;
        int remainExp = currentExp - currentLevelConfig.need;

        return new FishUpgradeInfo(fishTypeSn, currentLevel, newLevel, remainExp,
                currentLevelConfig.cost != null ? currentLevelConfig.cost[0] : 0,
                currentLevelConfig.cost != null ? currentLevelConfig.cost[1] : 0);
    }

    /**
     * 升级指定鱼类到最大等级（一键升级用）
     * 一级一级升级，货币不足就停止，继续下一个鱼类
     */
    private FishUpgradeInfo upgradeToMaxLevel(HumanObject humanObj, int fishTypeSn, FishDetail detail) {
        int currentLevel = detail.getLevel();
        int currentExp = detail.getExp();
        int newLevel = currentLevel;
        int remainExp = currentExp;

        long totalCostAmount = 0;
        int mainCurrencyId = 0;

        // 一级一级升级，直到货币不足或经验不足
        while (true) {
            ConfFishLevel nextLevelConfig = ConfFishLevel.get(fishTypeSn, newLevel + 1);
            if (nextLevelConfig == null) {
                Log.fish.debug("鱼类已达最大等级, fishTypeSn={}, level={}", fishTypeSn, newLevel);
                break; // 已达最大等级
            }
            ConfFishLevel currentLevelConfig = ConfFishLevel.get(fishTypeSn, newLevel);

            // 检查经验是否足够
            if (remainExp < currentLevelConfig.need) {
                Log.fish.debug("经验不足，停止升级, fishTypeSn={}, level={}, remainExp={}, needExp={}",
                    fishTypeSn, newLevel, remainExp, nextLevelConfig.need);
                break; // 经验不足
            }

            // 检查并消耗本级升级的货币
            boolean canUpgrade = true;
            if (currentLevelConfig.cost != null && currentLevelConfig.cost.length >= 2) {
                int currencyId = currentLevelConfig.cost[0];
                int costAmount = currentLevelConfig.cost[1];

                ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, currencyId, costAmount,
                    MoneyItemLogKey.钓鱼图鉴升级);

                if (!result.success) {
                    Log.fish.debug("货币不足，停止升级, fishTypeSn={}, level={}, currencyId={}, needAmount={}",
                        fishTypeSn, newLevel, currencyId, costAmount);
                    canUpgrade = false;
                } else {
                    // 记录消耗
                    totalCostAmount += costAmount;
                    mainCurrencyId = currencyId;
                }
            }

            if (!canUpgrade) {
                break; // 货币不足，停止升级
            }

            // 执行升级
            newLevel++;
            remainExp -= currentLevelConfig.need;

            Log.fish.debug("鱼类升级成功, fishTypeSn={}, newLevel={}, remainExp={}",
                fishTypeSn, newLevel, remainExp);
        }

        // 如果没有升级，返回null
        if (newLevel == currentLevel) {
            Log.fish.debug("鱼类无法升级, fishTypeSn={}, currentLevel={}", fishTypeSn, currentLevel);
            return null;
        }

        // 创建升级信息
        return new FishUpgradeInfo(fishTypeSn, currentLevel, newLevel, remainExp,
            mainCurrencyId, totalCostAmount);
    }

    /**
     * 发送鱼类升级响应
     */
    private void sendFishGroupLevelUpResponse(HumanObject humanObj, List<Integer> upgradedFishTypes,
                                            List<FishUpgradeInfo> upgradeInfoList) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;

        MsgHome.home_fish_group_level_up_s2c.Builder builder = MsgHome.home_fish_group_level_up_s2c.newBuilder();

        // 设置总评分和图鉴等级
        builder.setTotalScore((int)homeFish.getTotalScore());
        builder.setAlbumLv(homeFish.getAlbumLv());
        builder.setAlbumExp(homeFish.getAlbumExp());

        // 添加变更的图鉴详情
        Map<Integer, FishDetail> albumDetailMap = fishData.getAlbumDetailMap();
        for (Integer fishTypeSn : upgradedFishTypes) {
            FishDetail detail = albumDetailMap.get(fishTypeSn);
            if (detail != null) {
                Define.p_home_fish_detail.Builder detailBuilder = Define.p_home_fish_detail.newBuilder();
                detailBuilder.setFishTypeSn(fishTypeSn);
                detailBuilder.setFishSn(detail.getFishSn());
                detailBuilder.setLen(detail.getMaxLen());
                detailBuilder.setLv(detail.getLevel());
                detailBuilder.setExp(detail.getExp());
                builder.addDetailList(detailBuilder.build());
            }
        }

        humanObj.sendMsg(builder.build());
    }

    /**
     * 更新总评分
     */
    private void updateTotalScore(HumanObject humanObj) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;
        Map<Integer, FishDetail> albumMap = fishData.getAlbumDetailMap();

        int totalScore = 0;

        for (Map.Entry<Integer, FishDetail> entry : albumMap.entrySet()) {
            int fishTypeSn = entry.getKey();
            FishDetail detail = entry.getValue();

            // 检查是否是珍贵鱼
            ConfFishGroup groupConfig = ConfFishGroup.get(fishTypeSn);
            if (groupConfig != null && groupConfig.type != EFishType.FISH_TYPE_PRECIOUS) {
                // 非珍贵鱼才有评分
                ConfFishBase fishBase = ConfFishBase.get(detail.getFishSn());
                ConfFishLevel levelConfig = ConfFishLevel.get(fishTypeSn, detail.getLevel());

                if (fishBase != null && levelConfig != null) {
                    int fishScore = fishBase.score * levelConfig.score_mult / 100;
                    totalScore += fishScore;
                }
            }
        }

        // 设置long类型的总分
        homeFish.setTotalScore(totalScore);

        // 检查渔册总评分相关任务
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_钓鱼每日任务, TaskConditionTypeKey.TASK_TYPE_2017);
    }

    /**
     * 检查图鉴升级
     */
    private void checkAlbumLevelUp(HumanObject humanObj) {
        FishData fishData = humanObj.operation.fishData;
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        Map<Integer, FishDetail> albumMap = fishData.getAlbumDetailMap();

        // 累加所有鱼的等级
        int totalFishLevel = 0;
        for (Map.Entry<Integer, FishDetail> entry : albumMap.entrySet()) {
            FishDetail detail = entry.getValue();
            totalFishLevel += detail.getLevel();
        }

        int preNeed = 0;
        while (true) {
            ConfFishBook fishBookConfig = ConfFishBook.get(humanObj.operation.fishData.getHomeFish().getAlbumLv() + 1);
            if (fishBookConfig == null) {
                break;
            }
            if (totalFishLevel >= fishBookConfig.need) {
                homeFish.setAlbumLv(humanObj.operation.fishData.getHomeFish().getAlbumLv() + 1);
                homeFish.setAlbumExp(totalFishLevel - fishBookConfig.need);
                preNeed = fishBookConfig.need;
            } else {
                homeFish.setAlbumExp(totalFishLevel - preNeed);
                break;
            }
        }

        Log.fish.debug("检查图鉴升级, humanId={}, totalFishLevel={}", humanObj.id, totalFishLevel);
    }

    /**
     * 处理渔具升级请求
     */
    public void handleFishToolLvUpC2S(HumanObject humanObj, int toolType) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;
        Map<Integer, Integer> toolMap = fishData.getFishTools();

        int currentLevel = toolMap.getOrDefault(toolType, 0);
        int nextLevel = currentLevel + 1;

        // 获取升级配置
        ConfFishTool toolConfig = ConfFishTool.get(toolType, currentLevel);
        if (toolConfig == null) {
            Log.fish.warn("渔具升级配置不存在, humanId={}, toolType={}, nextLevel={}",
                humanObj.id, toolType, nextLevel);
            return;
        }

        // 检查前置条件
        if (toolConfig.condition != null) {
            for (int[] condition : toolConfig.condition) {
                if (condition.length >= 2) {
                    int requiredToolType = condition[0];
                    int requiredLevel = condition[1];
                    int currentRequiredLevel = toolMap.getOrDefault(requiredToolType, 0);

                    if (currentRequiredLevel < requiredLevel) {
                        Log.fish.warn("渔具升级前置条件不满足, humanId={}, toolType={}, requiredToolType={}, requiredLevel={}, currentLevel={}",
                            humanObj.id, toolType, requiredToolType, requiredLevel, currentRequiredLevel);
                        return;
                    }
                }
            }
        }

        // 检查消耗
        if(!ProduceManager.inst().checkAndCostItem(humanObj, toolConfig.cost[0], toolConfig.cost[1], MoneyItemLogKey.钓鱼渔具升级).success){
            Log.fish.error("升级渔具失败道具不足, humanId={}, toolType={}", humanObj.id, toolType);
            return;
        }

        // 执行升级
        toolMap.put(toolType, nextLevel);
        // 保存数据结构到实体
        fishData.saveFishTools();

        // 检查渔具总等级相关任务
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_渔场解锁, TaskConditionTypeKey.TASK_TYPE_2015);

        // 响应客户端
        MsgHome.home_fish_tool_lv_up_s2c.Builder builder = MsgHome.home_fish_tool_lv_up_s2c.newBuilder();
        builder.setToolType(toolType);
        builder.setToolLv(nextLevel);
        humanObj.sendMsg(builder.build());

        Log.fish.debug("渔具升级成功, humanId={}, toolType={}, newLevel={}",
            humanObj.id, toolType, nextLevel);
    }

    /**
     * 处理解锁鱼塘槽位请求
     */
    public void handleFishUnlockHouseSlotC2S(HumanObject humanObj, int locationId) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;
        Map<Integer, Integer> houseMap = fishData.getHouseList();

        // 检查槽位是否已解锁
        if (houseMap.containsKey(locationId)) {
            Log.fish.warn("槽位已解锁, humanId={}, locationId={}", humanObj.id, locationId);
            return;
        }

        // 获取解锁配置
        ConfFishHouse houseConfig = ConfFishHouse.get(locationId);
        if (houseConfig == null) {
            Log.fish.warn("鱼塘槽位配置不存在, humanId={}, locationId={}", humanObj.id, locationId);
            return;
        }

        // 检查解锁顺序 - 槽位要按顺序解锁
        if (!checkHouseSlotOrder(houseMap, locationId)) {
            Log.fish.warn("槽位解锁顺序错误, humanId={}, locationId={}", humanObj.id, locationId);
            return;
        }

        // 检查解锁消耗
        if (houseConfig.cost != null && houseConfig.cost.length >= 2) {
            if(!ProduceManager.inst().checkAndCostItem(humanObj, houseConfig.cost[0], houseConfig.cost[1], MoneyItemLogKey.钓鱼槽位解锁升级).success){
                Log.fish.error("解锁鱼塘槽位失败道具不足, humanId={}, locationId={}", humanObj.id, locationId);
                return;
            }
        }

        // 执行解锁
        houseMap.put(locationId, 0); // 0表示已解锁但未装备
        // 保存数据结构到实体
        homeFish.setHouseList(Utils.mapIntIntToJSON(houseMap));

        // 响应客户端
        MsgHome.home_fish_unlock_house_slot_s2c.Builder builder = MsgHome.home_fish_unlock_house_slot_s2c.newBuilder();
        builder.setLocationId(locationId);
        humanObj.sendMsg(builder.build());

        Log.fish.debug("解锁鱼塘槽位成功, humanId={}, locationId={}", humanObj.id, locationId);
    }

    /**
     * 检查鱼塘槽位解锁顺序
     */
    private boolean checkHouseSlotOrder(Map<Integer, Integer> houseMap, int locationId) {
        // 检查前面的槽位是否都已解锁
        for (int i = 1; i < locationId; i++) {
            if (!houseMap.containsKey(i)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 处理鱼塘装备鱼请求
     */
    public void handleFishHouseEquipC2S(HumanObject humanObj, int locationId, int fishGroupSn) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;
        Map<Integer, Integer> houseMap = fishData.getHouseList();

        // 检查槽位是否已解锁
        if (!houseMap.containsKey(locationId)) {
            Log.fish.warn("槽位未解锁, humanId={}, locationId={}", humanObj.id, locationId);
            return;
        }

        if (fishGroupSn > 0) {
            // 装备鱼类
            if (!validateFishEquip(humanObj, locationId, fishGroupSn, houseMap)) {
                return;
            }
        }

        // 更新装备
        houseMap.put(locationId, fishGroupSn); // 0表示卸下，>0表示装备的鱼类组SN
        // 保存数据结构到实体
        homeFish.setHouseList(Utils.mapIntIntToJSON(houseMap));

        //重新计算所有已装备鱼提供的"展示属性"总和，并更新玩家的战斗属性
        updateEquipPorpCalcPower(humanObj);

        // 响应客户端
        MsgHome.home_fish_house_equip_s2c.Builder builder = MsgHome.home_fish_house_equip_s2c.newBuilder();
        builder.setLocationId(locationId);
        builder.setFishTypeSn(fishGroupSn);
        humanObj.sendMsg(builder.build());

        Log.fish.debug("鱼塘装备更新, humanId={}, locationId={}, fishGroupSn={}",
            humanObj.id, locationId, fishGroupSn);
    }

    /**
     * 验证鱼类装备
     */
    private boolean validateFishEquip(HumanObject humanObj, int locationId, int fishGroupSn,
                                    Map<Integer, Integer> houseMap) {

        // 检查槽位是否允许放入该类型的鱼
        ConfFishHouse houseConfig = ConfFishHouse.get(locationId);
        if (houseConfig == null) {
            Log.fish.warn("鱼塘槽位配置不存在, humanId={}, locationId={}", humanObj.id, locationId);
            return false;
        }

        ConfFishGroup groupConfig = ConfFishGroup.get(fishGroupSn);
        if (groupConfig == null) {
            Log.fish.warn("鱼类组配置不存在, humanId={}, fishGroupSn={}", humanObj.id, fishGroupSn);
            return false;
        }

        if (houseConfig.fish_type != 0 && houseConfig.fish_type != groupConfig.type) {
            Log.fish.warn("槽位不允许放入该类型的鱼, humanId={}, locationId={}, fishType={}, allowedType={}",
                humanObj.id, locationId, groupConfig.type, houseConfig.fish_type);
            return false;
        }

        // 检查该鱼是否已在其他槽位装备
        for (Map.Entry<Integer, Integer> entry : houseMap.entrySet()) {
            if (!entry.getKey().equals(locationId)) {
                int equippedFishSn = entry.getValue();
                if (equippedFishSn == fishGroupSn) {
                    Log.fish.warn("该鱼已在其他槽位装备, humanId={}, fishGroupSn={}, otherLocationId={}",
                        humanObj.id, fishGroupSn, entry.getKey());
                    return false;
                }
            }
        }

        // 检查玩家是否拥有该鱼类
        FishData fishData = humanObj.operation.fishData;
        Map<Integer, FishDetail> albumMap = fishData.getAlbumDetailMap();
        if (!albumMap.containsKey(fishGroupSn)) {
            Log.fish.warn("玩家未拥有该鱼类, humanId={}, fishGroupSn={}", humanObj.id, fishGroupSn);
            return false;
        }

        return true;
    }

    /**
     * 鱼塘装扮解锁升级
     */
    public void handleFishHouseDesignLevelUpC2S(HumanObject humanObj, int sn) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;
        Map<Integer, Integer> designMap = fishData.getHouseDesign();
        int level = designMap.getOrDefault(sn, 0);
        ConfFishDesign_0 designConfig = ConfFishDesign_0.get(sn, level);
        if(designConfig == null) {
            Log.fish.error("装扮激活升级找不到配置，sn={}, lv={}",sn, level);
            return;
        }
        if(!ProduceManager.inst().checkAndCostItem(humanObj, designConfig.expend[0], designConfig.expend[1], MoneyItemLogKey.鱼塘装扮升级).success){
            Log.fish.error("装扮激活升级道具不足，humanId={}",humanObj.id);
            return;
        }
        designMap.put(sn, level+1);
        homeFish.setHouseDesign(Utils.mapIntIntToJSON(designMap));
        updatePorpCalcPower(humanObj);

        MsgHome.home_fish_house_design_level_up_s2c.Builder builder = MsgHome.home_fish_house_design_level_up_s2c.newBuilder();
        builder.setSn(sn);
        builder.setLevel(level+1);
        humanObj.sendMsg(builder.build());
    }

    /**
     * 鱼塘装扮使用
     */
    public void handleFishHouseSetUseDesignC2S(HumanObject humanObj, int sn) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;
        Map<Integer, Integer> designMap = fishData.getHouseDesign();
        if(!designMap.containsKey(sn)){
            Log.fish.error("装扮未解锁，humanId={}, sn={}",humanObj.id, sn);
            return;
        }
        homeFish.setCurrentHouseDesign(sn);
        MsgHome.home_fish_house_set_use_design_s2c.Builder builder = MsgHome.home_fish_house_set_use_design_s2c.newBuilder();
        builder.setSn(sn);
        humanObj.sendMsg(builder.build());
    }

    private void updatePorpCalcPower(HumanObject humanObj){
        FishData fishData = humanObj.operation.fishData;
        PropCalc propCalc = new PropCalc();
        int power = 0;
        //装扮属性
        Map<Integer, Integer> designMap = fishData.getHouseDesign();
        for (Map.Entry<Integer, Integer> entry : designMap.entrySet()) {
            ConfFishDesign_0 designConfig = ConfFishDesign_0.get(entry.getKey(), entry.getValue());
            if(designConfig == null) continue;
            propCalc.plus(designConfig.own_attrs);
            power += designConfig.power;
        }
        //鱼册属性
        Map<Integer, FishDetail> albumDetailMap = fishData.getAlbumDetailMap();
        for (Map.Entry<Integer, FishDetail> entry : albumDetailMap.entrySet()) {
            FishDetail detail = entry.getValue();
            ConfFishBase fishBase = ConfFishBase.get(detail.getFishSn());
            if(fishBase == null) continue;
            ConfFishGroup groupConfig = ConfFishGroup.get(entry.getKey());
            if(groupConfig == null) continue;
            propCalc.plus(fishBase.attr_base);
            ConfFishLevel levelConfig = ConfFishLevel.get(entry.getKey(), detail.getLevel());
            if(levelConfig == null) continue;
            propCalc.mul(levelConfig.attr_base_mult);
            power += levelConfig.power;
        }
        ConfFishBook bookConfig = ConfFishBook.get(fishData.getHomeFish().getAlbumLv());
        if(bookConfig != null){
            propCalc.plus(bookConfig.attr);
            power += bookConfig.power;
        }
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.fish, propCalc.toJSONStr());
        String oldPower = humanObj.getHuman().getCombat();
        HumanManager.inst().updatePowerPar(humanObj, EModule.fish, power);
        PropManager.inst().propCalc(humanObj, CombatChangeLog.钓鱼);
        if(oldPower.equals(humanObj.getHuman().getCombat())){
            HumanManager.inst().sendMsg_role_total_sp_update_s2c(humanObj);
        }
    }

    private void updateEquipPorpCalcPower(HumanObject humanObj){
        FishData fishData = humanObj.operation.fishData;
        PropCalc propCalc = new PropCalc();
        Map<Integer, Integer> houseList = fishData.getHouseList();
        Map<Integer, FishDetail> albumDetailMap = fishData.getAlbumDetailMap();
        for (Map.Entry<Integer, Integer> entry : houseList.entrySet()) {
            int fishGroupSn = entry.getValue();
            if(fishGroupSn == 0) continue;
            int fishSn = albumDetailMap.get(fishGroupSn).getFishSn();
            ConfFishBase fishBase = ConfFishBase.get(fishSn);
            if(fishBase == null){
                Log.fish.error("鱼塘装备鱼类基础配置不存在，humanId={}, fishSn={}",humanObj.id, fishSn);
                continue;
            }
            propCalc.plus(fishBase.attr_show);
        }
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.fish, propCalc.toJSONStr());
        PropManager.inst().propCalc(humanObj, CombatChangeLog.钓鱼);
        String oldPower = humanObj.getHuman().getCombat();
        if(oldPower.equals(humanObj.getHuman().getCombat())){
            HumanManager.inst().sendMsg_role_total_sp_update_s2c(humanObj);
        }
    }

    /**
     * 处理开始自动钓鱼请求
     */
    public void handleFishStartAutoC2S(HumanObject humanObj, int groundSn,
                                       List<Define.p_key_value> baitSnNumList, int castNum) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        // 检查是否已在自动钓鱼
        if (homeFish.getAutoFishGroundSn() > 0) {
            Log.fish.warn("已在自动钓鱼中, humanId={}", humanObj.id);
            return;
        }

        // 验证参数
        if (!validateAutoFishParams(humanObj, groundSn, baitSnNumList, castNum)) {
            return;
        }
        List<List<Integer>> baitList = parseBaitSnNumList(baitSnNumList);
        FishData fishData = humanObj.operation.fishData;

        // 设置自动钓鱼状态
        homeFish.setAutoFishGroundSn(groundSn);
        homeFish.setAutoFishStartTime(Port.getTime());
        homeFish.setAutoFishBaitsSnNum(Utils.toJSONString(baitList));
        homeFish.setAutoFishCastNum(castNum);
        homeFish.setAutoFishSettleCount(0);

        // 同时更新FishData中的鱼饵数据
        fishData.setAutoFishBaitsSnNum(baitList);
        fishData.saveAutoFishBaitsSnNum();

        // 初始化自动钓鱼统计数据
        fishData.getTotalAlbumFishes().clear();
        fishData.getTotalSellFishes().clear();
        fishData.getTotalRewards().clear();
        homeFish.setTotalCastCnt(0);
        homeFish.setTotalReelSuccCnt(0);
        homeFish.setTotalSlippedCnt(0);

        // 清空统计数据
        homeFish.setTotalAlbumFishes("[]");
        homeFish.setTotalSellFishes("{}");
        homeFish.setTotalCastCnt(0);
        homeFish.setTotalReelSuccCnt(0);
        homeFish.setTotalSlippedCnt(0);
        homeFish.setTotalRewards("{}");

        // 响应客户端
        MsgHome.home_fish_start_auto_s2c.Builder builder = MsgHome.home_fish_start_auto_s2c.newBuilder();
        builder.setStartTime((int)(homeFish.getAutoFishStartTime() / Time.SEC) + 1);
        builder.setFishGround(groundSn);

        // 添加鱼饵信息到响应
        builder.addAllBaitSnNum(baitSnNumList);
        builder.setMultiple(castNum);

        humanObj.sendMsg(builder.build());

        Log.fish.debug("开始自动钓鱼, humanId={}, groundSn={}, castNum={}",
            humanObj.id, groundSn, castNum);
    }

    /**
     * 验证自动钓鱼参数
     */
    private boolean validateAutoFishParams(HumanObject humanObj, int groundSn,
                                           List<Define.p_key_value> baitSnNumList, int castNum) {
        //不是vip无法自动钓鱼
        int privilegeValue = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.Fishing_AutoUnlock,0);
        if(privilegeValue < 1){
            Log.fish.warn("不是vip，无法自动钓鱼, humanId={}", humanObj.id);
            return false;
        }

        // 检查抛竿次数
        if (castNum != 1 && castNum != 10) {
            Log.fish.warn("无效的自动钓鱼抛竿次数, humanId={}, castNum={}", humanObj.id, castNum);
            return false;
        }

        // 检查渔场配置
        ConfFishGround groundConfig = ConfFishGround.get(groundSn);
        if (groundConfig == null) {
            Log.fish.warn("渔场配置不存在, humanId={}, groundSn={}", humanObj.id, groundSn);
            return false;
        }

        // 检查鱼饵列表
        if (baitSnNumList == null || baitSnNumList.isEmpty()) {
            Log.fish.warn("鱼饵列表为空, humanId={}", humanObj.id);
            return false;
        }

        int totalBaitNum = 0;
        for (Define.p_key_value baitInfo : baitSnNumList) {
            int baitSn = (int)baitInfo.getK();
            int baitNum = (int)baitInfo.getV();
            if (baitNum <= 0) {
                Log.fish.warn("鱼饵数量为0, humanId={}, baitSn={}, baitNum={}",
                    humanObj.id, baitSn, baitNum);
                return false;
            }
            totalBaitNum += baitNum;
            ConfFishBait baitConfig = ConfFishBait.get(baitSn);
            if (baitConfig == null) {
                Log.fish.warn("鱼饵配置不存在, humanId={}, baitSn={}", humanObj.id, baitSn);
                return false;
            }
            int itemSn = baitConfig.item_id;
            int realBaitNum = ItemManager.inst().getItemNum(humanObj, itemSn);
            if (realBaitNum < baitNum) {
                Log.fish.warn("鱼饵数量不足, humanId={}, baitSn={}, baitNum={}, realBaitNum={}",
                    humanObj.id, baitSn, baitNum, realBaitNum);
                return false;
            }
        }
        if (castNum == 10 && totalBaitNum < 10) {
            Log.fish.warn("鱼饵数量不足, humanId={}, totalBaitNum={}", humanObj.id, totalBaitNum);
            return false;
        }
        return true;
    }

    public List<List<Integer>> parseBaitSnNumList(List<Define.p_key_value> baitSnNumList) {
        List<List<Integer>> result = new ArrayList<>();
        for (Define.p_key_value baitInfo : baitSnNumList) {
            int baitSn = (int)baitInfo.getK();
            int baitNum = (int)baitInfo.getV();
            result.add(Arrays.asList(baitSn, baitNum));
        }
        return result;
    }

    /**
     * 处理结束自动钓鱼请求
     */
    public void handleFishFinAutoC2S(HumanObject humanObj) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }

        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;

        // 检查是否在自动钓鱼
        if (homeFish.getAutoFishGroundSn() <= 0) {
            Log.fish.warn("未在自动钓鱼中, humanId={}", humanObj.id);
            return;
        }

        // 自动钓鱼结算
        handleFishAutoSettleC2S(humanObj);

        // 响应客户端，返回累积的统计数据
        sendAutoFishFinResponse(humanObj);

        clearAutoFishState(homeFish, fishData);

        Log.fish.debug("结束自动钓鱼, humanId={}", humanObj.id);
    }

    private static void clearAutoFishState(HomeFish homeFish, FishData fishData) {
        // 清空自动钓鱼状态
        homeFish.setAutoFishGroundSn(0);
        homeFish.setAutoFishStartTime(0);
        homeFish.setAutoFishBaitsSnNum("[]");
        homeFish.setAutoFishCastNum(0);
        homeFish.setAutoFishSettleCount(0);

        // 同时清空FishData中的鱼饵数据
        fishData.getAutoFishBaitsSnNum().clear();
        fishData.saveAutoFishBaitsSnNum();

        // 清空累积统计数据
        homeFish.setTotalAlbumFishes("[]");
        homeFish.setTotalSellFishes("{}");
        homeFish.setTotalCastCnt(0);
        homeFish.setTotalReelSuccCnt(0);
        homeFish.setTotalSlippedCnt(0);
        homeFish.setTotalRewards("{}");
    }

    /**
     * 发送自动钓鱼结束响应
     */
    private void sendAutoFishFinResponse(HumanObject humanObj) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        FishData fishData = humanObj.operation.fishData;

        MsgHome.home_fish_fin_auto_s2c.Builder builder = MsgHome.home_fish_fin_auto_s2c.newBuilder();

        // 更新图鉴的鱼
        Map<Integer, FishDetail> totalAlbumMap = fishData.getTotalAlbumFishes();
        for (Map.Entry<Integer, FishDetail> entry : totalAlbumMap.entrySet()) {
            int fishGroupSn = entry.getKey();
            FishDetail detail = entry.getValue();
            Define.p_home_fish_detail.Builder detailBuilder = Define.p_home_fish_detail.newBuilder();
            detailBuilder.setFishTypeSn(fishGroupSn);
            detailBuilder.setFishSn(detail.getFishSn());
            detailBuilder.setLen(detail.getMaxLen());
            detailBuilder.setLv(detail.getLevel());
            detailBuilder.setExp(detail.getExp());
            builder.addAlbumFishes(detailBuilder.build());
        }

        // 卖出的鱼排查掉更新图鉴的鱼
        Map<Integer, Integer> totalSellMap = fishData.getTotalSellFishes();
        for (FishDetail detail : totalAlbumMap.values()) {
            ConfFishBase baseConfig = ConfFishBase.get(detail.getFishSn());
            totalSellMap.put(baseConfig.fish_group_id, totalSellMap.getOrDefault(baseConfig.fish_group_id, 0) - 1);
        }

        for (Map.Entry<Integer, Integer> entry : totalSellMap.entrySet()) {
            int fishGroupSn = entry.getKey();
            int count = entry.getValue();
            if(count <= 0){
                continue;
            }
            Define.p_key_value.Builder kvBuilder = Define.p_key_value.newBuilder();
            kvBuilder.setK(fishGroupSn);
            kvBuilder.setV(count);
            builder.addSellFishes(kvBuilder.build());
        }

        // 统计数据
        builder.setCastCnt(homeFish.getTotalCastCnt());
        builder.setReelSuccCnt(homeFish.getTotalReelSuccCnt());
        builder.setSlippedCnt(homeFish.getTotalSlippedCnt());

        // 出售获得的奖励
        Map<Integer, Integer> totalRewardsMap = fishData.getTotalRewards();
        for (Map.Entry<Integer, Integer> entry : totalRewardsMap.entrySet()) {
            int currencyId = entry.getKey();
            int amount = entry.getValue();

            Define.p_reward.Builder rewardBuilder = Define.p_reward.newBuilder();
            rewardBuilder.setGtid(currencyId);
            rewardBuilder.setNum(amount);
            builder.addReward(rewardBuilder.build());
        }

        // 添加钓鱼佬等级和经验
        builder.setFisherLv(homeFish.getFisherLv());
        builder.setFisherExp(homeFish.getFisherExp());

        humanObj.sendMsg(builder.build());
    }

    /**
     * 发送鱼类详情更新响应（用于道具获得鱼类）
     */
    private void sendFishDetailUpdateResponse(HumanObject humanObj, Set<FishDetail> recordFishes) {
        MsgHome.home_fish_detail_update_s2c.Builder builder = MsgHome.home_fish_detail_update_s2c.newBuilder();

        // 只发送破纪录的鱼
        for (FishDetail detail : recordFishes) {
            ConfFishBase baseConfig = ConfFishBase.get(detail.getFishSn());
            if (baseConfig != null) {
                Define.p_home_fish_detail.Builder detailBuilder = Define.p_home_fish_detail.newBuilder();
                detailBuilder.setFishTypeSn(baseConfig.fish_group_id);
                detailBuilder.setFishSn(detail.getFishSn());
                detailBuilder.setLen(detail.getMaxLen());
                detailBuilder.setLv(detail.getLevel());
                detailBuilder.setExp(detail.getExp());
                builder.addAlbumFishes(detailBuilder.build());
            }
        }

        humanObj.sendMsg(builder.build());
    }

    /**
     * 处理自动钓鱼结算请求
     */
    public void handleFishAutoSettleC2S(HumanObject humanObj) {
        if (humanObj.operation.fishData == null || humanObj.operation.fishData.getHomeFish() == null) {
            Log.fish.error("钓鱼数据未初始化, humanId={}", humanObj.id);
            return;
        }
        FishData fishData = humanObj.operation.fishData;
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();

        // 检查是否在自动钓鱼
        if (homeFish.getAutoFishGroundSn() <= 0) {
            Log.fish.warn("未在自动钓鱼中, humanId={}", humanObj.id);
            return;
        }

        // 获取自动钓鱼间隔配置
        ConfFishConfig intervalConfig = ConfFishConfig.get(EFishType.CONF_AUTO_FISH_INTERVAL);
        int intervalSeconds = 10; // 默认10秒一轮
        if (intervalConfig != null && intervalConfig.parameter != null && intervalConfig.parameter.length > 0) {
            intervalSeconds = intervalConfig.parameter[0][0];
        }

        // 计算当前可结算的轮次
        long startTime = homeFish.getAutoFishStartTime();
        long currentTime = Port.getTime();
        long elapsedSeconds = (currentTime - startTime) / Time.SEC;

        int totalRounds = (int)(elapsedSeconds / intervalSeconds); // 总轮次
        int alreadySettledRounds = homeFish.getAutoFishSettleCount(); // 已结算轮次
        int newRounds = totalRounds - alreadySettledRounds; // 新的可结算轮次

        if (newRounds <= 0) {
            // 没有新的可结算轮次，返回下次结算时间
            int nextSettleTime = getNextSettleTime(homeFish, intervalSeconds, totalRounds);
            sendAutoSettleResponse(humanObj, homeFish.getTotalCastCnt(), getCurrentBaitSn(fishData), nextSettleTime, 0, 0);
            return;
        }

        // 执行多轮钓鱼结算（结算所有可结算的轮次，包括余数轮次）
        AutoFishResult totalResult = executeMultipleRoundsAutoFish(humanObj, newRounds);

        fishData.saveAlbumDetailMap();
        if(!totalResult.albumFishes.isEmpty()){
            updatePorpCalcPower(humanObj);
        }

        // 更新已结算轮次（实际结算的轮次可能少于请求的轮次，因为鱼饵可能不足）
        int actualSettledRounds = totalResult.actualRounds > 0 ? totalResult.actualRounds : newRounds;
        homeFish.setAutoFishSettleCount(alreadySettledRounds + actualSettledRounds);

        // 计算下次结算时间
        int nextSettleTime = getNextSettleTime(homeFish, intervalSeconds, totalRounds);

        //鱼饵消耗完了就结束自动钓鱼
        List<Integer> lastBaitInfo = fishData.getAutoFishBaitsSnNum().get(fishData.getAutoFishBaitsSnNum().size() - 1);
        if (totalResult.baitSn == 0 || (lastBaitInfo != null && lastBaitInfo.get(1) == 0)) {
            sendAutoFishFinResponse(humanObj);
            clearAutoFishState(homeFish, humanObj.operation.fishData);
        }else{
            // 响应客户端（返回总的钓鱼结果，鱼信息是最后一轮的第一条鱼）
            sendAutoSettleResponse(humanObj, homeFish.getTotalCastCnt(), totalResult.baitSn, nextSettleTime,
                    totalResult.firstFishSn, totalResult.firstFishLen);
        }
        Event.fire(EventKey.HOME_FISHING, "humanObj", humanObj, "castNum", totalResult.fishCount, "reelSuccCnt", totalResult.successCount, "quitNumMap", totalResult.quitNumMap, "gradeNumMap", totalResult.gradeNumMap, "typeNumMap", totalResult.typeNumMap);
        Log.fish.debug("自动钓鱼结算完成, humanId={}, rounds={}, totalFishCount={}, firstFish={}",
            humanObj.id, newRounds, totalResult.fishCount, totalResult.firstFishSn);
    }

    /**
     * 执行多轮自动钓鱼（性能优化版本）
     */
    private AutoFishResult executeMultipleRoundsAutoFish(HumanObject humanObj, int rounds) {
        FishData fishData = humanObj.operation.fishData;
        HomeFish homeFish = fishData.getHomeFish();

        // 第一步：预先收集所有轮次的鱼饵消耗信息（支持多种鱼饵混合）
        BaitConsumptionCollector baitCollector = new BaitConsumptionCollector();
        List<List<BaitConsumptionInfo>> allRoundBaitInfos = new ArrayList<>();

        for (int round = 0; round < rounds; round++) {
            List<BaitConsumptionInfo> roundBaitInfos = getCurrentBaitInfosForRound(fishData, round);
            if (roundBaitInfos.isEmpty()) {
                Log.fish.warn("鱼饵不足，预计可执行{}轮钓鱼, humanId={}", round, humanObj.id);
                break;
            }
            allRoundBaitInfos.add(roundBaitInfos);

            // 收集本轮所有鱼饵消耗
            for (BaitConsumptionInfo baitInfo : roundBaitInfos) {
                baitCollector.addConsumption(baitInfo.baitSn, baitInfo.actualConsumeCount);
            }
        }

        // 第二步：预先验证并批量扣除所有鱼饵
        if (!batchConsumeBaits(humanObj, baitCollector.getTotalConsumption())) {
            Log.fish.error("批量扣除鱼饵失败，自动钓鱼中断, humanId={}", humanObj.id);
            return new AutoFishResult(); // 返回空结果
        }

        // 第三步：执行钓鱼逻辑（无需再扣除鱼饵）
        AutoFishResult totalResult = new AutoFishResult();
        totalResult.fishCount = 0;
        totalResult.successCount = 0;
        totalResult.slippedCount = 0;
        totalResult.actualRounds = allRoundBaitInfos.size(); // 记录实际结算的轮次数

        for (int round = 0; round < allRoundBaitInfos.size(); round++) {
            List<BaitConsumptionInfo> roundBaitInfos = allRoundBaitInfos.get(round);

            // 执行一轮钓鱼（支持多种鱼饵混合）
            AutoFishResult roundResult = executeOneRoundWithMultipleBaits(humanObj, roundBaitInfos);

            // 累积结果
            mergeAutoFishResults(totalResult, roundResult, roundBaitInfos.get(0).baitSn);
        }

        // 第四步：批量更新自动钓鱼鱼饵列表
        batchUpdateAutoFishBaitList(fishData, baitCollector.getRoundConsumptions());

        // 更新钓鱼统计数据
        homeFish.setTotalCastCnt(homeFish.getTotalCastCnt() + totalResult.fishCount);
        homeFish.setTotalReelSuccCnt(homeFish.getTotalReelSuccCnt() + totalResult.successCount);
        homeFish.setTotalSlippedCnt(homeFish.getTotalSlippedCnt() + totalResult.slippedCount);
        fishData.saveTotalAlbumFishes();


        // 第五步：批量发放奖励并更新统计
        if (!totalResult.rewards.isEmpty()) {
            // vip奖励加成
            int privilegeValue = PrivilegeManager.inst().getPrivilegeEffectValue(humanObj, PrivilegeType.Fishing_RewardBonus,0);
            if(privilegeValue > 0){
                for (Map.Entry<Integer, Long> entry : totalResult.rewards.entrySet()) {
                    if(entry.getKey() == EFishType.FISHING_COIN){
                        entry.setValue(entry.getValue() * (100 + privilegeValue) / 100);
                    }
                }
            }
            batchProduceRewards(humanObj, totalResult.rewards);

            // 更新总奖励统计
            Map<Integer, Integer> totalRewardsMap = fishData.getTotalRewards();
            for (Map.Entry<Integer, Long> entry : totalResult.rewards.entrySet()) {
                totalRewardsMap.put(entry.getKey(),
                    totalRewardsMap.getOrDefault(entry.getKey(), 0) + entry.getValue().intValue());
            }
        }
        homeFish.setTotalRewards(Utils.mapIntIntToJSON(fishData.getTotalRewards()));
        // 第六步：更新总卖出统计
        if (!totalResult.sellFishes.isEmpty()) {
            Map<Integer, Integer> totalSellMap = fishData.getTotalSellFishes();
            for (Map.Entry<Integer, Integer> entry : totalResult.sellFishes.entrySet()) {
                totalSellMap.put(entry.getKey(),
                    totalSellMap.getOrDefault(entry.getKey(), 0) + entry.getValue());
            }
        }
        homeFish.setTotalSellFishes(Utils.mapIntIntToJSON(fishData.getTotalSellFishes()));

        return totalResult;
    }

    /**
     * 批量扣除鱼饵（性能优化）
     */
    private boolean batchConsumeBaits(HumanObject humanObj, Map<Integer, Integer> totalConsumption) {
        if (totalConsumption.isEmpty()) {
            return true;
        }

        // 收集所有需要扣除的道具
        Map<Integer, Integer> itemConsumption = new HashMap<>();

        // 预先验证所有鱼饵是否充足，并转换为道具消耗
        for (Map.Entry<Integer, Integer> entry : totalConsumption.entrySet()) {
            int baitSn = entry.getKey();
            int totalNeed = entry.getValue();

            ConfFishBait baitConfig = ConfFishBait.get(baitSn);
            if (baitConfig == null) {
                Log.fish.error("鱼饵配置不存在, humanId={}, baitSn={}", humanObj.id, baitSn);
                return false;
            }

            // 计算实际消耗数量（考虑无损鱼饵概率）
            int actualConsumeNum = calculateActualBaitConsumption(baitConfig, totalNeed);

            int itemSn = baitConfig.item_id;
            int currentNum = ItemManager.inst().getItemNum(humanObj, itemSn);
            if (currentNum < actualConsumeNum) {
                Log.fish.warn("鱼饵数量不足, humanId={}, baitSn={}, need={}, have={}",
                    humanObj.id, baitSn, actualConsumeNum, currentNum);
                return false;
            }

            // 累积道具消耗（同一种道具可能对应多种鱼饵）
            itemConsumption.put(itemSn, itemConsumption.getOrDefault(itemSn, 0) + actualConsumeNum);
        }

        // 批量扣除道具（一次性调用）
        int[][] costMap = new int[itemConsumption.size()][2];
        int i = 0;
        for (Map.Entry<Integer, Integer> entry : itemConsumption.entrySet()) {
            costMap[i][0] = entry.getKey();
            costMap[i][1] = entry.getValue();
            i++;
        }
        ReasonResult result = ProduceManager.inst().checkAndCostItem(humanObj, costMap, MoneyItemLogKey.钓鱼自动收竿);

        if (!result.success) {
            Log.fish.error("批量扣除鱼饵失败, humanId={}, reason={}", humanObj.id, result.reason);
            return false;
        }

        Log.fish.debug("批量扣除鱼饵成功, humanId={}, baitTypes={}, itemTypes={}",
            humanObj.id, totalConsumption.size(), itemConsumption.size());
        return true;
    }

    /**
     * 计算实际鱼饵消耗数量（考虑无损鱼饵概率）
     */
    private int calculateActualBaitConsumption(ConfFishBait baitConfig, int baitNum) {
        int actualConsumeNum = baitNum;

        // 处理无损鱼饵
        if (baitConfig.sn == EFishType.BAIT_LOSSLESS &&
                baitConfig.parameter != null && baitConfig.parameter.length > 0) {

            int probability = baitConfig.parameter[0];
            for (int i = 0; i < baitNum; i++) {
                if (Utils.random(10000) <= probability) {
                    actualConsumeNum--; // 概率不消耗
                }
            }
            actualConsumeNum = Math.max(0, actualConsumeNum); // 确保不小于0
        }

        return actualConsumeNum;
    }

    /**
     * 合并自动钓鱼结果（性能优化）
     */
    private void mergeAutoFishResults(AutoFishResult totalResult, AutoFishResult roundResult, int baitSn) {
        // 累积基础数据
        totalResult.fishCount += roundResult.fishCount;
        totalResult.successCount += roundResult.successCount;
        totalResult.slippedCount += roundResult.slippedCount;
        totalResult.baitSn = baitSn; // 记录最后使用的鱼饵

        // 记录最后一轮的第一条鱼信息（用于展示）
        if (roundResult.firstFishSn > 0) {
            totalResult.firstFishSn = roundResult.firstFishSn;
            totalResult.firstFishLen = roundResult.firstFishLen;
        }

        // 合并图鉴更新
        totalResult.albumFishes.putAll(roundResult.albumFishes);

        // 合并卖出统计
        for (Map.Entry<Integer, Integer> entry : roundResult.sellFishes.entrySet()) {
            totalResult.sellFishes.merge(entry.getKey(), entry.getValue(), Integer::sum);
        }

        // 合并奖励
        for (Map.Entry<Integer, Long> entry : roundResult.rewards.entrySet()) {
            totalResult.rewards.merge(entry.getKey(), entry.getValue(), Long::sum);
        }

        totalResult.quitNumMap.putAll(roundResult.quitNumMap);
        totalResult.gradeNumMap.putAll(roundResult.gradeNumMap);
        totalResult.typeNumMap.putAll(roundResult.typeNumMap);
    }

    /**
     * 批量更新自动钓鱼鱼饵列表（性能优化）
     */
    private void batchUpdateAutoFishBaitList(FishData fishData, List<BaitConsumptionInfo> consumptions) {
        if (consumptions.isEmpty()) {
            return;
        }
        HomeFish homeFish = fishData.getHomeFish();
        // 一次性获取当前鱼饵列表
        List<List<Integer>> baitSnNumList = fishData.getAutoFishBaitsSnNum();

        if (baitSnNumList == null || baitSnNumList.isEmpty()) {
            return;
        }

        // 批量更新鱼饵数量
        for (BaitConsumptionInfo consumption : consumptions) {
            for (List<Integer> baitInfo : baitSnNumList) {
                if (baitInfo.size() >= 2 && baitInfo.get(0).equals(consumption.baitSn)) {
                    int currentCount = baitInfo.get(1);
                    int newCount = Math.max(0, currentCount - consumption.actualConsumeCount);
                    baitInfo.set(1, newCount);
                    break;
                }
            }
        }

        // 一次性更新到数据库
        homeFish.setAutoFishBaitsSnNum(JSON.toJSONString(baitSnNumList));

        Log.fish.debug("批量更新自动钓鱼鱼饵列表, humanId={}, consumptions={}",
            homeFish.getId(), consumptions.size());
    }

    /**
     * 批量发放奖励（性能优化）
     */
    private void batchProduceRewards(HumanObject humanObj, Map<Integer, Long> rewards) {
        if (rewards.isEmpty()) {
            return;
        }

        Map<Integer, Integer> rewardMap = new HashMap<>();
        for (Map.Entry<Integer, Long> entry : rewards.entrySet()) {
            rewardMap.put(entry.getKey(), entry.getValue().intValue());
        }

        ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.钓鱼收竿);

        Log.fish.debug("批量发放自动钓鱼奖励, humanId={}, rewardTypes={}",
            humanObj.id, rewardMap.size());
    }

    /**
     * 执行一轮自动钓鱼（支持多种鱼饵混合）
     */
    private AutoFishResult executeOneRoundWithMultipleBaits(HumanObject humanObj, List<BaitConsumptionInfo> baitInfos) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        int groundSn = homeFish.getAutoFishGroundSn();

        // 计算总钓鱼次数
        int totalFishCount = 0;
        for (BaitConsumptionInfo baitInfo : baitInfos) {
            totalFishCount += baitInfo.actualConsumeCount;
        }

        AutoFishResult result = new AutoFishResult(totalFishCount, baitInfos.get(0).baitSn);

        // 按鱼饵顺序执行钓鱼
        for (BaitConsumptionInfo baitInfo : baitInfos) {
            int baitSn = baitInfo.baitSn;
            int fishCount = baitInfo.actualConsumeCount;

            Log.fish.debug("使用鱼饵钓鱼: baitSn={}, count={}, humanId={}", baitSn, fishCount, humanObj.id);

            // 使用当前鱼饵执行钓鱼
            for (int i = 0; i < fishCount; i++) {
                // 生成鱼类
                int fishSn = executeFishing(humanObj, groundSn, baitSn);
                if (fishSn <= 0) continue;

                ConfFishBase fishBase = ConfFishBase.get(fishSn);
                if (fishBase == null) continue;

                // 记录第一条鱼
                if (result.firstFishSn == 0) {
                    result.firstFishSn = fishSn;
                    result.firstFishLen = Utils.random(fishBase.len_range[0], fishBase.len_range[1]);
                }

                // 逃跑判定
                if (calculateEscapeRate(humanObj, fishBase)) {
                    result.slippedCount++;
                    continue;
                }

                result.successCount++;

                // 处理奖励
                processAutoFishRewards(humanObj, fishBase, result);

                // 更新图鉴（使用与手动钓鱼相同的逻辑）
                int randomLen = Utils.random(fishBase.len_range[0], fishBase.len_range[1]);
                FishDetail updatedFish = updateAlbumData(humanObj, fishBase, randomLen);
                if (updatedFish != null) {
                    result.albumFishes.put(fishBase.fish_group_id, updatedFish);

                    // 只有破纪录的鱼才更新到totalAlbumFishes用于最终统计
                    if (updatedFish.isRecord()) {
                        FishData fishData = humanObj.operation.fishData;
                        Map<Integer, FishDetail> totalAlbumMap = fishData.getTotalAlbumFishes();
                        totalAlbumMap.put(fishBase.fish_group_id, updatedFish);
                    }
                }

                // 统计卖出
                int fishGroupSn = fishBase.fish_group_id;
                ConfFishGroup groupConfig = ConfFishGroup.get(fishGroupSn);
                result.sellFishes.put(fishGroupSn, result.sellFishes.getOrDefault(fishGroupSn, 0) + 1);
                result.quitNumMap.merge(groupConfig.quality, 1, Integer::sum);
                result.gradeNumMap.merge(fishBase.grade, 1, Integer::sum);
                result.typeNumMap.merge(groupConfig.type, 1, Integer::sum);
            }
        }

        return result;
    }

    public void giveFish(HumanObject humanObj, List<ProduceVo> voList) {
        FishData fishData = humanObj.operation.fishData;
        Set<FishDetail> changeFishDetailSet = new HashSet<>();
        boolean hasRecord = false;

        for (ProduceVo vo : voList) {
            int itemSn = vo.itemSn;
            int itemNum = vo.num;
            ConfGoods confGoods = ConfGoods.get(itemSn);
            if (confGoods == null || confGoods.effect == null || confGoods.effect.length == 0) {
                Log.fish.error("道具配置不存在或effect为空, humanId={}, itemSn={}", humanObj.id, itemSn);
                continue;
            }
            int fishGroupSn = confGoods.effect[0][0];
            for (int i = 0; i < itemNum; i++) {
                //直接拿权重随机鱼，没有渔具鱼饵影响
                int fishSn = randomFishGrade(humanObj, fishGroupSn, null);
                ConfFishBase fishBase = ConfFishBase.get(fishSn);
                if (fishBase == null) {
                    Log.fish.error("===鱼类基础配置不存在, humanId={}, fishSn={}", humanObj.id, fishSn);
                    continue;
                }
                int randomLen = Utils.random(fishBase.len_range[0], fishBase.len_range[1]);
                FishDetail updatedFish = updateAlbumData(humanObj, fishBase, randomLen);

                if (updatedFish != null) {
                    changeFishDetailSet.add(updatedFish);
                    if (updatedFish.isRecord()) {
                        hasRecord = true;
                    }
                }
            }
        }

        sendFishDetailUpdateResponse(humanObj, changeFishDetailSet);
        fishData.saveAlbumDetailMap();
        // 如果有破纪录的鱼，发送更新消息并保存数据
        if (hasRecord) {
            updatePorpCalcPower(humanObj);
        }
    }

    /**
     * 鱼饵消耗信息
     */
    static class BaitConsumptionInfo {
        public int baitSn;
        public int actualConsumeCount;

        public BaitConsumptionInfo(int baitSn, int actualConsumeCount) {
            this.baitSn = baitSn;
            this.actualConsumeCount = actualConsumeCount;
        }

        @Override
        public String toString() {
            return "BaitConsumptionInfo{baitSn=" + baitSn + ", actualConsumeCount=" + actualConsumeCount + "}";
        }
    }

    /**
     * 鱼饵消耗收集器 - 用于批量处理鱼饵扣除
     */
    static class BaitConsumptionCollector {
        // 收集需要扣除的鱼饵 Map<鱼饵SN, 总消耗数量>
        private Map<Integer, Integer> totalBaitConsumption = new HashMap<>();
        // 记录每轮的消耗信息，用于更新自动钓鱼列表
        private List<BaitConsumptionInfo> roundConsumptions = new ArrayList<>();

        public void addConsumption(int baitSn, int count) {
            totalBaitConsumption.put(baitSn, totalBaitConsumption.getOrDefault(baitSn, 0) + count);
            roundConsumptions.add(new BaitConsumptionInfo(baitSn, count));
        }

        public Map<Integer, Integer> getTotalConsumption() {
            return totalBaitConsumption;
        }

        public List<BaitConsumptionInfo> getRoundConsumptions() {
            return roundConsumptions;
        }

        public boolean isEmpty() {
            return totalBaitConsumption.isEmpty();
        }
    }

    /**
     * 获取当前使用的鱼饵SN（兼容旧方法）
     */
    private int getCurrentBaitSn(FishData fishData) {
        return getCurrentBaitSnForRound(fishData, 0);
    }

    /**
     * 获取指定轮次的鱼饵消耗信息（支持多种鱼饵混合，正确处理顺序消耗）
     */
    private List<BaitConsumptionInfo> getCurrentBaitInfosForRound(FishData fishData, int roundOffset) {
        HomeFish homeFish = fishData.getHomeFish();
        List<List<Integer>> baitSnNumList = fishData.getAutoFishBaitsSnNum();
        if (baitSnNumList == null || baitSnNumList.isEmpty()) {
            List<BaitConsumptionInfo> result = new ArrayList<>();
            return result;
        }

        int multiple = homeFish.getAutoFishCastNum(); // 1钓或10钓

        // 计算到当前轮次为止，总共需要消耗的鱼饵数量
        int totalNeededUpToThisRound = (roundOffset + 1) * multiple;

        // 计算到上一轮次为止，已经消耗的鱼饵数量
        int totalConsumedUpToPrevRound = roundOffset * multiple;

        // 本轮需要的鱼饵数量
        int needThisRound = totalNeededUpToThisRound - totalConsumedUpToPrevRound;

        List<BaitConsumptionInfo> roundBaits = new ArrayList<>();
        int totalAvailable = 0;

        // 计算总的可用鱼饵数量
        for (List<Integer> baitInfo : baitSnNumList) {
            if (baitInfo.size() >= 2) {
                totalAvailable += baitInfo.get(1);
            }
        }

        // 如果总的可用鱼饵不足以到达当前轮次，返回空
        if (totalAvailable < totalConsumedUpToPrevRound) {
            return new ArrayList<>();
        }

        // 计算本轮实际能消耗的鱼饵数量（可能少于needThisRound，这就是余数情况）
        int actualNeedThisRound = Math.min(needThisRound, totalAvailable - totalConsumedUpToPrevRound);
        if (actualNeedThisRound <= 0) {
            return new ArrayList<>();
        }

        // 按顺序分配鱼饵
        int allocated = 0;
        int consumedSoFar = 0;

        for (List<Integer> baitInfo : baitSnNumList) {
            if (baitInfo.size() < 2 || allocated >= actualNeedThisRound) break;

            int baitSn = baitInfo.get(0);
            int baitCount = baitInfo.get(1);

            // 检查这个鱼饵是否在当前轮次的消耗范围内
            if (consumedSoFar + baitCount > totalConsumedUpToPrevRound) {
                // 计算这个鱼饵在当前轮次可以提供多少
                int availableFromThisBait = Math.min(
                    baitCount - Math.max(0, totalConsumedUpToPrevRound - consumedSoFar), // 这个鱼饵的剩余数量
                    actualNeedThisRound - allocated // 当前轮次还需要的数量
                );

                if (availableFromThisBait > 0) {
                    roundBaits.add(new BaitConsumptionInfo(baitSn, availableFromThisBait));
                    allocated += availableFromThisBait;
                }
            }

            consumedSoFar += baitCount;
        }

        return roundBaits;
    }

    /**
     * 获取指定轮次使用的鱼饵SN
     */
    private int getCurrentBaitSnForRound(FishData fishData, int roundOffset) {
        HomeFish homeFish = fishData.getHomeFish();
        List<List<Integer>> baitSnNumList = fishData.getAutoFishBaitsSnNum();
        if (baitSnNumList == null || baitSnNumList.isEmpty()) {
            return 1; // 默认鱼饵
        }

        int multiple = homeFish.getAutoFishCastNum(); // 1钓或10钓
        int alreadySettledRounds = homeFish.getAutoFishSettleCount();
        int currentRound = alreadySettledRounds + roundOffset;

        // 计算已消耗的鱼饵总数
        int totalConsumed = currentRound * multiple;

        // 按顺序查找可用的鱼饵
        int consumed = 0;
        for (List<Integer> baitInfo : baitSnNumList) {
            if (baitInfo.size() < 2) continue;

            int baitSn = baitInfo.get(0);
            int baitCount = baitInfo.get(1);

            if (consumed + baitCount > totalConsumed) {
                // 这个鱼饵还有剩余，可以使用
                int remaining = consumed + baitCount - totalConsumed;
                if (remaining >= multiple) {
                    return baitSn; // 剩余数量足够本轮使用
                } else if (remaining > 0) {
                    // 余数鱼饵也要钓完
                    return baitSn;
                }
            }
            consumed += baitCount;
        }

        return 0; // 鱼饵不足
    }

    /**
     * 处理自动钓鱼奖励
     */
    private void processAutoFishRewards(HumanObject humanObj, ConfFishBase fishBase, AutoFishResult result) {
        ConfFishGroup groupConfig = ConfFishGroup.get(fishBase.fish_group_id);
        if (groupConfig == null) return;

        // 发放货币奖励
        if (groupConfig.sell != null && groupConfig.sell.length >= 2) {
            int currencyId = groupConfig.sell[0];
            long amount = groupConfig.sell[1];
            result.rewards.put(currencyId, result.rewards.getOrDefault(currencyId, 0L) + amount);
        }

        // 发放钓鱼经验（与手动钓鱼保持一致）
        if (fishBase.exp > 0) {
            addFisherExp(humanObj, fishBase.exp);
        }
    }


    /**
     * 获取下次结算时间
     */
    private int getNextSettleTime(HomeFish homeFish, int intervalSeconds, int totalRounds) {
        long startTime = homeFish.getAutoFishStartTime();

        // 下一轮的结算时间
        long nextSettleTime = startTime + ((totalRounds + 1) * intervalSeconds * Time.SEC);

        return (int)(nextSettleTime / Time.SEC);
    }

    /**
     * 发送自动钓鱼结算响应
     */
    private void sendAutoSettleResponse(HumanObject humanObj, int fishCount, int baitSn,
                                      int settleTime, int firstFishSn, int firstFishLen) {

        MsgHome.home_fish_auto_settle_s2c.Builder builder = MsgHome.home_fish_auto_settle_s2c.newBuilder();

        builder.setFishCount(fishCount);
        builder.setBaitSn(baitSn);
        builder.setSettleTime(settleTime);
        builder.setFirstFishSn(firstFishSn);
        builder.setLen(firstFishLen);

        humanObj.sendMsg(builder.build());
    }

    public void init(HumanObject humanObj, HomeFish homeFish) {
        if (homeFish == null) {
            return;
        }
        humanObj.operation.fishData = new FishData();
        humanObj.operation.fishData.setHomeFish(homeFish);

    }

    public void getFish(HumanObject humanObj, List<Integer> fishBaseSnList, int num) {
        HomeFish homeFish = humanObj.operation.fishData.getHomeFish();
        if (humanObj.operation.fishData == null || homeFish == null) {
            return;
        }
        FishData fishData = humanObj.operation.fishData;
        for (int fishSn : fishBaseSnList) {
            ConfFishBase fishBase = ConfFishBase.get(fishSn);
            if (fishBase == null) {
                continue;
            }
            ConfFishGroup fishGroup = ConfFishGroup.get(fishBase.fish_group_id);
            if (fishGroup == null) {
                return;
            }
            Map<Integer, FishDetail> albumDetailMap = fishData.getAlbumDetailMap();
            FishDetail fishDetail = albumDetailMap.get(fishGroup.sn);
            if (fishDetail == null) {
                int randomLen = Utils.random(fishBase.len_range[0], fishBase.len_range[1]);
                fishDetail = new FishDetail(fishBase.sn, randomLen, 0, 0);
                albumDetailMap.put(fishGroup.sn, fishDetail);
            }
            int len = Utils.random(fishBase.len_range[0], fishBase.len_range[1]);
            if (len > fishDetail.getMaxLen()) {
                fishDetail.setMaxLen(len);
                fishDetail.setFishSn(fishBase.sn);
            }
            fishDetail.setExp(fishDetail.getExp() + num);
        }

        fishData.saveAlbumDetailMap();
        handleFishDataC2S(humanObj);
    }
}
