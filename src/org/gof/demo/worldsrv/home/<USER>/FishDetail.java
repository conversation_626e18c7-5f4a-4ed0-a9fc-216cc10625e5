package org.gof.demo.worldsrv.home.Fish;


import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.Utils;

import java.util.ArrayList;
import java.util.List;

/**
 * 鱼类详情数据结构
 * 对应需求中的 FishDetail {fishSn, len, level, exp}
 */
public class FishDetail {
    /** 鱼类基础表的sn */
    private int fishSn;
    
    /** 最大长度(厘米) */
    private int maxLen;
    
    /** 鱼类等级 */
    private int level;
    
    /** 鱼类经验 */
    private int exp;
    /** 是否破记录 - 临时标记，不持久化存储 */
    private boolean isRecord;
    
    public FishDetail() {
    }
    
    public FishDetail(int fishSn, int maxLen, int level, int exp) {
        this.fishSn = fishSn;
        this.maxLen = maxLen;
        this.level = level;
        this.exp = exp;
    }
    
    // ===== Getter/Setter =====
    public int getFishSn() {
        return fishSn;
    }
    
    public void setFishSn(int fishSn) {
        this.fishSn = fishSn;
    }
    
    public int getMaxLen() {
        return maxLen;
    }
    
    public void setMaxLen(int maxLen) {
        this.maxLen = maxLen;
    }
    
    public int getLevel() {
        return level;
    }
    
    public void setLevel(int level) {
        this.level = level;
    }
    
    public int getExp() {
        return exp;
    }
    
    public void setExp(int exp) {
        this.exp = exp;
    }

    public boolean isRecord() {
        return isRecord;
    }

    public void setRecord(boolean record) {
        isRecord = record;
    }

    public JSONObject toJSON() {
        JSONObject json = new JSONObject();
        List<Integer> fields = new ArrayList<>();
        fields.add(maxLen);
        fields.add(level);
        fields.add(exp);
        json.put(Integer.toString(fishSn), Utils.listToString(fields));
        return json;
    }

    public void fromJSON(JSONObject json) {
        String key = json.keySet().iterator().next();
        fishSn = Integer.parseInt(key);
        List<Integer> fields = Utils.strToIntList(json.getString(key));
        maxLen = fields.get(0);
        level = fields.get(1);
        exp = fields.get(2);
    }

    public String toJSONString() {
        return toJSON().toJSONString();
    }
    
    @Override
    public String toString() {
        return "FishDetail{" +
                "fishSn=" + fishSn +
                ", maxLen=" + maxLen +
                ", level=" + level +
                ", exp=" + exp +
                '}';
    }
}
