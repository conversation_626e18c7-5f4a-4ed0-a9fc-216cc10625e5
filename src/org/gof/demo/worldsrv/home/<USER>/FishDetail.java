package org.gof.demo.worldsrv.home.Fish;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 鱼类详情数据结构
 * 对应需求中的 FishDetail {fishSn, len, level, exp}
 */
public class FishDetail {
    /** 鱼类基础表的sn */
    @JSONField(name = "sn")
    private int fishSn;
    
    /** 最大长度(厘米) */
    @JSONField(name = "len")
    private int maxLen;
    
    /** 鱼类等级 */
    @JSONField(name = "lv")
    private int level;
    
    /** 鱼类经验 */
    @JSONField(name = "exp")
    private int exp;
    /** 是否破记录 */
    private boolean isRecord;
    
    public FishDetail() {
    }
    
    public FishDetail(int fishSn, int maxLen, int level, int exp) {
        this.fishSn = fishSn;
        this.maxLen = maxLen;
        this.level = level;
        this.exp = exp;
    }
    
    // ===== Getter/Setter =====
    public int getFishSn() {
        return fishSn;
    }
    
    public void setFishSn(int fishSn) {
        this.fishSn = fishSn;
    }
    
    public int getMaxLen() {
        return maxLen;
    }
    
    public void setMaxLen(int maxLen) {
        this.maxLen = maxLen;
    }
    
    public int getLevel() {
        return level;
    }
    
    public void setLevel(int level) {
        this.level = level;
    }
    
    public int getExp() {
        return exp;
    }
    
    public void setExp(int exp) {
        this.exp = exp;
    }

    public boolean isRecord() {
        return isRecord;
    }

    public void setRecord(boolean record) {
        isRecord = record;
    }
    
    @Override
    public String toString() {
        return "FishDetail{" +
                "fishSn=" + fishSn +
                ", maxLen=" + maxLen +
                ", level=" + level +
                ", exp=" + exp +
                '}';
    }
}
