package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.support.Param;
import org.gof.demo.worldsrv.activity.ActivityProgressSourceModule;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlAccumulateScoreData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.util.List;

/**
 * 累加积分活动
 */
public class ActivityControlAccumulateScore extends AbstractActivityControl {
    public ActivityControlAccumulateScore(int type) {
        super(type);
    }

    @Override
    public void handleEvent(HumanObject humanObj, int event, Param param) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        if (event == EventKey.ACTIVITY_ADD_PROGRESS) {
            int moduleValue = param.get("module");
            ActivityProgressSourceModule module = ActivityProgressSourceModule.parseValue(moduleValue);
            switch (module) {
                case Fish: {
                    // 钓鱼引起的活动积分增加
                    int addScore = param.get("num");
                    ControlAccumulateScoreData controlData = (ControlAccumulateScoreData) data.getControlData();
                    controlData.score += addScore;
                    data.updateControlData();
                    // 任务的积分不要加，因为事件调用处已经处理活动任务
                    addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2020, addScore, true, new Object[] { 0 });
                    on_act_accumulate_score_info_c2s(humanObj);
                }
                break;
                default:
                    break;
            }

        }
    }

    public void on_act_accumulate_score_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ControlAccumulateScoreData controlData = (ControlAccumulateScoreData) data.getControlData();
        MsgAct.act_accumulate_score_info_s2c.Builder msg = MsgAct.act_accumulate_score_info_s2c.newBuilder();
        msg.setActType(type);
        msg.setScore(controlData.score);
        humanObj.sendMsg(msg.build());
    }

}
