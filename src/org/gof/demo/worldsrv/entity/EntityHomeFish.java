package org.gof.demo.worldsrv.entity;

import org.gof.core.gen.entity.Column;
import org.gof.core.gen.entity.Entity;

@Entity(entityName = "HomeFish", tableName = "home_fish")
public enum EntityHomeFish {

    // ===== 核心进度与等级 =====
    @Column(type = int.class, comment = "钓鱼佬等级", defaults = "1")
    fisherLv,
    @Column(type = int.class, comment = "钓鱼佬经验", defaults = "0")
    fisherExp,
    @Column(type = int.class, comment = "图鉴等级", defaults = "0")
    albumLv,
    @Column(type = int.class, comment = "图鉴经验 (所有鱼类等级之和)", defaults = "0")
    albumExp,
    @Column(type = int.class, comment = "图鉴总分", defaults = "0")
    totalScore,

    // ===== 核心养成数据 =====
    @Column(type = String.class, comment = "图鉴详情JSONArray K:fishGroupSn, V:fishSn,len,lv,exp的String", length = 2048, defaults = "[]")
    albumDetailMap,
    @Column(type = String.class, comment = "渔具类型等级 K:toolType, V:level 的JSON", length = 256, defaults = "{}")
    fishTools,
    @Column(type = String.class, comment = "鱼塘槽位 K:locationId, V:fishGroupSn 的JSON", length = 256, defaults = "{}")
    houseList,
    @Column(type = String.class, comment = "已领取的图鉴评分奖励ID列表", length = 512, defaults = "")
    albumScoreRewards,
    @Column(type = String.class, comment = "鱼塘装饰", length = 2048, defaults = "{}")
    houseDesign,
    @Column(type = int.class, comment = "当前鱼塘装饰", defaults = "0")
    currentHouseDesign,

    // ===== 任务与状态 =====
    @Column(type = int.class, comment = "当前每日任务组SN", defaults = "0")
    dailyTaskGroupSn,
    @Column(type = String.class, comment = "每日任务", length = 1024, defaults = "{}")
    dailyTasks,
    @Column(type = boolean.class, comment = "是否已领取每日任务最终奖励", defaults = "false")
    dailyTaskRecv,
    @Column(type = int.class, comment = "已解锁的最高常驻渔场ID", defaults = "1")
    unlockedMaxGround,
    @Column(type = String.class, comment = "渔场解锁任务", length = 1024, defaults = "{}")
    fishGroundTasks,
    @Column(type = int.class, comment = "总钓鱼次数", defaults = "0")
    totalFishCount,
    @Column(type = int.class, comment = "每日上钩次数", defaults = "0")
    dailyReelSuccCount,

    // ===== 自动钓鱼状态 (持久化) =====
    @Column(type = int.class, comment = "自动钓鱼所在的渔场SN (0代表未开启)", defaults = "0")
    autoFishGroundSn,
    @Column(type = long.class, comment = "自动钓鱼开始时间戳", defaults = "0")
    autoFishStartTime,
    @Column(type = String.class, comment = "自动钓鱼选择的鱼饵和数量列表 [[baitSn, num], ...] 的JSON", length = 1024, defaults = "[]")
    autoFishBaitsSnNum,
    @Column(type = int.class, comment = "自动钓鱼每次几钓 (1或10)", defaults = "0")
    autoFishCastNum,
    @Column(type = int.class, comment = "自动钓鱼已经结算的总次数", defaults = "0")
    autoFishSettleCount,

    // ===== 自动钓鱼会话统计 (持久化, 用于断线重连和最终结算) =====
    @Column(type = String.class, comment = "会话中破纪录的鱼JSONArray K:fishGroupSn, V:fishSn,len,lv,exp的String", length = 4096, defaults = "[]")
    totalAlbumFishes,
    @Column(type = String.class, comment = "会话中卖出的鱼 K:fishGroupSn, V:count 的JSON", length = 2048, defaults = "{}")
    totalSellFishes,
    @Column(type = int.class, comment = "会话中总抛竿次数", defaults = "0")
    totalCastCnt,
    @Column(type = int.class, comment = "会话中总上钩次数", defaults = "0")
    totalReelSuccCnt,
    @Column(type = int.class, comment = "会话中总逃跑次数", defaults = "0")
    totalSlippedCnt,
    @Column(type = String.class, comment = "会话中获得的总奖励 K:currencyId, V:amount 的JSON", length = 1024, defaults = "{}")
    totalRewards,
    @Column(type = String.class, comment = "扩展属性(用于处理bug)", length = 200, defaults = "")
    extendJSON,
}
