package org.gof.util;
import java.awt.*;
import java.awt.event.InputEvent;
import java.util.Random;

public class utilClick {
    private static final int MIN_DELAY = 100; // 最小延迟(毫秒)
    private static final int MAX_DELAY = 500; // 最大延迟(毫秒)
    private static volatile boolean running = false; // 运行状态标志

    public static void main(String[] args) {
        System.out.println("屏幕连点器 - 按Enter键开始/停止");

        // 创建机器人对象用于模拟输入
        Robot robot;
        try {
            robot = new Robot();
        } catch (AWTException e) {

            return;
        }

        // 随机数生成器
        Random random = new Random();
        // 监听Enter键
        new Thread(() -> {
            try {
                System.in.read();
                running = !running;
                System.out.println(running ? "连点器已启动" : "连点器已停止");

                // 如果再次按Enter可以退出程序
                if (!running) {
                    System.in.read();
                    System.exit(0);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }).start();

        // 主点击循环
        while (true) {
            if (running) {
                try {
                    // 模拟鼠标左键按下和释放
                    robot.mousePress(InputEvent.BUTTON1_DOWN_MASK);
                    robot.mouseRelease(InputEvent.BUTTON1_DOWN_MASK);

                    // 生成随机延迟并等待
                    int delay = MIN_DELAY + random.nextInt(MAX_DELAY - MIN_DELAY + 1);
                    Thread.sleep(delay);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } else {
                try {
                    // 不运行时降低CPU占用
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
